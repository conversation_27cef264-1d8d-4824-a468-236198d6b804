# https://docs.github.com/en/code-security/dependabot/working-with-dependabot/keeping-your-actions-up-to-date-with-dependabot
version: 2

updates:
  - package-ecosystem: mix
    directory: "/"
    insecure-external-code-execution: allow
    open-pull-requests-limit: 3
    schedule:
      interval: weekly
      time: "22:00"
      timezone: Australia/Melbourne
    labels:
      - "mix dependencies"
    reviewers:
      - "ston88"
      - "c20020207"
      - "hjemmel"

  - package-ecosystem: "github-actions"
    directory: "/"
    open-pull-requests-limit: 3
    schedule:
      interval: weekly
      time: "22:00"
      timezone: Australia/Melbourne
    labels:
      - "gha dependencies"
    reviewers:
      - "ston88"
      - "c20020207"
      - "hjemmel"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: weekly
      time: "22:00"
      timezone: Australia/Melbourne
    labels:
      - "docker dependencies"
    reviewers:
      - "ston88"
      - "c20020207"
      - "hjemmel"
