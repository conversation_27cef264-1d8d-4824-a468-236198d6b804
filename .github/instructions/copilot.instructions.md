This is our backend repo.  It is an Elixir / Phoenix umbrella app.

- always include the @codebase context in each chat
- The data fetching and models / contexts / Ecto (ORM) is in the app `Gaia`
- Each of the two main apps, `Athena` and `Hermes` are used as backends and graphql servers for our frontend apps of the same names
- The `Hades` app is our internal dashboard for managing clients, a Phoenix Liveview application