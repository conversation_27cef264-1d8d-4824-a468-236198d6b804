---
name: <PERSON>uild and Push Image to G<PERSON>
on:
  push:
    branches:
      - master
      - staging
  workflow_dispatch:

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-push-gcp:
    name: Build and Push to G<PERSON>
    runs-on: blacksmith-4vcpu-ubuntu-2204
    permissions:
      contents: "read"
      id-token: "write"
    env:
      IMAGE_NAME: leaf_be
      PROJECT_ID: leaf-343323
    steps:
      - name: Set environment for branch
        id: branch_check
        run: |
          if [[ $GITHUB_REF == 'refs/heads/master' ]]; then
              echo "MIX_ENV=prod" >> $GITHUB_OUTPUT
              echo "LEAF_ENVIRONMENT=production" >> $GITHUB_OUTPUT
          else
              echo "MIX_ENV=prod" >> $GITHUB_OUTPUT
              echo "LEAF_ENVIRONMENT=staging" >> $GITHUB_OUTPUT
          fi

      - name: Checkout
        uses: actions/checkout@v4

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.SERVICE_ACCOUNT_KEY }}"
          project_id: ${{ env.PROJECT_ID }}

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Configure Docker Client
        run: |-
          gcloud auth configure-docker australia-southeast1-docker.pkg.dev --quiet

      - name: Build Docker Image
        run: docker build -t $IMAGE_NAME:latest . --build-arg MIX_ENV=${{ steps.branch_check.outputs.MIX_ENV }} --build-arg LEAF_ENVIRONMENT=${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}

      - name: Push Docker Image to Artifact Registry
        run: |-
          docker tag $IMAGE_NAME:latest australia-southeast1-docker.pkg.dev/${PROJECT_ID}/leaf-gae/${IMAGE_NAME}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest
          docker push australia-southeast1-docker.pkg.dev/${PROJECT_ID}/leaf-gae/${IMAGE_NAME}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest

      - name: Prune
        id: prune
        run: |-
          bash ./.cloudbuild/prune.sh ${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }} 10

      - name: "Deploy to App Engine"
        uses: "google-github-actions/deploy-appengine@v2"
        with:
          deliverables: ".cloudbuild/app.${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}.yaml"
          image_url: australia-southeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/leaf-gae/${{ env.IMAGE_NAME }}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest
          version: "${{ github.ref_name }}-${{ github.run_number }}-${{ github.run_attempt }}"
          flags: "--stop-previous-version"

      # - name: Send notification to slack on deployment failure
      #   if: always() && (job.status == 'failure')
      #   id: slack
      #   uses: slackapi/slack-github-action@v1.18.0
      #   with:
      #     payload: |
      #       {
      #         "environment": "${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}",
      #         "actor": "${{ github.actor }}",
      #         "slack-message": "GitHub build result: ${{ job.status }}\n${{ github.event.pull_request.html_url || github.event.head_commit.url }}"
      #       }
      #   env:
      #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
