name: Run mix check

on:
  push:
    # These files are modified via GitHub Actions.
    # No action required if they change.
    paths-ignore:
      - "graphql/**"
      - "**/priv/gettext/**"
    branches-ignore:
      - staging

jobs:
  mix_check:
    name: mix check

    runs-on: blacksmith-4vcpu-ubuntu-2204

    env:
      GCP_SERVICE_ACCOUNT_CREDENTIALS_JSON: ${{ secrets.SERVICE_ACCOUNT_KEY }}
      SECRETS_MASTER_KEY: ${{ secrets.SECRETS_MASTER_KEY }}
      LEAF_ENVIRONMENT: test

    services:
      postgres:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_HOST_AUTH_METHOD: trust
          POSTGRES_USER: "postgres"
          POSTGRES_PASSWORD: "postgres"
          POSTGRES_DB: "leaf_test"
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          # Maps tcp port 5432 on service container to the host
          - 5432:5432

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set Erlang/Elixir Versions
        run: |
          echo "ERLANG_VERSION=$(grep -E '^\s*erlang\s' .tool-versions | awk '{print $2}')" >> $GITHUB_ENV
          echo "ELIXIR_VERSION=$(grep -E '^\s*elixir\s' .tool-versions | awk '{print $2}')" >> $GITHUB_ENV

      - name: Setup Elixir
        uses: erlef/setup-beam@v1
        with:
          elixir-version: "${{ env.ELIXIR_VERSION }}"
          otp-version: "${{ env.ERLANG_VERSION }}"

      - name: Cache Elixir deps & build & dialyzer
        uses: useblacksmith/cache@v5
        with:
          path: |
            deps
            _build
            dialyzer
          key: cache-${{ runner.os }}-${{ hashFiles('mix.lock') }}-${{ hashFiles('lib/**/*.ex', 'config/*.exs', 'mix.exs') }}
          restore-keys: |
            cache-${{ runner.os }}-${{ hashFiles('mix.lock') }}-
            cache-${{ runner.os }}

      - name: Fetch Elixir deps
        run: mix deps.get

      - name: Compile Elixir deps
        run: MIX_ENV=test mix deps.compile

      - name: Compile application and check for warnings
        run: MIX_ENV=test mix compile

      - name: Install poppler pdf utility and wkhtmltopdf for announcement prep tests
        run: sudo apt update && sudo apt-get install -y poppler-utils wkhtmltopdf

      - name: Run check
        run: mix check --except gettext

      - name: Run deps.audit
        run: mix deps.audit --ignore-advisory-ids GHSA-vq52-99r9-h5pw
