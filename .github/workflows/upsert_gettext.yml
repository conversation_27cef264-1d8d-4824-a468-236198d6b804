name: Upsert GetText Language Script
on:
  push:
    branches:
      - master
    # These files are modified via GitHub Actions.
    # No action required if they change.
    paths-ignore:
      - "graphql/**"
      - "**/priv/gettext/**"

jobs:
  upsert_gettext:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - name: Checkout master branch
        uses: actions/checkout@v4
        with:
          ref: master

      - name: Set Erlang/Elixir Versions
        run: |
          echo "ERLANG_VERSION=$(grep -E '^\s*erlang\s' .tool-versions | awk '{print $2}')" >> $GITHUB_ENV
          echo "ELIXIR_VERSION=$(grep -E '^\s*elixir\s' .tool-versions | awk '{print $2}')" >> $GITHUB_ENV

      - name: Setup Elixir
        uses: erlef/setup-beam@v1
        with:
          elixir-version: "${{ env.ELIXIR_VERSION }}"
          otp-version: "${{ env.ERLANG_VERSION }}"

      - name: <PERSON>tch Elixir deps
        run: mix deps.get

      - name: Generate Language File
        run: |
          mix gettext.extract --merge --local-en

      - name: Commit changes
        uses: EndBug/add-and-commit@v9
        with:
          add: .
          default_author: github_actions
