defmodule Analytics do
  @moduledoc """
  Documentation for `Analytics`.
  """

  @doc """
  Send tracking event to Mixpanel and Segment.

  `app` value can be `"athena"`, or `"hermes"`.
  """
  def track(app, event, user_id, properties \\ %{}) do
    Task.async(fn -> Analytics.Mixpanel.track(app, event, user_id, properties) end)

    Task.async(fn -> Analytics.Segment.track(app, event, user_id, properties) end)

    :ok
  end
end
