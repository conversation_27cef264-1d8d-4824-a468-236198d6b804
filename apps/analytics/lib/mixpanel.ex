defmodule Analytics.Mixpanel do
  @moduledoc false

  @base_url "https://api.mixpanel.com/"

  @doc """
  Send tracking event to Mixpanel.

  `app` value can be `"athena"`, or `"hermes"`.
  """
  def track(app, event, user_id, properties \\ %{}) do
    with project_id when not is_nil(project_id) and project_id != "" <- get_project_id(app),
         username when not is_nil(username) and username != "" <- get_service_account_username(),
         password when not is_nil(password) and password != "" <- get_service_account_password() do
      url =
        @base_url
        |> URI.parse()
        |> URI.merge("import")
        |> URI.append_query(URI.encode_query(%{"project_id" => project_id, "strict" => 1}, :rfc3986))
        |> URI.to_string()

      body = [
        %{
          "properties" =>
            Map.merge(
              properties,
              %{
                "distinct_id" => "#{user_id}",
                "event_original_name" => event,
                "id" => "#{user_id}",
                "time" => DateTime.to_unix(DateTime.utc_now()),
                "$identified_id" => "#{user_id}",
                "$insert_id" => UUID.uuid4(),
                "$user_id" => "#{user_id}"
              }
            ),
          "event" => event
        }
      ]

      headers = [
        {"Authorization", "Basic #{Base.encode64("#{username}:#{password}")}"},
        {"Content-Type", "application/json"}
      ]

      case HTTPoison.post(url, Jason.encode!(body), headers) do
        {:ok, %{status_code: 200, body: body}} -> {:ok, Jason.decode!(body)}
        {:ok, %{status_code: _, body: body}} -> {:error, Jason.decode!(body)}
        {:error, %{reason: reason}} -> {:error, reason}
      end
    else
      _ ->
        :skip
    end
  end

  def get_project_id("athena"), do: Application.get_env(:analytics, :athena_mixpanel_project_id)
  def get_project_id("hermes"), do: Application.get_env(:analytics, :hermes_mixpanel_project_id)

  def get_service_account_username, do: Application.get_env(:analytics, :mixpanel_service_account_username)
  def get_service_account_password, do: Application.get_env(:analytics, :mixpanel_service_account_password)
end
