defmodule Analytics.Segment do
  @moduledoc false

  @doc """
  Send tracking event to Segment.

  `app` value can be `"athena"`, or `"hermes"`.
  """
  def track(app, event, user_id, properties \\ %{}),
    do:
      app
      |> client()
      |> Segment.Http.send(%Segment.Analytics.Track{event: event, properties: properties, userId: user_id})

  defp client("athena"), do: :analytics |> Application.get_env(:athena_segment_key) |> Segment.Http.client()

  defp client("hermes"), do: :analytics |> Application.get_env(:hermes_segment_key) |> Segment.Http.client()
end
