defmodule Automic do
  @moduledoc """
  Documentation for `Automic using oauth`.
  """

  defp api_key do
    Application.get_env(:automic, :api_key)
  end

  defp base_url do
    Application.get_env(:automic, :base_url)
  end

  defp client_id do
    Application.get_env(:automic, :client_id)
  end

  defp client_secret do
    Application.get_env(:automic, :client_secret)
  end

  defp proxy_ip do
    Application.get_env(:boardroom, :proxy_ip)
  end

  defp proxy_port do
    Application.get_env(:boardroom, :proxy_port)
  end

  # Must use a static IP address to connect to Automic
  # Staging and prod already use one via app engine
  # dev:
  #   `gcloud config set project leaf-343323`
  #   `gcloud compute ssh --zone australia-southeast2-a tunnel -- -N -p 22 -D localhost:5000 -f`
  defp request_options do
    case Application.get_env(:helper, :runtime_env) do
      "development" -> [proxy: {:socks5, ~c"localhost", 5000}]
      _ -> [proxy: {proxy_ip(), proxy_port()}]
    end
  end

  @doc """
  Automic authorisation endpoint.

  Returns `{:ok, response}` if the request is successful.

  Returns `{:error, :automic_account_locked_error}` if the automic account has been locked.

  Returns `{:error, :bad_credentials_error}` if cannot get token with given oauth credentials.

  Returns `{:error, error}` otherwise.

  response_sample = %{
    "token_type" => "Bearer",
    "access_token" => "some_string",
    "refresh_token" => "some_string"
  }
  """
  def grant_access_token do
    auth_b64 = :base64.encode("#{client_id()}:#{client_secret()}")

    headers = [
      {"Accept", "application/json"},
      {"Authorization", "Basic #{auth_b64}"},
      {"Content-Type", "application/x-www-form-urlencoded"},
      {"X-API-Key", api_key()}
    ]

    base_url()
    |> URI.merge("oauth/token/v1/grant?grant_type=client_credentials")
    |> to_string()
    |> HTTPoison.post("{}", headers, request_options())
    |> handle_grant_access_token_response()
  end

  # Happy scenario
  defp handle_grant_access_token_response({:ok, %HTTPoison.Response{status_code: 200, body: body}}) do
    Poison.decode(body)
  end

  # Unhappy scenario - followed the scenario when using username and password, might not applicable to oauth
  # Account could be locked when we tried to logging in with wrong password a few times
  # Account credentials could also be wrong
  defp handle_grant_access_token_response({:ok, %HTTPoison.Response{body: body} = http_response}) do
    with {:ok, response_message} <- Poison.decode(body) do
      # 423 is the status code for a locked resource

      # 401 is the status code for unauthorized access (when they've entered the wrong credentials)
      response_message
      |> Map.get("status", 400)
      |> case do
        423 ->
          {:error, :automic_account_locked_error}

        401 ->
          {:error, :bad_credentials_error}

        _ ->
          {:error, http_response}
      end
    end
  end

  defp handle_grant_access_token_response({:error, http_error}), do: {:error, http_error}

  @doc """
  Automic issuer endpoint.

  Returns `{:ok, issuer}` if the request is successful.

  Returns `{:error, error}` otherwise.

  issuer_sample = [
    %{
      "id" => 1111,
      "code" => "ABC",
      "name" => "ABC Limited",
      "internetAddress" => "https://abc-limited-test.com",
      "groupType" => "None",
      "listed" => true,
      "subsidiaryLabel" => null,
      "parentId" => null
    }
  ]
  """
  def get_issuer(access_token) do
    base_url()
    |> URI.merge("issuer/v1")
    |> to_string()
    |> HTTPoison.get(
      [{"Authorization", get_authorization(access_token)}, {"X-API-Key", api_key()}],
      request_options()
    )
    |> handle_http_response()
  end

  @doc """
  Automic security endpoint.

  Returns `{:ok, security}` if the request is successful.

  Returns `{:error, error}` otherwise.

  security_sample = [
    %{
      "id" => 1444,
      "code" => "ABC",
      "name" => "ABC Fully Paid",
      "description" => "ABC Fully Paid Listed",
      "numberOfHolders" => 345,
      "issuedCapitalTotal" => "1000000.0",
      "decimals" => 2,
      "netAssetValueLabel" => "NAV",
      "issuedCapital" => true,
      "rg97" => true,
      "displayPrice" => true,
      "allowRebates" => true
    }
  ]
  """
  def get_security(issuer_id, access_token) do
    base_url()
    |> URI.merge("security/v1/issuer/#{issuer_id}")
    |> to_string()
    |> HTTPoison.get(
      [{"Authorization", get_authorization(access_token)}, {"X-API-Key", api_key()}],
      request_options()
    )
    |> handle_http_response()
  end

  @doc """
  Automic request register listing report endpoint.

  If as_of_date is not provided, automic will refer to current date.

  Return `{:ok, register_listing}` if the request is successful.

  Returns `{:error, error}` otherwise.

  register_listing_sample = %{
    "_links" => %{
      "status" => %{
        "href" =>
          "http://demo-api.automic.com.au/api/report/async/v1/issuer/1111/report/22222/status"
      }
    },
    "issuerId" => 1111,
    "reportId" => 22222
  }
  """
  def request_register_listing_report(issuer_id, security_ids, access_token, opts \\ []) do
    default_as_of_date = Helper.ExDay.now_date()
    as_of_date = Keyword.get(opts, :as_of_date, default_as_of_date)

    params =
      URI.encode_query([
        {"securityIds", security_ids},
        {"holderTypes", "Company,JointCompany,Individual,JointIndividual"},
        {"minimumHolding", "1"},
        {"asOfDate", as_of_date},
        {"countryCodes", ""}
      ])

    # double check if these fields below are still relevant with oauth api
    # {"chessFormat", "true"},
    # {"detailedView", "true"},
    # {"idVerification", "true"},
    # {"reportType", "JSON"}
    headers = [
      {"Accept", "application/json"},
      {"Authorization", get_authorization(access_token)},
      {"X-API-Key", api_key()}
    ]

    base_url()
    |> URI.merge("report/async/register-listing/v1/issuer/#{issuer_id}?#{params}")
    |> to_string()
    |> HTTPoison.post("{}", headers, request_options())
    |> handle_http_response()
  end

  @doc """
  Automic request transaction listing report endpoint.

  Return `{:ok, transaction_listing}` if the request is successful.

  Returns `{:error, error}` otherwise.

  transaction_listing_sample = %{
    "_links" => %{
      "status" => %{
        "href" =>
          "http://demo-api.automic.com.au/api/report/async/v1/issuer/1111/report/22222/status"
      }
    },
    "issuerId" => 1111,
    "reportId" => 22222
  }
  """
  def request_transaction_listing_report(issuer_id, security_ids, %Date{} = from_date, %Date{} = to_date, access_token) do
    params =
      URI.encode_query([
        {"securityIds", security_ids},
        {"fromDate", from_date},
        {"toDate", to_date},
        {"minThreshold", "0"},
        {"includeHolderNumber", "true"}
      ])

    # double check if these fields below are still relevant with oauth api
    # {"detailedView", "true"},
    # {"reportType", "JSON"}
    headers = [
      {"Accept", "application/json"},
      {"Authorization", get_authorization(access_token)},
      {"X-API-Key", api_key()}
    ]

    base_url()
    |> URI.merge("report/async/transaction-listing/v1/issuer/#{issuer_id}?#{params}")
    |> to_string()
    |> HTTPoison.post("{}", headers, request_options())
    |> handle_http_response()
  end

  @doc """
  Automic report status endpoint.

  Return `{:ok, report_status}` if the request is successful.

  Returns `{:error, error}` otherwise.

  report_status_sample = %{
    "_links" => %{
      "download" => %{
        "href" =>
          "http://demo-api.automic.com.au/api/report/async/v1/issuer/1111/report/22222/download"
      }
    },
    "reportStatus" => %{
      "error" => false,
      "errorMessage" => nil,
      "format" => ".json",
      "inProgress" => false,
      "issuerId" => 1111,
      "ready" => true,
      "reportId" => 22222,
      "reportName" => "register listing report",
      "requestedTimeUtc" => "2022-05-04T09:24:02Z",
      "status" => "COMPLETE"
    }
  }
  """
  def get_report_status(report_url, access_token) do
    report_url
    |> HTTPoison.get(
      [{"Authorization", get_authorization(access_token)}, {"X-API-Key", api_key()}],
      request_options()
    )
    |> handle_http_response()
  end

  @doc """
  Automic download report endpoint.

  Return `{:ok, report}` if the request is successful.

  Returns `{:error, error}` otherwise.

  ```
  register_listing_report_sample = %{
    "customAttributeConfigs" => [
      %{
        "id" => 1,
        "name" => "ID Verification Type"
      },
      %{
        "id" => 1,
        "name" => "ID Verification Status"
      }
    ],
    "asOfDate" => "2022-03-03",
    "securities" => [
      %{
        "id" => 123,
        "name" => "ABC Ordinary",
        "code" => "ABC",
        "decimals" => 0
      }
    ],
    "maxDecimals" => 0,
    "totalBalances" => ["1000000.************"],
    "holders" => [
      %{
        "holderId" => 234,
        "holderNumber" => nil,
        "registrationDetails" => nil,
        "chessFormat" => nil,
        "extendFormat" => nil,
        "holderType" => "Individual",
        "holderStatus" => "Normal",
        "communicationOption" => "EA",
        "emailAddress" => "<EMAIL>",
        "additionalEmailAddresses" =>
          "<EMAIL>,<EMAIL>",
        "mobileNumber" => "***********",
        "workNumber" => "+61 *********",
        "homeNumber" => "**********",
        "taxDetailsQuoted" => true,
        "returnedMail" => false,
        "primaryPaymentType" => "BPay",
        "primaryBsb" => "123-123",
        "primaryAccountNumber" => "*********",
        "primaryAccountName" => "Bank Test Account Name",
        "controllingPid" => nil,
        "drpParticipation" => "Full",
        "adviserNumber" => "123123",
        "adviserName" => "Financial Adviser Provider",
        "licenceNumber" => "234234",
        "idv1CreatedDate" => "2022-03-03",
        "idv2CreatedDate" => nil,
        "idv3CreatedDate" => nil,
        "idResponse1" => %{
          "type" => "BronIdVerificationResponseDto",
          "status" => "success",
          "verificationStatus" => "verified",
          "verificationUuid" => "5e7c24e9-7ecd-4784-bf2b-ac75601036d4",
          "parentVerificationUuid" => "656c43ce-2f70-42ae-864b-070c62154b21",
          "userId" => "8ced5353-ec31-4acc-8468-b298482e4b8f",
          "parentUserId" => "317e590c-d5de-4dae-a1df-a99cd6336387",
          "timestamp" => 1_646_303_127_677,
          "score" => "Low Risk",
          "watchList" => [
            %{
              "name" => "ABC Director Watchlist"
            }
          ],
          "outOfWatchlist" => [
            %{
              "name" => "Corporate Bank Watchlist"
            }
          ],
          "idvRiskScore" => %{
            "description" => "Low"
          }
        },
        "idResponse2" => nil,
        "idResponse3" => nil,
        "idV1OverrideScore" => "Low to Medium",
        "idV2OverrideScore" => nil,
        "idV3OverrideScore" => nil,
        "minFirstInvestmentDate" => "2022-03-03",
        "customAttributeValues" => ["Drive License", "Valid"],
        "fatcaCrsDeclaration" => true,
        "fatcaCrsDeclarationDate" => "2022-03-03",
        "balances" => ["1000.************"]
      }
    ]
  }

  transaction_listing_report_sample = %{
    "securities" => [
      %{
        "id" => 123,
        "name" => "TES Ordinary",
        "code" => "TES",
        "decimals" => 6
      }
    ],
    "transactions" => [
      %{
        "movementId" => 1234,
        "createdDate" => "2022-03-03",
        "effectiveDate" => "2022-03-03",
        "friendlyDescription" => "Initial Application Allotment",
        "internalDescription" => "Application allotment",
        "holderId" => 2345,
        "holderNumber" => "X00123456790",
        "accountHolders" => ["MR John Smith", "<THE JONES FAMILY A/C>"],
        "brokerCode" => "01234",
        "brokerName" => "Smith Financial Ltd",
        "securityCode" => "TES",
        "movement" => "********.123321",
        "closingBalance" => "*********.123123",
        "transactionPrice" => "123.21",
        "navPrice" => "123.22",
        "movementType" => "ApplicationAllotment",
        "holderGroup" => "Director Group"
      }
    ]
  }
  ```
  """
  def download_report(report_url, access_token) do
    headers = [
      {"Authorization", get_authorization(access_token)},
      {"Content-Type", "application/octet-stream"},
      {"X-API-Key", api_key()}
    ]

    report_url
    |> HTTPoison.get(headers, request_options())
    |> handle_http_response()
  end

  defp get_authorization(access_token) do
    "Bearer #{access_token}"
  end

  defp handle_http_response({:ok, %HTTPoison.Response{status_code: 200, body: body}}) do
    Poison.decode(body)
  end

  defp handle_http_response({_, %HTTPoison.Response{body: body}}) do
    {:error, Poison.decode!(body)}
  end
end
