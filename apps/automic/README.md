# Automic

Client for interacting with Automic OAuth API

[Link to documentation](https://developer.automic.com.au/#_getting_started)

## Usage

You will need `api_key`, `client_id` and `client_secret` to interact with Automic OAuth API
They are configured in `config/dev.exs` for local development

```elixir
# Get the access token
{:ok, access_token} = Automic.grant_access_token()

# Select the appropriate issuer
{:ok, [%{"id" => issuer_id} | _]} = Automic.get_issuer(access_token)

# Select the appropriate security
{:ok, [%{"id" => security_id} | _]} = Automic.get_security(issuer_id, access_token)

# Request the register listing report
{:ok, %{"links" => [%{"href" => url}]}} =
  Automic.request_register_listing_report(issuer_id, security_id, access_token)

# Get the status of the requested report
{:ok, %{"reportStatus" => %{"status" => "COMPLETE"}, "links" => [%{"href" => download_url}}} =
  Automic.get_report_status(url, access_token)

# Download the report
{:ok, register_report} = Automic.download_report(download_url, access_token)
```

## Installation

If [available in Hex](https://hex.pm/docs/publish), the package can be installed
by adding `automic` to your list of dependencies in `mix.exs`:

```elixir
def deps do
  [
    {:automic, in_umbrella: true}
  ]
end
```

Documentation can be generated with [ExDoc](https://github.com/elixir-lang/ex_doc)
and published on [HexDocs](https://hexdocs.pm). Once published, the docs can
be found at [https://hexdocs.pm/automic](https://hexdocs.pm/automic).

