defmodule Computershare.Holders do
  @moduledoc """
  Documentation for `Computershare.Holders`.
  """

  alias Computershare.Auth

  # Returns:
  # %{
  #   "accountName" => "SOMETHING PTY LTD <SOMETHING A/C>",
  #   "birthDates" => nil,
  #   "fullAddress" => "100 BLAH RD, MELBOURNE, VIC, 3000, AUSTRALIA",
  #   "hasBalance" => true,
  #   "holderId" => "Actual HIN",
  #   "holderIdEncrypted" => "PZjz/KlLZYzO6stTPL6kMd9v2Ohw1sSOSucix9/mR9Z6MTVnqhkfBPXrTaLJ5fHHXDGsA8N N0BIXkt6I673uAJ zuYmS5Hxfa9N8/w29Rr6O4DqrmSZ90roNuvMj430vVAEesz6CJhAP1zpNq8JbfXfHyTTVinIv79ybLHatGCyqVywJ3Gi 91 2dAF9h2rvjx4D2EL/M0mjlXTmHVL9nsJEqajnqqLWZAz/0Q50aJw8CJ71ka9x8QhDiuUP3NqEgid3BQjC9nAEjOEU3b2ZslxeXrjYBUcsm95j ******************************/PL0Cw1asKTlFZ0uZlAhiTy2zDhu4jQvXCTUXUX7fUjtb2q5EDAPO6hq1wO5jOaLVeUW/Hf2UUL1gdTdtxdyGywGOlnbkfN/H3PICQGrXkgw1vxh8cEHg6nYW03eqgmkHko/nrlfMJMgKMnfzewx1E1Quwd9ag G7mE4M TgTlCN1UsuDQ7WS2X OOZ8c96GFroCki0qpx0anEoaeUYDjGCXyfE Zj4Ipaqy9Zyp4oCRWCMkdtsiQZbE6DpvxKWXx9zN/WFUSc3sHHxEof 9C/33tTWIhsk2Q8 2U35wZcSKeQnEyKLKqZK6NIGMSaGyjWmIiNdzQiH1eT26Hkm3OBghbQs  GvSgx5 yJ4=",
  #   "holderIdMasked" => "I******4007",
  #   "holderLinkOutUrl" => "HolderSummary.aspx?HIN=EFVtU2Xz7t5hpXP0FjLXc2TQhnAuXeFG7K%2fqqHW1RfAnVitLVdWfJr4TSzdfeORRxppJ%2bK%2fC8XWbH6BNFPXQ0RS3DVHmGUbaSJ4cDuCVwfuptZFqqxtwtYx7EuGE1ArfH4sH%2faJlcav1k3pigSJyLP370G33P%2bmEUYtjL5v759u6qFwRkv4NzC9vYbdT9bASjlgEaDctq1QiMNRkZMj2tUU%3d&type=holdersearch2",
  #   "securityDetails" => nil
  # }

  # Can then get their profile from the holderLinkOutUrl

  def search(credential, search_phrase, attempts \\ 1) do
    {:ok, session_cookie} = Auth.get_session_cookie(credential)

    action_url = "https://www1.issueronline.com/Services/Anonymous/HolderWCF/HolderSearch2.svc/Search"

    req_body =
      Poison.encode!(%{
        "address" => "",
        "excludeNilBalance" => false,
        "holderIdEncrypted" => "",
        "holderLinkOutUrl" => "",
        "includeBalance" => false,
        "isGlobalSearch" => false,
        "originatingURL" => "https://www1.issueronline.com/Holders/HolderSearch2.aspx",
        "searchPhrase" => search_phrase
      })

    try do
      case HTTPoison.post(
             action_url,
             req_body,
             %{
               "Content-Type" => "application/json",
               "Cookie" => session_cookie,
               "Accept" => "application/json, text/javascript, */*; q=0.01",
               "Host" => "www1.issueronline.com",
               "Origin" => "https://www1.issueronline.com",
               "Referer" => "https://www1.issueronline.com/Holders/HolderSearch2.aspx"
             },
             hackney: [:insecure]
           ) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          parse_search_results(body)

        _ ->
          raise "Request failed"
      end
    rescue
      _error ->
        if attempts < 4 do
          Auth.set_session_cookie(credential)
          search(credential, search_phrase, attempts + 1)
        end
    end
  end

  defp parse_search_results(body) do
    body
    |> String.replace("\uFEFF", "")
    |> Poison.decode!()
    |> Map.get("data", %{})
    |> Map.get("holders", [])
    |> List.first()
  end

  def get_hashed_holder_id(credential, holder_id) do
    credential
    |> search(holder_id)
    |> parse_hashed_holder_id()
  end

  defp parse_hashed_holder_id(%{"holderLinkOutUrl" => holder_url}) do
    holder_url
    |> String.replace("HolderSummary.aspx?HIN=", "")
    |> String.replace("&type=holdersearch2", "")
  end

  defp parse_hashed_holder_id(_), do: nil

  # This endpoint is needed by the csv transactions, must visit this first before getting the transactions
  def get_balances_page(credential, hashed_hin, attempts \\ 1) do
    {:ok, session_cookie} = Auth.get_session_cookie(credential)

    action_url = "https://www1.issueronline.com/Holders/Balances.aspx?HIN=#{hashed_hin}"

    try do
      case HTTPoison.get(action_url, %{"Cookie" => session_cookie}, hackney: [:insecure]) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          body

        _ ->
          raise "Request failed"
      end
    rescue
      _error ->
        if attempts < 4 do
          Auth.set_session_cookie(credential)
          get_balances_page(credential, hashed_hin, attempts + 1)
        else
          []
        end
    end
  end

  # Use the CSV endpoint instead as this one has pagination that we can't get working
  # Still need this endpoint though for the CSV one to work
  # Computershare.Holders.transactions("lmg", hashed_hin, ~D[1980-01-01], ~D[2022-06-24])
  def get_transactions(credential, hashed_hin, from_date, to_date, attempts \\ 1) do
    {:ok, session_cookie} = Auth.get_session_cookie(credential)

    from_date_1 = Timex.format!(from_date, "{YYYY}-{0M}-{0D}")
    from_date_2 = Timex.format!(from_date, "{0D}/{0M}/{YYYY}")
    to_date_1 = Timex.format!(to_date, "{YYYY}-{0M}-{0D}")
    to_date_2 = Timex.format!(to_date, "{0D}/{0M}/{YYYY}")

    # "ctl00$ctl00$wpmanager$TransactionHistoryPart$radgTransactions$ctl00$ctl03$ctl01$ddl"
    # is the pagination field
    # FIXME: have not been able to get pagination working here yet...

    action_url = "https://www1.issueronline.com/Holders/Balances.aspx?HIN=#{hashed_hin}"

    req_body =
      Plug.Conn.Query.encode(%{
        "__VIEWSTATEGENERATOR" => "",
        "__VIEWSTATE" => "",
        "ctl00$ctl00$ScriptManager" =>
          "ctl00$ctl00$ctl00$ctl00$wpmanager$TransactionHistoryPart$TransactionsPanelPanel|ctl00$ctl00$wpmanager$TransactionHistoryPart$btnSubmit",
        "ctl00$ctl00$wpmanager$BalancesPart$RadDatePicker1" => "2022-06-23",
        "ctl00$ctl00$wpmanager$BalancesPart$RadDatePicker1$dateInput" => "23/06/2022",
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$RadDatePickerFrom" => from_date_1,
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$RadDatePickerFrom$dateInput" => from_date_2,
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$RadDatePickerTo" => to_date_1,
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$RadDatePickerTo$dateInput" => to_date_2,
        "ctl00_ctl00_wpmanager_BalancesPart_RadDatePicker1_ClientState" =>
          "{\"minDateStr\":\"2018-06-23-00-00-00\",\"maxDateStr\":\"2022-06-23-00-00-00\"}",
        "ctl00_ctl00_wpmanager_BalancesPart_RadDatePicker1_dateInput_ClientState" =>
          "{\"valueAsString\":\"2022-06-23-00-00-00\",\"lastSetTextBoxValue\":\"23/06/2022\"}",
        "ctl00_ctl00_wpmanager_TransactionHistoryPart_RadDatePickerFrom_dateInput_ClientState" =>
          "{\"valueAsString\":\"#{from_date_1}-00-00-00\",\"lastSetTextBoxValue\":\"#{from_date_2}\"}",
        "ctl00_ctl00_wpmanager_TransactionHistoryPart_RadDatePickerTo_ClientState" =>
          "{\"minDateStr\":\"1980-01-01-00-00-00\",\"maxDateStr\":\"2022-06-23-00-00-00\"}",
        "ctl00_ctl00_wpmanager_TransactionHistoryPart_RadDatePickerTo_dateInput_ClientState" =>
          "{\"valueAsString\":\"#{to_date_1}-00-00-00\",\"lastSetTextBoxValue\":\"#{to_date_2}\"}",
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$radgTransactions$ctl00$ctl03$ctl01$ddl" => 2,
        "ctl00$ctl00$wpmanager$TransactionHistoryPart$radgTransactions$ctl00$ctl03$ctl01$NextButton" => "Next >"
      })

    try do
      case HTTPoison.post(
             action_url,
             req_body,
             %{
               "Content-Type" => "application/x-www-form-urlencoded; charset=UTF-8",
               "Cookie" => session_cookie,
               "Accept" => "*/*",
               "Host" => "www1.issueronline.com",
               "Origin" => "https://www1.issueronline.com",
               "Referer" => action_url
             },
             hackney: [:insecure]
           ) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          parse_transaction_page(body)

        _ ->
          raise "Request failed"
      end
    rescue
      _error ->
        if attempts < 4 do
          Auth.set_session_cookie(credential)

          get_transactions(
            credential,
            hashed_hin,
            from_date,
            to_date,
            attempts + 1
          )
        else
          []
        end
    end
  end

  defp parse_transaction_page(body) do
    {:ok, content} = Floki.parse_document(body)

    content
    |> Floki.find("div#ctl00_ctl00_wpmanager_TransactionHistoryPart_radgTransactions")
    |> Floki.find("tr")
    |> Enum.map(&parse_transaction_row/1)
    |> Enum.filter(& &1)
  end

  defp parse_transaction_row(
         {"tr", _,
          [
            {"td", [{"class", "rgExpandCol"}], _},
            {"td", _, [transaction_date]},
            {"td", _, [transaction_type]},
            {"td", _, [transaction_change]},
            {"td", _, [balance]}
          ]}
       ) do
    %{
      date: transaction_date,
      movement_type: transaction_type,
      change: transaction_change,
      closing_balance: balance
    }
  end

  defp parse_transaction_row(_), do: nil

  # This is the actual endpoint to use
  def get_transactions_as_csv(%{metadata: %{security_alias: security_alias}} = credential, hashed_hin, attempts \\ 1) do
    us_format_today = Timex.format!(Helper.ExDay.now(), "{0M}/{0D}/{YYYY}")

    try do
      # The session seems to store which holder you were last looking at in a previous request (maybe hin not necessary?)
      get_balances_page(credential, hashed_hin)

      {:ok, session_cookie} = Auth.get_session_cookie(credential)

      # action_url = "https://www1.issueronline.com/Holders/Balances.aspx?HIN=EEXst1tCzuGiVthflqPhZRC5myPmRKdxzLE68epkgnZmnWmq60CfaWNKM6pKgRX36jQZQ7u%20sp47TQtl5fAr7bOTi392ZNrgASmY4MBBoSCcmaDe5NRiP67mfDwL5ZDjEcqbgfr/J0Vshd1E0cSmTypWOXxEbXixgKs4KKirAFfWH82vqVFcMBB4sY10HWY1IcplCllnoe/ot6gD8Q3o1XA=&Export=excel&SecurityId=FP~WA&DateFrom=06/27/2017&DateTo=06/27/2022"
      # Dates below are formatted in American format for some reason MM/DD/YYYY
      action_url =
        if security_alias do
          "https://www1.issueronline.com/Holders/Balances.aspx?HIN=#{hashed_hin}&Export=excel&DateFrom=01/01/1980&DateTo=#{us_format_today}&ExportHINUnmasked=true&SecurityId=#{security_alias}"
        else
          "https://www1.issueronline.com/Holders/Balances.aspx?HIN=#{hashed_hin}&Export=excel&DateFrom=01/01/1980&DateTo=#{us_format_today}&ExportHINUnmasked=true"
        end

      case HTTPoison.get(
             action_url,
             %{
               "Cookie" => session_cookie,
               "Host" => "www1.issueronline.com",
               "Pragma" => "no-cache",
               "Referer" => "https://www1.issueronline.com/Holders/Balances.aspx",
               "Sec-Fetch-Dest" => "document",
               "Sec-Fetch-Mode" => "navigate",
               "Sec-Fetch-Site" => "same-origin",
               "Sec-Fetch-User" => "?1",
               "Sec-GPC" => "1",
               "Upgrade-Insecure-Requests" => "1",
               "Cache-Control" => "no-cache",
               "Connection" => "keep-alive",
               "Accept" => "text/csv; charset=utf-8"
             },
             follow_redirect: true,
             hackney: [:insecure]
           ) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          result_to_movements(body)

        _ ->
          raise "Request failed"
      end
    rescue
      _error ->
        if attempts < 4 do
          Auth.set_session_cookie(credential)
          get_transactions_as_csv(credential, hashed_hin, attempts + 1)
        else
          []
        end
    end
  end

  defp result_to_movements(body) do
    if csv_contains_ordinary_fully_paid_shares(body) do
      parse_csv_transaction_results(body)
      # Note dates are American format, MM/DD/YYYY

      # iex(5)> Computershare.Holders.get_account_details("lmg", id)
      # [
      #   %{address: %{line_1: "PO BOX 276", line_2: "BELROSE NSW 2085"}},
      #   %{
      #     contact_details: %{
      #       email: "Not on file",
      #       home: "Not on file",
      #       mobile: "Not on file",
      #       work: "Not on file"
      #     }
      #   },
      #   %{
      #     accountinformationdetails: %{
      #       account_open_date: "11/04/2022",
      #       account_type: "Corporate",
      #       accountcloseddate: "06/05/2022",
      #       broker_name: "OPENMARKETS AUSTRALIA LIMITED",
      #       broker_pid: "1137",
      #       category: "CHESS Sponsored",
      #       dateof_birth_registration: "Not on file",
      #       employee_indicator: "No",
      #       investor_centre_last_login: "20/04/2022",
      #       investor_centre_member: "Yes"
      #     }
      #   },
      #   %{
      #     taxinformation: %{
      #       tax_file_number: "ABN quoted",
      #       tax_file_number_type: "Trust"
      #     }
      #   }
      # ]

      # Address

      # Contact Details

      # Communication preferences
      # TODO: parse this if we want the info

      # Tax Information
      # TODO: parse this if we want the info
    else
      []
    end
  end

  defp csv_contains_ordinary_fully_paid_shares(body) do
    share_type =
      body
      |> csv_rows_to_lists()
      |> Enum.find(&(List.first(&1) == "Class:"))
      |> Enum.at(1)

    share_type =~ ~r/ordinary/i && share_type =~ ~r/shares/i &&
      !(share_type =~ ~r/options/i)
  end

  defp csv_rows_to_lists(body) do
    body
    |> String.replace("\uFEFF", "")
    |> String.split("\r\n")
    |> Enum.reject(&(&1 == ""))
    |> Enum.map(&String.split(&1, ","))
  end

  defp parse_csv_transaction_results(body) do
    body
    |> csv_rows_to_lists()
    |> Enum.drop(6)
    |> Enum.map(&parse_csv_transaction_row/1)
    |> Enum.filter(& &1)
  end

  defp parse_csv_transaction_row(row) when is_list(row) do
    %{
      date: Enum.at(row, 0),
      movement_type: Enum.at(row, 1),
      change: Enum.at(row, 2),
      closing_balance: Enum.at(row, 3)
    }
  end

  defp parse_csv_transaction_row(_), do: nil

  def get_account_details(credential, hashed_hin, attempts \\ 1) do
    {:ok, session_cookie} = Auth.get_session_cookie(credential)

    action_url = "https://www1.issueronline.com/Holders/AccountDetails.aspx?HIN=#{hashed_hin}"

    try do
      case HTTPoison.get(
             action_url,
             %{
               "Cookie" => session_cookie,
               "Host" => "www1.issueronline.com",
               "Pragma" => "no-cache",
               "Referer" =>
                 "https://www1.issueronline.com/Holders/HolderSummary.aspx?HIN=#{hashed_hin}&type=holdersearch2",
               "Sec-Fetch-Dest" => "document",
               "Sec-Fetch-Mode" => "navigate",
               "Sec-Fetch-Site" => "same-origin",
               "Sec-Fetch-User" => "?1",
               "Sec-GPC" => "1",
               "Upgrade-Insecure-Requests" => "1",
               "Cache-Control" => "no-cache",
               "Connection" => "keep-alive"
             },
             follow_redirect: true,
             hackney: [:insecure]
           ) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          parse_account_details_response(body)

        _ ->
          raise "Request failed"
      end
    rescue
      _ ->
        if attempts < 4 do
          Auth.set_session_cookie(credential)
          get_account_details(credential, hashed_hin, attempts + 1)
        else
          %{}
        end
    end
  end

  defp parse_account_details_response(body) do
    {:ok, content} = Floki.parse_document(body)

    content
    |> Floki.find("div#ctl00_ctl00_wpmanager_AccountDetailsPart_content")
    |> Floki.find("table")
    |> Enum.map(&parse_account_details_table/1)
    |> Enum.filter(& &1)
    |> Enum.reduce(fn x, y ->
      Map.merge(x, y, fn _k, v1, v2 -> v2 ++ v1 end)
    end)
  rescue
    _error ->
      %{}
  end

  defp parse_account_details_table(
         {"table", _,
          [_, {_, _, [{"th", _, ["Address:"]}, {"td", [], [address_line_1, {"br", [], []}, address_line_2]}, _]}, _]}
       ) do
    %{
      address: %{
        line_1: address_line_1,
        line_2: address_line_2
      }
    }
  end

  defp parse_account_details_table(
         {"table", _,
          [
            _,
            {"tr", [], [{"th", _, ["Home:"]}, {"td", [], [home]}, _]},
            {"tr", [], [{"th", _, ["Work:"]}, {"td", [], [work]}, _]},
            {"tr", [], [{"th", _, ["Mobile:"]}, {"td", [], [mobile]}, _]},
            {"tr", [], [{"th", _, ["Email Address:"]}, {"td", [], [email]}, _]}
          ]}
       ) do
    %{
      contact_details: %{
        home: home,
        work: work,
        mobile: mobile,
        email: email
      }
    }
  end

  defp parse_account_details_table(
         {"table", _,
          [
            _,
            {"tr", [], [{"th", _, ["Annual Report:"]}, {"td", [], [{"div", _, [_annual_report]}]}, _]},
            {"tr", [],
             [
               {"th", _, [{"span", [], ["Publications:"]}]},
               {"td", _,
                [
                  {"div", [],
                   [
                     {"div", [],
                      [
                        {"div", [], [_, {"span", _, ["Company Information"]}, _]},
                        {"span", _, ["Delivery via:"]},
                        _preference_1
                      ]},
                     {"div", [],
                      [{"div", [], [_, {"span", _, ["Statements"]}, _]}, {"span", _, ["Delivery via:"]}, _preference_2]},
                     {"div", [],
                      [
                        {"div", [], [_, {"span", _, ["Notice of Meeting and Proxy"]}, _]},
                        {"span", _, ["Delivery via:"]},
                        _preference_3
                      ]}
                   ]}
                ]}
             ]}
          ]}
       ) do
    nil
  end

  defp parse_account_details_table(
         {"table", _,
          [
            _,
            {"tr", [], [{"th", _, ["Tax File Number:"]}, {"td", [], [_tfn_quoted]}, _]},
            {"tr", [], [{"th", _, ["Tax File Number Type:"]}, {"td", [], [_tfn_type]}, _]}
          ]}
       ) do
    nil
  end

  defp parse_account_details_table({"table", _, [head | tail]}) do
    content =
      tail
      |> Enum.map(&parse_account_details_row/1)
      |> Enum.reduce(fn x, y ->
        Map.merge(x, y, fn _k, v1, v2 -> v2 ++ v1 end)
      end)
      |> Map.new(fn {k, v} -> {String.to_atom(k), v} end)

    title_text =
      head
      |> Floki.text()
      |> String.replace("\r\n\t\t\t\t\t", "")
      |> String.replace(":", "")
      |> String.replace(" ", "")
      |> Macro.underscore()
      |> String.replace("\t_", "")
      |> String.to_atom()

    %{title_text => content}
  end

  defp parse_account_details_table(_), do: nil

  defp parse_account_details_row({"tr", [], [{"th", _, ["Broker Details:"]}, {"td", [], [broker_id, _, broker_name]}, _]}) do
    %{
      "broker_pid" => String.replace(broker_id, "ID: ", ""),
      "broker_name" => String.replace(broker_name, "Name: ", "")
    }
  end

  defp parse_account_details_row({"tr", [], [title, content, _]}), do: parse_account_details_row_resolve(title, content)

  defp parse_account_details_row({"tr", [], [title, content]}), do: parse_account_details_row_resolve(title, content)

  defp parse_account_details_row_resolve(title, content) do
    title_text =
      title
      |> Floki.text()
      |> String.replace(":", "")
      |> String.replace(" ", "")
      |> String.replace("/", "")
      |> Macro.underscore()
      |> String.replace("\t_", "")

    %{
      title_text => Floki.text(content)
    }
  end
end
