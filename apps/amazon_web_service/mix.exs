defmodule AmazonWebService.MixProject do
  use Mix.Project

  def project do
    [
      app: :amazon_web_service,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:aws, "~> 0.10.0"},
      {:hackney, "~> 1.24"},
      {:httpoison, "~> 1.8"},
      {:poison, "~> 3.1"},
      {:slack, in_umbrella: true}
    ]
  end
end
