defmodule AmazonWebService.SES do
  @moduledoc """
  AWS SES
  """

  require Logger

  @configuration_set_name "default-configuration-set"
  @event_destination_name "default-sns-destination"
  @ets_table :ses_status

  def get_ets_table, do: @ets_table

  defp access_key, do: Application.get_env(:aws, :access_key)

  defp secret_access_key, do: Application.get_env(:aws, :secret)

  defp region, do: Application.get_env(:aws, :region)

  defp sns_topic_arn, do: Application.get_env(:aws, :sns_topic_arn)

  #########################################################################
  # Company sender email address verification workflow with AWS SES       #
  #########################################################################

  def get_client, do: %AWS.Client{access_key_id: access_key(), region: region(), secret_access_key: secret_access_key()}

  @doc """
  Create a domain identity.
  """
  def create_domain_identity(domain) do
    with %AWS.Client{} = client <- get_client(),
         {:ok, %{"DkimAttributes" => %{"Status" => status, "Tokens" => tokens}}, _} <-
           AWS.SESv2.create_email_identity(
             client,
             %{
               "ConfigurationSetName" => @configuration_set_name,
               "EmailIdentity" => domain
             }
           ),
         # Disable feedback forwarding as we already setup SNS
         {:ok, _, _} <-
           AWS.SES.set_identity_feedback_forwarding_enabled(
             client,
             %{"ForwardingEnabled" => false, "Identity" => domain}
           ) do
      {:ok, tokens_to_dns_records(tokens, status)}
    else
      {:error, aws_response} ->
        create_domain_identity_error_resolution(aws_response, domain)

      other ->
        {:error, "Cannot create Amazon SES domain identity: #{inspect(other)}"}
    end
  end

  defp create_domain_identity_error_resolution({_response_type, %{body: body, headers: headers}}, domain) do
    case headers |> Map.new() |> Map.get("x-amzn-ErrorType") do
      "AlreadyExistsException" ->
        # domain exists, get the DNS records
        get_configured_and_recommended_dns_settings(domain)

      "NotFoundException" ->
        # Configuration setting not found
        error_message = body |> Poison.decode!() |> Map.get("message")
        {:error, "Cannot create Amazon SES domain identity: #{error_message}"}

      other ->
        {:error, "Cannot create Amazon SES domain identity: #{inspect(other)}"}
    end
  end

  defp create_domain_identity_error_resolution(response, domain) do
    {:error, "Cannot create Amazon SES domain identity - Domain: #{inspect(domain)}, Response: #{inspect(response)}"}
  end

  def create_mail_from_for_identity(identity_domain, mail_from_domain) do
    client = get_client()

    AWS.SES.set_identity_mail_from_domain(
      client,
      %{"MailFromDomain" => mail_from_domain, "Identity" => identity_domain}
    )
  end

  @doc """
  When you send emails through Amazon SES, the MAIL FROM domain is used in the Return-Path header of the email. By default, Amazon SES uses its own MAIL FROM domain (amazonses.com or amazon.com). However, you have the option to configure a custom MAIL FROM domain for your verified email identities.

  The MailFromDomainStatus can have one of the following values:

  "SUCCESS": The custom MAIL FROM domain is successfully verified and can be used for sending emails.
  "PENDING": The verification of the custom MAIL FROM domain is pending. This means that the necessary DNS records (MX and TXT records) are not yet detected by Amazon SES.
  "FAILED": The verification of the custom MAIL FROM domain has failed. This could happen if the required DNS records are not set up correctly or if there are other configuration issues.
  "TEMPORARY_FAILURE": There is a temporary issue with the custom MAIL FROM domain verification, such as a DNS lookup failure. Amazon SES will retry the verification process.
  """
  def get_mail_from_identity(domain) do
    get_client()
    |> AWS.SESv2.get_email_identity(domain)
    |> case do
      {:ok, %{"MailFromAttributes" => %{"MailFromDomainStatus" => "SUCCESS"}}, _} ->
        {:ok, true}

      {:error, error} ->
        {:error, "Cannot get From Mail Domain Status. Error: #{inspect(error)}"}

      _ ->
        {:ok, false}
    end
  end

  @doc """
  Before verifying an email identity,
  need to create an email identity first.
  Check if the DKIM CNAMEs is set.

  Domain should only be treated as verified if:
  - Has a configuration set
  - Has one event destination (SNS)
  - Has feedback forwarding disabled
  - Using the right SNS topic

  To minimise the risk of losing email events data
  """
  def is_dkim_verified(domain) do
    client = get_client()

    with {
           :ok,
           %{
             "ConfigurationSetName" => configuration_set_name,
             "DkimAttributes" => %{"Status" => "SUCCESS"},
             "FeedbackForwardingStatus" => false
           },
           _
         }
         when is_binary(configuration_set_name) <-
           AWS.SESv2.get_email_identity(client, domain),
         {:ok, %{"SendingOptions" => %{"SendingEnabled" => true}}, _} <-
           AWS.SESv2.get_configuration_set(client, configuration_set_name),
         {:ok, true} <-
           check_ses_events_are_working(client, configuration_set_name, Application.get_env(:helper, :runtime_env)) do
      {:ok, true}
    else
      {:ok, %{"ConfigurationSetName" => nil}, _} ->
        {:error, "Configuration set not found"}

      {:ok, %{"DkimAttributes" => %{"Status" => "FAILED"}}, _} ->
        {:error, "Verification failed"}

      {:ok, %{"DkimAttributes" => %{"Status" => "PENDING"}}, _} ->
        {:ok, false}

      {:ok, %{"FeedbackForwardingStatus" => true}, _} ->
        {:ok, false}

      {:ok, %{"SendingOptions" => %{"SendingEnabled" => false}}, _} ->
        {:error, "Configuration set sending disabled"}

      {:error, msg} ->
        {:error, msg}

      _error ->
        {:error, "Cannot verify Amazon SES domain identity for #{domain}"}
    end
  end

  # SES events like open, click, bounce, etc. aren't setup for local development. Hence we skip it here.
  defp check_ses_events_are_working(_, _, "development"), do: {:ok, true}

  defp check_ses_events_are_working(client, configuration_set_name, _env) do
    with {
           :ok,
           %{
             "EventDestinations" => [
               %{
                 "Enabled" => true,
                 "SnsDestination" => %{"TopicArn" => topic_arn}
               }
             ]
           },
           _
         } <-
           AWS.SESv2.get_configuration_set_event_destinations(
             client,
             configuration_set_name
           ),
         {:check_topic_arn, true} <-
           {:check_topic_arn, topic_arn === sns_topic_arn()} do
      {:ok, true}
    else
      {:ok, %{"EventDestinations" => _}, _} ->
        {:error, "Error on event destinations"}

      {:check_topic_arn, false} ->
        {:error, "Incorrect SNS topic"}

      _error ->
        {:error, ""}
    end
  end

  def get_configured_and_recommended_dns_settings(domain) do
    case AWS.SESv2.get_email_identity(get_client(), domain) do
      {:ok, %{"DkimAttributes" => %{"Status" => status, "Tokens" => tokens}}, _} ->
        {:ok, tokens_to_dns_records(tokens, status)}

      error ->
        error
    end
  end

  def get_dns_dkim_status(domain) do
    case AWS.SESv2.get_email_identity(get_client(), domain) do
      {:ok, %{"DkimAttributes" => %{"Status" => status, "Tokens" => tokens}}, _} ->
        {:ok, status, tokens_to_dns_records(tokens, status)}

      error ->
        error
    end
  end

  defp tokens_to_dns_records(tokens, status) do
    Enum.map(tokens, fn t ->
      %{
        name: "#{t}._domainkey",
        type: "CNAME",
        value: "#{t}.dkim.amazonses.com",
        configured: status == "SUCCESS"
      }
    end)
  end

  @doc """
  Deletes an email identity. An identity can be either an email address or a domain name.
  """
  def delete_domain_identity(domain) do
    get_client()
    |> AWS.SESv2.delete_email_identity(domain, nil)
    |> case do
      {:ok, _, %{status_code: 200}} ->
        {:ok, "Email identity #{domain} has been deleted successfully."}

      {:error, {:unexpected_response, %{status_code: 404}}} ->
        {:ok, "Email identity #{domain} has already deleted."}

      error ->
        error
    end
  end

  @doc """
  Creates a SES configuration set

  This is only for reference as we only need to run this once when we first created our AWS account
  """
  def create_default_configuration_set do
    with %AWS.Client{} = client <- get_client(),
         {:ok, _, %{status_code: 200}} <-
           AWS.SESv2.create_configuration_set(client, %{
             "ConfigurationSetName" => @configuration_set_name,
             "ReputationOptions" => %{"ReputationMetricsEnabled" => true},
             "SendingOptions" => %{"SendingEnabled" => true}
           }),
         {:ok, _, %{status_code: 200}} <-
           AWS.SESv2.create_configuration_set_event_destination(
             client,
             @configuration_set_name,
             %{
               "EventDestination" => %{
                 "Enabled" => true,
                 "MatchingEventTypes" => [
                   "BOUNCE",
                   "CLICK",
                   "COMPLAINT",
                   "DELIVERY",
                   "DELIVERY_DELAY",
                   "OPEN",
                   "REJECT",
                   "RENDERING_FAILURE",
                   "SEND",
                   "SUBSCRIPTION"
                 ],
                 "SnsDestination" => %{
                   "TopicArn" => sns_topic_arn()
                 }
               },
               "EventDestinationName" => @event_destination_name
             }
           ) do
      {:ok, "Configuration set created"}
    else
      {:error, aws_response} ->
        create_configuration_set_error_resolution(
          aws_response,
          @configuration_set_name
        )

      _ ->
        {:error, "Cannot create Amazon SES configuration set"}
    end
  end

  defp create_configuration_set_error_resolution({_response_type, %{headers: headers}}, configuration_set_name) do
    case headers |> Map.new() |> Map.get("x-amzn-ErrorType") do
      "AlreadyExistsException" ->
        # configuration set exists already, ok
        {:ok, "Configuration set exists already"}

      _ ->
        Slack.Message.send(
          %{
            text: "Something wrong with creating configuration set #{configuration_set_name}"
          },
          :notification_webhook_url
        )

        {:error, "Cannot create Amazon SES configuration set"}
    end
  end

  defp create_configuration_set_error_resolution(_, _), do: {:error, "Cannot create Amazon SES configuration set"}

  @doc """
  List all email identities that are available on Amazon SES
  """
  def list_email_identities do
    get_client()
    |> AWS.SESv2.list_email_identities()
    |> retrieve_more_list_email_identities([])
  end

  # Return identities when all pages have been iterated
  defp retrieve_more_list_email_identities({:ok, %{"EmailIdentities" => identities, "NextToken" => nil}, _}, acc)
       when is_list(identities) do
    identities ++ acc
  end

  defp retrieve_more_list_email_identities({:ok, %{"EmailIdentities" => identities, "NextToken" => next_token}, _}, acc)
       when is_list(identities) and is_binary(next_token) do
    get_client()
    |> AWS.SESv2.list_email_identities(next_token)
    |> retrieve_more_list_email_identities(identities ++ acc)
  end

  defp retrieve_more_list_email_identities(_, _) do
    raise("Failed to retrieve email identities")
  end

  @doc """
  Reset all of the SES identities to default settings

  Default settings:
    - Use the default configuration set
    - Disable feedback forwarding
  """
  def reset_identities_settings do
    Map.new(list_email_identities(), fn %{"IdentityName" => email_identity} ->
      :timer.sleep(750)
      res = get_client() |> AWS.SESv2.get_email_identity(email_identity) |> reset_identity_setting(email_identity)
      Logger.info("Reset identity #{email_identity}: #{res}")
      {email_identity, res}
    end)

    # Put a delay between each request to comply with AWS RPS (requests per second) rules
  end

  defp reset_identity_setting(
         {:ok, %{"ConfigurationSetName" => config, "FeedbackForwardingStatus" => status}, _},
         email_identity
       ) do
    # Use default configuration set if not already using it
    if config != @configuration_set_name do
      input = %{"ConfigurationSetName" => @configuration_set_name}

      AWS.SESv2.put_email_identity_configuration_set_attributes(get_client(), email_identity, input)
    end

    # Disable feedback forwarding if not already disabled
    if status == true do
      input = %{"ForwardingEnabled" => false, "Identity" => email_identity}

      AWS.SES.set_identity_feedback_forwarding_enabled(get_client(), input)
    end

    :ok
  end

  defp reset_identity_setting(_, _email_identity) do
    :error
  end

  def insert_aws_ses_status_into_ets(root_domain) do
    now = NaiveDateTime.utc_now()

    result =
      root_domain
      |> is_dkim_verified()
      |> case do
        {:ok, true} ->
          true

        _ ->
          false
      end

    :ets.insert(@ets_table, {root_domain, {result, now}})

    result
  end

  def fetch_aws_status_from_ets(root_domain) do
    with [{_, {result, time}}] <- :ets.lookup(@ets_table, root_domain),
         now = NaiveDateTime.utc_now(),
         diff when diff < 300 <- NaiveDateTime.diff(now, time, :second) do
      result
    else
      _ -> insert_aws_ses_status_into_ets(root_domain)
    end
  end

  def is_dmarc?(root_domain) do
    ("_dmarc." <> root_domain)
    |> String.to_charlist()
    |> :inet_res.lookup(:in, :txt)
    |> List.flatten()
    |> List.to_string()
    |> String.contains?("v=DMARC1")
  end

  def get_domain_isp(domain) do
    domain
    |> String.to_charlist()
    |> :inet_res.lookup(:in, :mx)
    |> Enum.map(fn {_, url} -> url end)
    |> List.flatten()
    |> List.to_string()
    |> get_domain_isp_dic(:google)
  end

  defp get_domain_isp_dic(mx_record, :google) do
    if String.contains?(mx_record, "google") do
      "google"
    else
      get_domain_isp_dic(mx_record, :microsoft)
    end
  end

  defp get_domain_isp_dic(mx_record, :microsoft) do
    if String.contains?(mx_record, "outlook") do
      "microsoft"
    else
      get_domain_isp_dic(mx_record, :yahoo)
    end
  end

  defp get_domain_isp_dic(mx_record, :yahoo) do
    if String.contains?(mx_record, "yahoo") do
      "yahoo"
    else
      get_domain_isp_dic(mx_record, :amazon)
    end
  end

  defp get_domain_isp_dic(mx_record, :amazon) do
    if String.contains?(mx_record, "amazon") do
      "amazon"
    else
      get_domain_isp_dic(mx_record, :mailguard)
    end
  end

  defp get_domain_isp_dic(mx_record, :mailguard) do
    if String.contains?(mx_record, "mailguard") do
      "mailguard"
    else
      get_domain_isp_dic(mx_record, :mimecast)
    end
  end

  defp get_domain_isp_dic(mx_record, :mimecast) do
    if String.contains?(mx_record, "mimecast") do
      "mimecast"
    else
      mx_record
    end
  end

  #########################################################################
  # Development Tools                                                     #
  #########################################################################
  # To verify if SES setup correctly
  # Get identity
  # Get config set, shouldn't be null
  # Get config set destination, should only contain one event destination
  # Testing around with SES API for next tasks
  # def generate_report() do
  #   data =
  #     list_email_identities()
  #     |> Enum.map(fn %{"IdentityName" => email_identity, "SendingEnabled" => identity_enabled} ->
  #       # Put a delay between each request to comply with AWS RPS (requests per second) rules
  #       :timer.sleep(500)

  #       with {
  #              :ok,
  #              %{
  #                "ConfigurationSetName" => configuration_set_name,
  #                "FeedbackForwardingStatus" => feedback_forwarding_status
  #              },
  #              _
  #            }
  #            when is_binary(configuration_set_name) <-
  #              AWS.SESv2.get_email_identity(get_client(), email_identity),
  #            {:ok, %{"SendingOptions" => %{"SendingEnabled" => configuration_enabled}}, _} <-
  #              AWS.SESv2.get_configuration_set(get_client(), configuration_set_name) do
  #         {:ok, %{"EventDestinations" => event_destinations}, _} =
  #           AWS.SESv2.get_configuration_set_event_destinations(
  #             get_client(),
  #             configuration_set_name
  #           )

  #         event_destination =
  #           case event_destinations do
  #             nil -> %{}
  #             [ele] -> ele
  #           end

  #         %{
  #           identity: email_identity,
  #           identity_sending_enabled: identity_enabled,
  #           identity_feedback_forwarding_status: feedback_forwarding_status,
  #           configuration_set: configuration_set_name,
  #           configuration_sending_enabled: configuration_enabled,
  #           event_destination_enabled: get_in(event_destination, ["Enabled"]),
  #           event_destination_topic_arn: get_in(event_destination, ["SnsDestination", "TopicArn"])
  #         }
  #       else
  #         {:ok, %{"ConfigurationSetName" => nil}, _} ->
  #           %{identity: email_identity, configuration_set_name: nil, sending_enabled: nil}

  #         _error ->
  #           %{identity: email_identity, configuration_set_name: :error, sending_enabled: :error}
  #       end
  #     end)
  #     |> Poison.encode!()

  #   File.write!("./report.json", data, [:utf8])
  # end
end
