defmodule AthenaWeb.LogEmailControllerTest do
  use AthenaWeb.ConnCase

  alias Gaia.Contacts
  alias Gaia.Contacts.EmailLog

  describe "webhook/2" do
    setup [:build_data, :build_attrs]

    test "ensure rejects requests with wrong username and password", %{conn: conn, attrs: attrs} do
      conn
      |> put_req_header("authorization", "Basic " <> Base.encode64("#{Faker.Pokemon.name()}:#{Faker.Code.Iban.iban()}"))
      |> post(Routes.log_email_path(conn, :webhook), attrs)
      |> response(401)
    end

    test "ensure contact is not created when receiving a new email log", %{conn: conn, auth: auth, attrs: attrs} do
      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      email_log = Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})

      assert response =~ "OK"
      assert is_nil(Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "from"])}))
      assert is_nil(Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "to"])}))

      assert %EmailLog{contact_id: nil, company_profile_id: nil} = email_log
    end

    test "ensure contact source is not update when receiving a new email log for an existing contact", %{
      conn: conn,
      auth: auth,
      attrs: attrs,
      company_profile: %{id: company_profile_id}
    } do
      %{id: contact_id} =
        contact(%{
          contact_source: :registry_import,
          email: get_in(attrs, ["headers", "to"]),
          company_profile_id: company_profile_id
        })

      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      email_log = Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})

      assert response =~ "OK"
      assert is_nil(Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "from"])}))

      assert %{contact_source: :registry_import} =
               Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "to"])})

      assert %EmailLog{contact_id: ^contact_id, company_profile_id: ^company_profile_id} = email_log
    end

    test "ensure get message id is parsed correctly from references fields", %{
      conn: conn,
      auth: auth,
      attrs: attrs,
      company_profile: %{id: company_profile_id}
    } do
      contact(%{
        contact_source: :registry_import,
        email: get_in(attrs, ["headers", "to"]),
        company_profile_id: company_profile_id
      })

      reference = Faker.Code.iban()

      attrs = put_in(attrs, ["headers", "references"], "#{reference} - Reference ID")

      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      assert is_nil(Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])}))

      refute is_nil(Gaia.Contacts.get_email_log_by(%{message_id: reference}))

      assert response =~ "OK"
    end

    test "ensure email format FirstName LastName <<EMAIL>> is parsed correctly", %{
      conn: conn,
      auth: auth,
      attrs: attrs,
      company_profile: %{id: company_profile_id}
    } do
      contact(%{
        contact_source: :registry_import,
        email: get_in(attrs, ["headers", "to"]),
        company_profile_id: company_profile_id
      })

      email = "#{Faker.Person.first_name()} #{Faker.Person.last_name()} <#{get_in(attrs, ["headers", "to"])}>"

      attrs =
        put_in(
          attrs,
          ["headers", "to"],
          email
        )

      conn
      |> put_req_header("authorization", auth)
      |> post(Routes.log_email_path(conn, :webhook), attrs)
      |> response(200)

      assert %{to: ^email} = Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})
    end

    test "ensure when a new email log with same message id is received we update the content", %{
      conn: conn,
      auth: auth,
      attrs: %{"plain" => content} = attrs,
      company_profile: %{id: company_profile_id}
    } do
      %{id: contact_id} =
        contact(%{
          contact_source: :registry_import,
          email: get_in(attrs, ["headers", "to"]),
          company_profile_id: company_profile_id
        })

      old_date =
        DateTime.utc_now()
        |> Timex.shift(days: -5)
        |> Timex.format!("{WDshort}, {0D} {Mshort} {YYYY} {h24}:{m}:{s} {Z}")

      Contacts.create_email_log(%{
        from: Faker.Internet.email(),
        message_id: get_in(attrs, ["headers", "message_id"]),
        metadata: put_in(attrs, ["headers", "date"], old_date),
        subject: "Old subject",
        text_content: "Old Contect",
        to: Faker.Internet.email(),
        company_profile_id: company_profile_id,
        contact_id: contact_id
      })

      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      email_log = Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})

      assert response =~ "OK"
      assert is_nil(Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "from"])}))

      assert %{contact_source: :registry_import} =
               Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "to"])})

      assert %EmailLog{
               contact_id: ^contact_id,
               text_content: ^content,
               company_profile_id: ^company_profile_id
             } = email_log
    end

    test "ensure parsing an old email already parsed we don't update the content", %{
      conn: conn,
      auth: auth,
      attrs: attrs,
      company_profile: %{id: company_profile_id}
    } do
      %{id: contact_id} =
        contact(%{
          contact_source: :registry_import,
          email: get_in(attrs, ["headers", "to"]),
          company_profile_id: company_profile_id
        })

      new_date =
        DateTime.utc_now()
        |> Timex.shift(days: 5)
        |> Timex.format!("{WDshort}, {0D} {Mshort} {YYYY} {h24}:{m}:{s} {Z}")

      Contacts.create_email_log(%{
        from: Faker.Internet.email(),
        message_id: get_in(attrs, ["headers", "message_id"]),
        metadata: put_in(attrs, ["headers", "date"], new_date),
        subject: "Old subject",
        text_content: "Old Contect",
        to: Faker.Internet.email(),
        company_profile_id: company_profile_id,
        contact_id: contact_id
      })

      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      email_log = Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})

      assert response =~ "OK"
      assert is_nil(Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "from"])}))

      assert %{contact_source: :registry_import} =
               Gaia.Contacts.get_contact_by(%{email: get_in(attrs, ["headers", "to"])})

      assert %EmailLog{
               contact_id: ^contact_id,
               text_content: "Old Contect",
               company_profile_id: ^company_profile_id
             } = email_log
    end

    test "ensure we can handle an empty subject", %{
      conn: conn,
      auth: auth,
      attrs: attrs
    } do
      attrs = put_in(attrs, ["headers", "subject"], nil)

      response =
        conn
        |> put_req_header("authorization", auth)
        |> post(Routes.log_email_path(conn, :webhook), attrs)
        |> response(200)

      %EmailLog{subject: subject} =
        Gaia.Contacts.get_email_log_by(%{message_id: get_in(attrs, ["headers", "message_id"])})

      assert response =~ "OK"
      assert is_nil(subject)
    end
  end

  def build_data(_) do
    company_profile = company_profile()
    custom_domain = company_profile.custom_domain
    [company_profile: company_profile, custom_domain: custom_domain]
  end

  def build_attrs(%{custom_domain: %{root_domain: root_domain}}) do
    [
      attrs: %{
        "headers" => %{
          "date" => Timex.format!(DateTime.utc_now(), "{WDshort}, {0D} {Mshort} {YYYY} {h24}:{m}:{s} {Z}"),
          "from" => "#{Faker.Pokemon.name()}@#{root_domain}",
          "message_id" => Faker.UUID.v4(),
          "to" => Faker.Internet.email(),
          "subject" => Faker.Lorem.sentence()
        },
        "plain" => Faker.Lorem.paragraph()
      },
      auth: "Basic " <> Base.encode64("#{username()}:#{password()}")
    ]
  end

  defp username do
    Application.get_env(:athena, :log_email_basic_auth)[:username]
  end

  defp password do
    Application.get_env(:athena, :log_email_basic_auth)[:password]
  end
end
