defmodule AthenaWeb.Workflows.SearchContactIndividualFilterTest do
  use AthenaWeb.ConnCase, async: true

  @contacts_graphql """
  query Contacts(
    $after: String
    $before: String
    $first: Int
    $last: Int
    $options: OptionsInput
  ) {
    contacts(
      after: $after
      before: $before
      first: $first
      last: $last
      options: $options
    ) {
      edges {
        node {
          id
        }
      }

      options {
        filters {
          key
          value
        }
        orders {
          key
          value
        }
      }

      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }

      total(options: $options)
    }
  }
  """

  setup context do
    company_profile = company_profile()
    company_profile_user = company_profile_user(%{profile_id: company_profile.id})

    authenticated_conn = authenticate_company_user(context.conn, company_profile_user.user)

    {:ok,
     company_profile: company_profile,
     contacts_query: fn variables ->
       graphql_query(authenticated_conn,
         market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
         query: @contacts_graphql,
         variables: variables
       )
     end}
  end

  describe "Lead status filter" do
    setup %{company_profile: company_profile} do
      nominated_shareholder_contact =
        contact(company_profile, %{is_nominated_shareholder: true, nominated_shareholder_identified_at: utc_now()})

      %{contact: investor_lead_contact} = investor_user(company_profile)
      %{contact: shareholder_contact} = shareholding(:current, %{company_profile_id: company_profile.id})
      %{contact: past_shareholder_contact} = shareholding(:past, %{company_profile_id: company_profile.id})

      {:ok,
       investor_lead_contact: investor_lead_contact,
       nominated_shareholder_contact: nominated_shareholder_contact,
       past_shareholder_contact: past_shareholder_contact,
       shareholder_contact: shareholder_contact}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "shareholder_status", "value" => "none"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 4
    end

    test "Investor lead", %{contacts_query: contacts_query, investor_lead_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "shareholder_status", "value" => "not-shareholder"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Nominated shareholder", %{contacts_query: contacts_query, nominated_shareholder_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "shareholder_status", "value" => "nominated-shareholder"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Shareholder", %{contacts_query: contacts_query, shareholder_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "shareholder_status", "value" => "shareholder"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Past shareholder", %{contacts_query: contacts_query, past_shareholder_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "shareholder_status", "value" => "past-shareholder"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end
  end

  describe "Qualified investor filter" do
    setup %{company_profile: company_profile} do
      contact = contact(company_profile)

      nominated_investor = investor_user(company_profile)
      Gaia.Investors.update_user(nominated_investor, %{hnw_status: :nominated_without_cert, hnw_identified_at: utc_now()})

      pending_investor = investor_user(company_profile)
      certificate(pending_investor, :pending_review)

      verified_investor = investor_user(company_profile)
      certificate(verified_investor, :verified)

      %{contact: hnw_contact} = shareholding(:hnw, %{company_profile_id: company_profile.id})

      {:ok,
       contact: contact,
       hnw_contact: hnw_contact,
       nominated_contact: nominated_investor.contact,
       pending_contact: pending_investor.contact,
       verified_contact: verified_investor.contact}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "none"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 5
    end

    test "Verified", %{contacts_query: contacts_query, verified_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "nominated_cert_verified"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Pending review", %{contacts_query: contacts_query, pending_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "nominated_cert_pending"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Self-nominated", %{contacts_query: contacts_query, nominated_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "nominated_without_cert"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "HNW behaviour", %{contacts_query: contacts_query, hnw_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "identified_via_behaviour"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Not indicated", %{contacts_query: contacts_query, contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "hnw_status", "value" => "not-indicated"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end
  end

  describe "Hub member filter" do
    setup %{company_profile: company_profile} do
      %{contact: hub_contact} = investor_user(company_profile)

      non_hub_contact_1 = contact(company_profile, %{imported_at: utc_now()})
      %{contact: non_hub_contact_2} = shareholding(:current, %{company_profile_id: company_profile.id})

      {:ok, hub_contact: hub_contact, non_hub_contacts: [non_hub_contact_1, non_hub_contact_2]}
    end

    test "No filter", %{contacts_query: contacts_query, non_hub_contacts: non_hub_contacts} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "has_investor_hub_user", "value" => "none"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1 + length(non_hub_contacts)
    end

    test "Hub member", %{contacts_query: contacts_query, hub_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "has_investor_hub_user", "value" => "linked-only"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Not a hub member", %{contacts_query: contacts_query, non_hub_contacts: non_hub_contacts} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "has_investor_hub_user", "value" => "unlinked-only"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == length(non_hub_contacts)
    end
  end

  describe "Tags filter" do
    setup %{company_profile: company_profile} do
      friendly_contact = contact(company_profile)
      institutional_contact = contact(company_profile, %{imported_at: utc_now()})
      %{contact: contact} = investor_user(company_profile)
      %{contact: super_advocate_contact} = shareholding(:current, %{company_profile_id: company_profile.id})

      tag(friendly_contact, %{name: "friendly"})
      tag(institutional_contact, %{name: "institutional"})
      tag(super_advocate_contact, %{name: "super advocate"})

      {:ok,
       contact: contact,
       friendly_contact: friendly_contact,
       institutional_contact: institutional_contact,
       super_advocate_contact: super_advocate_contact}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "tags", "value" => ""}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 4
    end

    test "Single tag", %{contacts_query: contacts_query, friendly_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "tags", "value" => "friendly"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Multiple tags", %{
      contacts_query: contacts_query,
      institutional_contact: institutional_contact,
      super_advocate_contact: super_advocate_contact
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "tags", "value" => "institutional,super advocate"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert institutional_contact.id in returned_contact_ids
      assert super_advocate_contact.id in returned_contact_ids
    end
  end

  describe "Location filter" do
    setup %{company_profile: company_profile} do
      act_contact = contact(company_profile, %{address_state: "ACT", address_country: "AUSTRALIA"})
      nsw_contact = contact(company_profile, %{address_state: "NSW", address_country: "AUSTRALIA"})
      nt_contact = contact(company_profile, %{address_state: "NT", address_country: "AUSTRALIA"})
      qld_contact = contact(company_profile, %{address_state: "QLD", address_country: "AUSTRALIA"})
      sa_contact = contact(company_profile, %{address_state: "SA", address_country: "AUSTRALIA"})
      tas_contact = contact(company_profile, %{address_state: "TAS", address_country: "AUSTRALIA"})
      vic_contact = contact(company_profile, %{address_state: "VIC", address_country: "AUSTRALIA"})
      wa_contact = contact(company_profile, %{address_state: "WA", address_country: "AUSTRALIA"})

      nz_contact = contact(company_profile, %{address_country: "NEW ZEALAND"})

      id_contact = contact(company_profile, %{address_country: "INDONESIA", imported_at: utc_now()})
      sg_contact = contact(company_profile, %{address_country: "SINGAPORE"})

      investor_user(company_profile, %{email: act_contact.email})
      shareholding(:current, %{email: nsw_contact.email})
      shareholding(:past, %{email: nt_contact.email})
      shareholding(:hnw, %{email: qld_contact.email})

      {:ok,
       contacts: [
         act_contact,
         id_contact,
         nsw_contact,
         nt_contact,
         nz_contact,
         qld_contact,
         sa_contact,
         sg_contact,
         tas_contact,
         vic_contact,
         wa_contact
       ]}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "location", "value" => ""}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 11
    end

    test "Single location - Australian state", %{contacts_query: contacts_query, contacts: contacts} do
      Enum.each(["act", "nsw", "nt", "qld", "sa", "tas", "vic", "wa"], fn state ->
        resp =
          contacts_query.(%{
            "first" => 10,
            "options" => %{"filters" => [%{"key" => "location", "value" => state}]}
          })

        assert get_in(resp, ["data", "contacts", "total"]) == 1

        returned_contact_id =
          resp
          |> get_in(["data", "contacts", "edges"])
          |> List.first()
          |> get_in(["node", "id"])
          |> String.to_integer()

        expected_contact_id =
          contacts
          |> Enum.find(&(&1.address_country == "AUSTRALIA" and &1.address_state == String.upcase(state)))
          |> Map.get(:id)

        assert returned_contact_id == expected_contact_id
      end)
    end

    test "Single location - New Zealand", %{contacts_query: contacts_query, contacts: contacts} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{"filters" => [%{"key" => "location", "value" => "nz"}]}
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      returned_contact_id =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> List.first()
        |> get_in(["node", "id"])
        |> String.to_integer()

      expected_contact_id =
        contacts
        |> Enum.find(&(&1.address_country == "NEW ZEALAND"))
        |> Map.get(:id)

      assert returned_contact_id == expected_contact_id
    end

    test "Single location - Other", %{contacts_query: contacts_query, contacts: contacts} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{"filters" => [%{"key" => "location", "value" => "other"}]}
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      expected_contact_ids =
        contacts
        |> Enum.filter(&(&1.address_country not in ["AUSTRALIA", "NEW ZEALAND"]))
        |> Enum.map(& &1.id)

      assert returned_contact_ids -- expected_contact_ids == []
      assert expected_contact_ids -- returned_contact_ids == []
    end

    test "Multiple locations", %{contacts_query: contacts_query, contacts: contacts} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "location", "value" => "vic,nz,other"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 4

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      expected_nz_contact_id =
        contacts
        |> Enum.find(&(&1.address_country == "NEW ZEALAND"))
        |> Map.get(:id)

      expected_vic_contact_id =
        contacts
        |> Enum.find(&(&1.address_country == "AUSTRALIA" and &1.address_state == "VIC"))
        |> Map.get(:id)

      expected_contact_ids =
        contacts
        |> Enum.filter(&(&1.address_country not in ["AUSTRALIA", "NEW ZEALAND"]))
        |> Enum.map(& &1.id)
        |> Kernel.++([expected_nz_contact_id, expected_vic_contact_id])

      assert returned_contact_ids -- expected_contact_ids == []
      assert expected_contact_ids -- returned_contact_ids == []
    end
  end

  describe "Email subscriptions filter" do
    setup %{company_profile: company_profile} do
      contact = contact(company_profile)

      %{contact: announcement_unsub_contact} =
        company_profile |> contact() |> contact_unsubscribe(%{scope: "announcement"})

      %{contact: general_unsub_contact} = company_profile |> contact() |> contact_unsubscribe(%{scope: "general"})
      %{contact: qa_unsub_contact} = company_profile |> contact() |> contact_unsubscribe(%{scope: "qa"})
      %{contact: update_unsub_contact} = company_profile |> contact() |> contact_unsubscribe(%{scope: "activity_update"})

      %{contact: global_unsub_contact} = company_profile |> contact() |> contact_global_unsubscribe()
      %{contact: suppressed_contact} = company_profile |> contact() |> contact_suppression()

      {:ok,
       contacts: [
         announcement_unsub_contact,
         contact,
         general_unsub_contact,
         global_unsub_contact,
         qa_unsub_contact,
         suppressed_contact,
         update_unsub_contact
       ],
       global_unsub_contact: global_unsub_contact,
       suppressed_contact: suppressed_contact}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "subscription_status", "value" => ""}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 7
    end

    test "Single subscription", %{
      contacts_query: contacts_query,
      contacts: contacts,
      global_unsub_contact: global_unsub_contact,
      suppressed_contact: suppressed_contact
    } do
      Enum.each(["activity_update", "announcement", "general", "qa"], fn scope ->
        resp =
          contacts_query.(%{
            "first" => 10,
            "options" => %{
              "filters" => [%{"key" => "subscription_status", "value" => scope}]
            }
          })

        assert get_in(resp, ["data", "contacts", "total"]) == 4

        returned_contact_ids =
          resp
          |> get_in(["data", "contacts", "edges"])
          |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

        unsub_contact_ids =
          [
            global_unsub_contact.id,
            suppressed_contact.id,
            contacts
            |> Enum.find(
              &Enum.any?(&1.comms_unsubscribes, fn comms_unsubscribe -> to_string(comms_unsubscribe.scope) == scope end)
            )
            |> Map.get(:id)
          ]

        assert returned_contact_ids -- unsub_contact_ids == returned_contact_ids
      end)
    end

    test "Unsubscribed globally", %{contacts_query: contacts_query, global_unsub_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "subscription_status", "value" => "global-unsubscribed"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Suppressed", %{contacts_query: contacts_query, suppressed_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "subscription_status", "value" => "suppressed"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Multiple subscriptions", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "subscription_status", "value" => "announcement,general"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 3
    end
  end

  describe "New shareholders filter" do
    setup %{company_profile: company_profile} do
      contact(company_profile)
      investor_user(company_profile)

      # churned in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      # downgraded in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 500, movement: -500, opening_balance: 500, settled_at: Date.add(today(), -71)}
      ])

      # held in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 500, movement: -500, opening_balance: 500, settled_at: Date.add(today(), -71)},
        %{closing_balance: 1_000, movement: 500, opening_balance: 500, settled_at: Date.add(today(), -69)}
      ])

      # no movement in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)}
      ])

      # upgraded in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 1_500, movement: 500, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      # contact_30 has one shareholding
      %{contact: contact_30} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})

      # contact_60 has two shareholdings
      %{contact: contact_60} =
        shareholding(%{company_profile_id: company_profile.id}, [
          %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -37)}
        ])

      shareholding(:past_30_days_churned, Map.take(contact_60, [:company_profile_id, :email]))

      # contact_90 has two shareholdings
      %{contact: contact_90} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})

      contact_90
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -75)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      {:ok, contact_30: contact_30, contact_60: contact_60, contact_90: contact_90}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "newholder_status", "value" => "none"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 10
    end

    test "Past 30 days", %{contacts_query: contacts_query, contact_30: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "newholder_status", "value" => "30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Past 60 days", %{contacts_query: contacts_query, contact_30: contact_30, contact_60: contact_60} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "newholder_status", "value" => "60"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_30.id in returned_contact_ids
      assert contact_60.id in returned_contact_ids
    end

    test "Past 90 days", %{
      contacts_query: contacts_query,
      contact_30: contact_30,
      contact_60: contact_60,
      contact_90: contact_90
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "newholder_status", "value" => "90"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 3

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_30.id in returned_contact_ids
      assert contact_60.id in returned_contact_ids
      assert contact_90.id in returned_contact_ids
    end
  end

  describe "Trading activity filter" do
    setup %{company_profile: company_profile} do
      contact(company_profile)
      investor_user(company_profile)

      # churned in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      # downgraded in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 500, movement: -500, opening_balance: 500, settled_at: Date.add(today(), -71)}
      ])

      # held in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 500, movement: -500, opening_balance: 500, settled_at: Date.add(today(), -71)},
        %{closing_balance: 1_000, movement: 500, opening_balance: 500, settled_at: Date.add(today(), -69)}
      ])

      # no movement in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)}
      ])

      # upgraded in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 1_500, movement: 500, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      # contact_60 has two shareholdings - downgrader in last 30 days
      %{contact: contact_60} =
        shareholding(%{company_profile_id: company_profile.id}, [
          %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -37)}
        ])

      shareholding(:past_30_days_churned, Map.take(contact_60, [:company_profile_id, :email]))

      # contact_90 has two shareholdings - returning in last 30 days
      %{contact: contact_90} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})

      contact_90
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -75)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -71)}
      ])

      shareholding(:past_30_days_held, %{company_profile_id: company_profile.id})
      shareholding(:past_30_days_no_movement, %{company_profile_id: company_profile.id})

      %{contact: churned_30} = shareholding(:past_30_days_churned, %{company_profile_id: company_profile.id})
      %{contact: downgrader_30} = shareholding(:past_30_days_downgrader, %{company_profile_id: company_profile.id})
      %{contact: new_30} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})
      %{contact: returning_30} = shareholding(:past_30_days_returning, %{company_profile_id: company_profile.id})
      # upgrader_30 has two shareholdings
      %{contact: upgrader_30} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})
      shareholding(:past_30_days_no_movement, Map.take(upgrader_30, [:company_profile_id, :email]))

      {:ok,
       churned_30: churned_30,
       contact_60: contact_60,
       contact_90: contact_90,
       downgrader_30: downgrader_30,
       new_30: new_30,
       returning_30: returning_30,
       upgrader_30: upgrader_30}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "none"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 16
    end

    test "Churned in last 30 days", %{contacts_query: contacts_query, churned_30: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "churned,30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Downgrader in last 30 days", %{
      contacts_query: contacts_query,
      contact_60: contact_60,
      downgrader_30: downgrader_30
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "downgrader,30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_60.id in returned_contact_ids
      assert downgrader_30.id in returned_contact_ids
    end

    test "New in last 30 days", %{contacts_query: contacts_query, new_30: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "new,30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Returning in last 30 days", %{
      contacts_query: contacts_query,
      contact_90: contact_90,
      returning_30: returning_30
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "returning,30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_90.id in returned_contact_ids
      assert returning_30.id in returned_contact_ids
    end

    test "Upgrader in last 30 days", %{contacts_query: contacts_query, upgrader_30: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "upgrader,30"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Upgrader with custom date range", %{contacts_query: contacts_query, upgrader_30: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [%{"key" => "trading_activity", "value" => "upgrader,#{Date.add(today(), -30)},#{today()}"}]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end
  end

  describe "Holding size filter" do
    setup %{company_profile: company_profile} do
      contact(company_profile)
      investor_user(company_profile)

      %{contact: contact_0} = shareholding(:past_30_days_churned, %{company_profile_id: company_profile.id})

      %{contact: contact_20} =
        shareholding(%{company_profile_id: company_profile.id}, [
          %{closing_balance: 20, movement: 20, opening_balance: 0, settled_at: Date.add(today(), -100)}
        ])

      # contact_80 has three shareholdings
      %{contact: contact_80} =
        shareholding(%{company_profile_id: company_profile.id}, [
          %{closing_balance: 10, movement: 10, opening_balance: 0, settled_at: Date.add(today(), -100)},
          %{closing_balance: 30, movement: 20, opening_balance: 10, settled_at: Date.add(today(), -85)},
          %{closing_balance: 20, movement: -10, opening_balance: 30, settled_at: Date.add(today(), -70)}
        ])

      contact_80
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 100, movement: 100, opening_balance: 0, settled_at: Date.add(today(), -90)},
        %{closing_balance: 0, movement: -100, opening_balance: 100, settled_at: Date.add(today(), -5)}
      ])

      contact_80
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 100, movement: 100, opening_balance: 0, settled_at: Date.add(today(), -80)},
        %{closing_balance: 20, movement: -80, opening_balance: 100, settled_at: Date.add(today(), -65)},
        %{closing_balance: 60, movement: 40, opening_balance: 20, settled_at: Date.add(today(), -5)}
      ])

      %{contact: contact_100} =
        shareholding(%{company_profile_id: company_profile.id}, [
          %{closing_balance: 100, movement: 100, opening_balance: 0, settled_at: Date.add(today(), -88)}
        ])

      {:ok, contact_0: contact_0, contact_20: contact_20, contact_80: contact_80, contact_100: contact_100}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "min_share_count", "value" => "undefined"},
              %{"key" => "max_share_count", "value" => "undefined"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 6
    end

    test "20 - 80", %{contacts_query: contacts_query, contact_20: contact_20, contact_80: contact_80} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "min_share_count", "value" => "20"},
              %{"key" => "max_share_count", "value" => "80"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_20.id in returned_contact_ids
      assert contact_80.id in returned_contact_ids
    end

    test "Max 20", %{contacts_query: contacts_query, contact_0: contact_0, contact_20: contact_20} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "min_share_count", "value" => "undefined"},
              %{"key" => "max_share_count", "value" => "20"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_0.id in returned_contact_ids
      assert contact_20.id in returned_contact_ids
    end

    test "Min 80", %{contacts_query: contacts_query, contact_80: contact_80, contact_100: contact_100} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "min_share_count", "value" => "80"},
              %{"key" => "max_share_count", "value" => "undefined"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      returned_contact_ids =
        resp
        |> get_in(["data", "contacts", "edges"])
        |> Enum.map(&(&1 |> get_in(["node", "id"]) |> String.to_integer()))

      assert contact_80.id in returned_contact_ids
      assert contact_100.id in returned_contact_ids
    end
  end
end
