defmodule AthenaWeb.Workflows.SearchContactCombinationFilterTest do
  use AthenaWeb.ConnCase, async: true

  @contacts_graphql """
  query Contacts(
    $after: String
    $before: String
    $first: Int
    $last: Int
    $options: OptionsInput
  ) {
    contacts(
      after: $after
      before: $before
      first: $first
      last: $last
      options: $options
    ) {
      edges {
        node {
          id
          email
        }
      }

      options {
        filters {
          key
          value
        }
        orders {
          key
          value
        }
      }

      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }

      total(options: $options)
    }
  }
  """

  setup context do
    company_profile = company_profile()
    company_profile_user = company_profile_user(%{profile_id: company_profile.id})

    authenticated_conn = authenticate_company_user(context.conn, company_profile_user.user)

    {:ok,
     company_profile: company_profile,
     contacts_query: fn variables ->
       graphql_query(authenticated_conn,
         market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
         query: @contacts_graphql,
         variables: variables
       )
     end}
  end

  describe "Combination of lead status, qualified investor, hub member, tags and location" do
    setup %{company_profile: company_profile} do
      # Investor lead + hub member
      %{contact: investor_lead_hub_member_contact} = investor_user(company_profile)

      # Nominated shareholder + hub member
      company_profile
      |> contact(%{is_nominated_shareholder: true, nominated_shareholder_identified_at: utc_now()})
      |> Map.take([:company_profile_id, :email])
      |> investor_user()

      # Nominated shareholder + keen tag + location VIC
      %{contact: nominated_shareholder_keen_tag_location_vic_contact} =
        company_profile
        |> contact(%{
          address_country: "AUSTRALIA",
          address_state: "VIC",
          is_nominated_shareholder: true,
          nominated_shareholder_identified_at: utc_now()
        })
        |> tag(%{name: "keen"})

      # Investor lead + location VIC
      contact(company_profile, %{address_country: "AUSTRALIA", address_state: "VIC"})

      # Investor lead
      contact(company_profile, %{imported_at: utc_now()})

      # Investor lead + keen tag
      company_profile
      |> contact()
      |> tag(%{name: "keen"})

      # Past shareholder + hub member
      company_profile
      |> investor_user()
      |> Map.get(:contact)
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -50)}
      ])

      # Shareholder + hub member
      :current
      |> shareholding(%{company_profile_id: company_profile.id})
      |> Map.get(:contact)
      |> Map.take([:company_profile_id, :email])
      |> investor_user()

      # Shareholder + location VIC
      {:ok, shareholder_location_vic_contact} =
        :current
        |> shareholding(%{company_profile_id: company_profile.id})
        |> Map.get(:contact)
        |> Gaia.Contacts.update_contact(%{address_country: "AUSTRALIA", address_state: "VIC"})

      # Shareholder + location NSW
      :current
      |> shareholding(%{company_profile_id: company_profile.id})
      |> Map.get(:contact)
      |> Gaia.Contacts.update_contact(%{address_country: "AUSTRALIA", address_state: "NSW"})

      # Qualified investor + holding size 0 (past shareholder)
      qualified_holding_0_investor = investor_user(company_profile)
      certificate(qualified_holding_0_investor, :verified)

      qualified_holding_0_investor
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 50, movement: 50, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 0, movement: -50, opening_balance: 50, settled_at: Date.add(today(), -75)}
      ])

      # Qualified investor + holding size 100
      qualified_holding_100_investor = investor_user(company_profile)
      certificate(qualified_holding_100_investor, :verified)

      %{contact: qualified_holding_100_contact} =
        qualified_holding_100_investor
        |> Map.take([:company_profile_id, :email])
        |> shareholding([
          %{closing_balance: 50, movement: 50, opening_balance: 0, settled_at: Date.add(today(), -100)},
          %{closing_balance: 100, movement: 50, opening_balance: 50, settled_at: Date.add(today(), -75)}
        ])

      # Qualified investor + holding size 20_000
      qualified_holding_20000_investor = investor_user(company_profile)
      certificate(qualified_holding_20000_investor, :verified)

      qualified_holding_20000_investor
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 20_000, movement: 20_000, opening_balance: 0, settled_at: Date.add(today(), -100)}
      ])

      # Pending review investor + holding size 100
      pending_holding_100_investor = investor_user(company_profile)
      certificate(pending_holding_100_investor, :pending_review)

      pending_holding_100_investor
      |> Map.take([:company_profile_id, :email])
      |> shareholding([
        %{closing_balance: 50, movement: 50, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 100, movement: 50, opening_balance: 50, settled_at: Date.add(today(), -75)}
      ])

      {:ok,
       investor_lead_hub_member_contact: investor_lead_hub_member_contact,
       nominated_shareholder_keen_tag_location_vic_contact: nominated_shareholder_keen_tag_location_vic_contact,
       qualified_holding_100_contact: qualified_holding_100_contact,
       shareholder_location_vic_contact: shareholder_location_vic_contact,
       company_profile: company_profile}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => []
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 14
    end

    test "Investor lead + hub member", %{contacts_query: contacts_query, investor_lead_hub_member_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "has_investor_hub_user", "value" => "linked-only"},
              %{"key" => "shareholder_status", "value" => "not-shareholder"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Nominated shareholder + tag", %{
      contacts_query: contacts_query,
      nominated_shareholder_keen_tag_location_vic_contact: contact
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "tags", "value" => "keen"},
              %{"key" => "shareholder_status", "value" => "nominated-shareholder"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "ensure it returns only one contact even when in 2 tags", %{
      contacts_query: contacts_query,
      nominated_shareholder_keen_tag_location_vic_contact: contact,
      company_profile: company_profile
    } do
      st_1 = static_list(%{company_profile_id: company_profile.id})
      st_2 = static_list(%{company_profile_id: company_profile.id})

      static_list_member(%{contact_id: contact.id, static_list_id: st_1.id})
      static_list_member(%{contact_id: contact.id, static_list_id: st_2.id})

      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "static_list_ids", "value" => "#{st_1.id},#{st_2.id}"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "ensure it returns contacts with a valid contact source", %{
      conn: conn
    } do
      # Creating the company here so I can test with the exact data I need.
      # Setup was already generating some contacts with different sources.
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      %_{id: id_1} = contact(%{company_profile_id: company_profile.id, contact_source: :manual_creation})
      %_{id: id_2} = contact(%{company_profile_id: company_profile.id, contact_source: :registry_import})

      expected = Enum.sort(["#{id_1}", "#{id_2}"])

      resp =
        conn
        |> authenticate_company_user(company_profile_user.user)
        |> graphql_query(
          market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
          query: @contacts_graphql,
          variables: %{
            "first" => 10,
            "options" => %{
              "filters" => [
                %{"key" => "sources", "value" => "manual_creation,registry_import"}
              ]
            }
          }
        )

      assert get_in(resp, ["data", "contacts", "total"]) == 2

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> Enum.map(&get_in(&1, ["node", "id"]))
             |> Enum.sort()
             |> Kernel.==(expected)
    end

    test "ensure it returns contacts with null emails for a given filter" do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      %_{id: id_1} = contact(%{company_profile_id: company_profile.id, contact_source: :manual_creation})
      %_{id: id_2} = contact(%{company_profile_id: company_profile.id, contact_source: :registry_import})

      {:ok, %_{id: id_3}} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          contact_source: :manual_creation,
          first_name: Faker.Person.first_name()
        })

      expected = Enum.sort(["#{id_1}", "#{id_2}", "#{id_3}"])

      vars = %{
        "first" => 10,
        "options" => %{
          "filters" => [
            %{"key" => "sources", "value" => "manual_creation,registry_import"}
          ]
        }
      }

      %{data: data} =
        resp =
        Absinthe.run!(@contacts_graphql, AthenaWeb.Schema,
          variables: vars,
          context: %{current_company_profile_user: company_profile_user}
        )

      refute Map.has_key?(resp, :errors)

      assert get_in(data, ["contacts", "total"]) == 3

      assert data
             |> get_in(["contacts", "edges"])
             |> Enum.map(&get_in(&1, ["node", "id"]))
             |> Enum.sort()
             |> Kernel.==(expected)

      null_item =
        data
        |> get_in(["contacts", "edges"])
        |> Enum.find(&(get_in(&1, ["node", "id"]) == "#{id_3}"))

      assert is_nil(null_item["email"])
    end

    test "Shareholder + location", %{contacts_query: contacts_query, shareholder_location_vic_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "shareholder_status", "value" => "shareholder"},
              %{"key" => "location", "value" => "vic"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Qualified investor + holding size", %{contacts_query: contacts_query, qualified_holding_100_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "hnw_status", "value" => "nominated_cert_verified"},
              %{"key" => "min_share_count", "value" => "100"},
              %{"key" => "max_share_count", "value" => "10000"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end
  end

  describe "Combination of qualified investor, hub member, new shareholders and trading activity" do
    setup %{company_profile: company_profile} do
      # HNW + returning in the past 180 days
      %{contact: hnw_returning_180_contact} =
        :hnw
        |> shareholding(%{company_profile_id: company_profile.id})
        |> Map.take([:company_profile_id, :email])
        |> shareholding([
          %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -200)},
          %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -198)},
          %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -150)}
        ])

      # HNW + upgrader in the past 30 days
      %{contact: hnw_upgrader_30_contact} =
        shareholding(
          :past_30_days_upgrader,
          :hnw
          |> shareholding(%{company_profile_id: company_profile.id})
          |> Map.take([:company_profile_id, :email])
        )

      # Hub member + churned in the past 30 days
      %{contact: hub_member_churned_30_contact} =
        :past_30_days_churned
        |> shareholding(%{company_profile_id: company_profile.id})
        |> Map.take([:company_profile_id, :email])
        |> investor_user()

      # Hub member + downgrader in the past 90 days
      %{contact: hub_member_downgrader_90_contact} =
        company_profile
        |> investor_user()
        |> Map.take([:company_profile_id, :email])
        |> shareholding([
          %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
          %{closing_balance: 500, movement: -500, opening_balance: 1_000, settled_at: Date.add(today(), -80)}
        ])

      # Hub member + new shareholders in the past 30 days
      %{contact: hub_member_new_30_contact} =
        :past_30_days_new
        |> shareholding(%{company_profile_id: company_profile.id})
        |> Map.take([:company_profile_id, :email])
        |> investor_user()

      # Hub member
      investor_user(company_profile)

      # HNW
      shareholding(:hnw, %{company_profile_id: company_profile.id})

      # Returning in the past 180 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -200)},
        %{closing_balance: 0, movement: -1_000, opening_balance: 1_000, settled_at: Date.add(today(), -198)},
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -150)}
      ])

      # Upgrader in the past 30 days
      shareholding(:past_30_days_upgrader, %{company_profile_id: company_profile.id})

      # Churned in the past 30 days
      shareholding(:past_30_days_churned, %{company_profile_id: company_profile.id})

      # Downgrader in the past 90 days
      shareholding(%{company_profile_id: company_profile.id}, [
        %{closing_balance: 1_000, movement: 1_000, opening_balance: 0, settled_at: Date.add(today(), -100)},
        %{closing_balance: 500, movement: -500, opening_balance: 1_000, settled_at: Date.add(today(), -80)}
      ])

      # New shareholders in the past 30 days
      shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})

      {:ok,
       hnw_returning_180_contact: hnw_returning_180_contact,
       hnw_upgrader_30_contact: hnw_upgrader_30_contact,
       hub_member_churned_30_contact: hub_member_churned_30_contact,
       hub_member_downgrader_90_contact: hub_member_downgrader_90_contact,
       hub_member_new_30_contact: hub_member_new_30_contact}
    end

    test "No filter", %{contacts_query: contacts_query} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => []
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 12
    end

    test "Hub member + new shareholders", %{contacts_query: contacts_query, hub_member_new_30_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "has_investor_hub_user", "value" => "linked-only"},
              %{"key" => "newholder_status", "value" => "30"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "HNW + returning (trading activity)", %{contacts_query: contacts_query, hnw_returning_180_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "hnw_status", "value" => "identified_via_behaviour"},
              %{"key" => "trading_activity", "value" => "returning,180"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Hub member + downgrader (trading activity)", %{
      contacts_query: contacts_query,
      hub_member_downgrader_90_contact: contact
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "has_investor_hub_user", "value" => "linked-only"},
              %{"key" => "trading_activity", "value" => "downgrader,90"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "HNW + upgrader (trading activity)", %{contacts_query: contacts_query, hnw_upgrader_30_contact: contact} do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "hnw_status", "value" => "identified_via_behaviour"},
              %{"key" => "trading_activity", "value" => "upgrader,30"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end

    test "Hub member + churned (trading activity)", %{
      contacts_query: contacts_query,
      hub_member_churned_30_contact: contact
    } do
      resp =
        contacts_query.(%{
          "first" => 10,
          "options" => %{
            "filters" => [
              %{"key" => "has_investor_hub_user", "value" => "linked-only"},
              %{"key" => "trading_activity", "value" => "churned,30"}
            ]
          }
        })

      assert get_in(resp, ["data", "contacts", "total"]) == 1

      assert resp
             |> get_in(["data", "contacts", "edges"])
             |> List.first()
             |> get_in(["node", "id"])
             |> Kernel.==("#{contact.id}")
    end
  end
end
