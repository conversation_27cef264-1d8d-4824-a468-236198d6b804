defmodule AthenaWeb.Resolvers.Comms.EstimateEmailRecipientsCountTest do
  use AthenaWeb.ConnCase

  @query """
  query EstimateEmailRecipientsCount($emailId: ID!) {
    estimateEmailRecipientsCount(emailId: $emailId)
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      [
        company_profile: company_profile,
        logged_in_user: logged_in_user
      ]
    end

    test "ensure returns the estimated email recipients count without the null email contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      email = email(%{company_profile_id: company_profile.id, send_to_all_contacts: true})

      contact(company_profile)

      Gaia.Contacts.create_contact(%{
        company_profile_id: company_profile.id,
        contact_source: :manual_creation,
        first_name: Faker.Person.first_name()
      })

      %{"data" => %{"estimateEmailRecipientsCount" => count}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"emailId" => email.id}
        )

      assert count == 1
    end

    test "ensure returns the estimated email recipients count if static list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contact = contact(company_profile)

      {:ok, contact2} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          contact_source: :manual_creation,
          first_name: Faker.Person.first_name()
        })

      static_list_input = %{
        name: "Test Static List",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000",
        contactIds: [contact.id, contact2.id]
      }

      static_list_mutation = """
        mutation CreateStaticList($staticList: StaticListInput!) {
          createStaticList(staticList: $staticList) {
          id
          name
          description
          backgroundColor
          textColor
        }
      }
      """

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: static_list_mutation,
          variables: %{"staticList" => static_list_input}
        )

      static_list = response["data"]["createStaticList"]
      assert Gaia.Contacts.get_static_list_member_count(static_list["id"]) == 2

      email =
        email(%{
          company_profile_id: company_profile.id,
          send_to_all_contacts: false,
          send_to_static_list_ids: [static_list["id"]]
        })

      %{"data" => %{"estimateEmailRecipientsCount" => count}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"emailId" => email.id}
        )

      assert count == 1
    end

    test "ensure returns the estimated email recipients count if dynamic list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      attrs = %{company_profile_id: company_profile.id}

      {:ok, contact_1} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          contact_source: :manual_creation,
          first_name: Faker.Person.first_name()
        })

      {:ok, contact_2} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          contact_source: :manual_creation,
          first_name: Faker.Person.first_name()
        })

      shareholding(:past_30_days_new, attrs)
      shareholding(:past_30_days_new, attrs)
      shareholding(:past_30_days_new, attrs)
      shareholding(:past_30_days_new, attrs)

      # those do not count towards the estimated email recipients count
      shareholding(:past_30_days_new, Map.put(attrs, :contact_id, contact_1.id))
      shareholding(:past_30_days_new, Map.put(attrs, :contact_id, contact_2.id))

      dynamic_list =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 6,
          filters: [%{key: "trading_activity", value: "new,30"}],
          name: "New shareholders in the last 30 days"
        })

      email =
        email(%{
          company_profile_id: company_profile.id,
          send_to_all_contacts: false,
          send_to_dynamic_list_ids: [dynamic_list.id]
        })

      %{"data" => %{"estimateEmailRecipientsCount" => count}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"emailId" => email.id}
        )

      assert count == 4
    end

    test "ensure returns the estimated email recipients count if comms list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contact = contact(company_profile)

      {:ok, contact2} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          contact_source: :manual_creation,
          first_name: Faker.Person.first_name()
        })

      email =
        email(%{
          company_profile_id: company_profile.id,
          send_to_all_contacts: false,
          send_to_contact_ids: [contact.id, contact2.id]
        })

      %{"data" => %{"estimateEmailRecipientsCount" => count}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"emailId" => email.id}
        )

      assert count == 1
    end
  end
end
