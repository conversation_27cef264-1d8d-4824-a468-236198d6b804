defmodule AthenaWeb.Resolvers.Contacts.ContactLatestEngagementActivityTest do
  use AthenaWeb.ConnCase

  @query """
  query ContactLatestEngagementActivity($contactId: ID!) {
    contactLatestEngagementActivity(contactId: $contactId) {
      id
      type
      timestamp
      metadata
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      [
        company_profile: company_profile,
        logged_in_user: logged_in_user
      ]
    end

    test "returns latest engagement activity for contact without investorhub account", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      # Enable the CRM refactor feature flag for this company
      {:ok, _} = FunWithFlags.enable(:crm_refactor_profile, for_actor: company_profile)

      # Create a contact without an investor (no investorhub account)
      contact = contact(company_profile)

      # Make the GraphQL query
      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"contactId" => contact.id}
        )

      # The query should not crash and should return a response
      # Even if there's no activity, it should return nil or empty result gracefully
      assert %{"data" => %{"contactLatestEngagementActivity" => activity}} = response

      # Activity could be nil if no engagement activities exist
      # The important thing is that the query doesn't crash with an Ecto.SubQueryError
      assert is_nil(activity) or is_map(activity)
    end

    test "returns latest engagement activity for contact with investorhub account", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      # Enable the CRM refactor feature flag for this company
      {:ok, _} = FunWithFlags.enable(:crm_refactor_profile, for_actor: company_profile)

      # Create a contact with an investor (has investorhub account)
      investor_user = investor_user(company_profile)
      contact = contact(company_profile, %{investor_id: investor_user.id})

      # Make the GraphQL query
      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"contactId" => contact.id}
        )

      # The query should not crash and should return a response
      assert %{"data" => %{"contactLatestEngagementActivity" => activity}} = response

      # Activity could be nil if no engagement activities exist
      # The important thing is that the query doesn't crash
      assert is_nil(activity) or is_map(activity)
    end

    test "returns error when contact not found", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      # Use a non-existent contact ID
      non_existent_contact_id = "999999"

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"contactId" => non_existent_contact_id}
        )

      # Should return an error for non-existent contact
      assert %{"errors" => [%{"message" => "Contact not found"}]} = response
    end
  end
end
