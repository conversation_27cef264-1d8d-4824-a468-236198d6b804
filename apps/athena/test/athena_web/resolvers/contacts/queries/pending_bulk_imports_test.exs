defmodule AthenaWeb.Resolvers.Queries.PendingBulkImportsTest do
  use AthenaWeb.ConnCase

  import Gaia.BulkImportFixtures

  @query """
  query PendingBulkImports {
    pendingBulkImports {
      id
      insertedAt
      contactsCount
      uploaderProfileUser {
        id
        user {
          id
          firstName
          lastName
        }
      }
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
      uploader_profile_user = company_profile_user(%{profile_id: company_profile.id})
      bulk_import = bulk_import_fixture(company_profile, %{uploader_profile_user_id: uploader_profile_user.id})

      # Create 10 contacts for the bulk import
      Enum.each(1..10, fn _ ->
        bulk_import_contacts_fixture(bulk_import)
      end)

      {:ok,
       company_profile: company_profile,
       logged_in_user: logged_in_user,
       bulk_import: bulk_import,
       uploader_profile_user: uploader_profile_user}
    end

    test "successfully returns the shareholder offer", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      uploader_profile_user: uploader_profile_user
    } do
      %{"data" => %{"pendingBulkImports" => data}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{}
        )

      first_bulk_import = List.first(data)
      assert first_bulk_import["contactsCount"] == 10
      assert first_bulk_import["uploaderProfileUser"]["user"]["firstName"] == uploader_profile_user.user.first_name
      assert first_bulk_import["uploaderProfileUser"]["user"]["lastName"] == uploader_profile_user.user.last_name
    end
  end
end
