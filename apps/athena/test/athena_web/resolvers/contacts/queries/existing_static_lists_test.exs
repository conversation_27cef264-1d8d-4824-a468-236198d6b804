defmodule AthenaWeb.Resolvers.Contacts.Queries.ExistingStaticListsTest do
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @query """
  query ExistingStaticLists {
    existingStaticLists {
      id
      name
      backgroundColor
      textColor
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_profile_user = company_profile_user(%{profile_id: company_profile.id})
      logged_in_user = logged_in_profile_user.user

      [company_profile: company_profile, logged_in_user: logged_in_user, logged_in_profile_user: logged_in_profile_user]
    end

    test "ensure successfully returns the existing static lists", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      logged_in_profile_user: logged_in_profile_user
    } do
      {:ok, static_list} =
        Contacts.create_static_list(%{
          name: "Test Static List",
          company_profile_id: company_profile.id,
          last_updated_by_profile_user_id: logged_in_profile_user.id,
          background_color: "#FFFFFF",
          text_color: "#000000"
        })

      %{"data" => %{"existingStaticLists" => [expected]}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{}
        )

      assert expected["backgroundColor"] == static_list.background_color
      assert expected["id"] == "#{static_list.id}"
      assert expected["name"] == static_list.name
      assert expected["textColor"] == static_list.text_color
    end

    test "ensure only returns the existing static lists for current company", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      logged_in_profile_user: logged_in_profile_user
    } do
      another_company_profile = company_profile()

      {:ok, static_list} =
        Contacts.create_static_list(%{
          name: "Test Static List",
          company_profile_id: company_profile.id,
          last_updated_by_profile_user_id: logged_in_profile_user.id,
          background_color: "#FFFFFF",
          text_color: "#000000"
        })

      Contacts.create_static_list(%{
        name: "I am another list",
        company_profile_id: another_company_profile.id,
        last_updated_by_profile_user_id: logged_in_profile_user.id,
        background_color: "#8989",
        text_color: "#234234"
      })

      %{"data" => %{"existingStaticLists" => [expected]}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{}
        )

      assert expected["backgroundColor"] == static_list.background_color
      assert expected["id"] == "#{static_list.id}"
      assert expected["name"] == static_list.name
      assert expected["textColor"] == static_list.text_color
    end

    test "ensure only returns non invalidated lists", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      logged_in_profile_user: logged_in_profile_user
    } do
      {:ok, static_list} =
        Contacts.create_static_list(%{
          name: "Test Static List",
          company_profile_id: company_profile.id,
          last_updated_by_profile_user_id: logged_in_profile_user.id,
          background_color: "#FFFFFF",
          text_color: "#000000"
        })

      {:ok, to_invalidate} =
        Contacts.create_static_list(%{
          name: "Invalidated list",
          company_profile_id: company_profile.id,
          last_updated_by_profile_user_id: logged_in_profile_user.id,
          background_color: "#8989",
          text_color: "#234234"
        })

      Contacts.invalidate_static_list(to_invalidate, %{})

      %{"data" => %{"existingStaticLists" => [expected]}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{}
        )

      assert expected["backgroundColor"] == static_list.background_color
      assert expected["id"] == "#{static_list.id}"
      assert expected["name"] == static_list.name
      assert expected["textColor"] == static_list.text_color
    end
  end
end
