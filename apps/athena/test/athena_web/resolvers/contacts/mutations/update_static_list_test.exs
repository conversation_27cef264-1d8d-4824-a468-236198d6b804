defmodule AthenaWeb.Resolvers.Contacts.Mutations.UpdateStaticList do
  @moduledoc false
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @query """
    mutation UpdateStaticList($id: ID!, $staticList: StaticListInput!) {
      updateStaticList(id: $id, staticList: $staticList) {
        id
        name
        description
        totalMembers
        membersContactIds
        textColor
        backgroundColor
        lastUpdatedByProfileUser {
          user {
            id
            email
            firstName
            lastName
          }
        }
        lastUsedOnEmail {
          id
          campaignName
        }
        companyProfile {
          id
        }
        insertedAt
        updatedAt
      }
    }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
      contact1 = contact(company_profile)
      contact2 = contact(company_profile)

      static_list = static_list_with_contacts(%{company_profile_id: company_profile.id}, [contact1.id, contact2.id])

      [
        company_profile: company_profile,
        logged_in_user: logged_in_user,
        contact1: contact1,
        contact2: contact2,
        static_list: static_list
      ]
    end

    test "update a static list if there are no conflicts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      static_list: static_list
    } do
      static_list_input = %{
        name: "Test Static List Name",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000"
      }

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"id" => static_list.id, "staticList" => static_list_input}
        )

      assert response["data"]["updateStaticList"]["name"] == "Test Static List Name"
      assert response["data"]["updateStaticList"]["description"] == "Test Static List Description"
      assert response["data"]["updateStaticList"]["backgroundColor"] == "#FFFFFF"
      assert response["data"]["updateStaticList"]["textColor"] == "#000000"
    end

    test "does not update a static list's name if that name is already taken by another static list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      static_list: static_list
    } do
      static_list2 = static_list(%{company_profile_id: company_profile.id})

      static_list_input = %{
        name: static_list.name,
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000"
      }

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"id" => static_list2.id, "staticList" => static_list_input}
        )

      assert response["errors"] != nil
    end

    test "removes existing members from the static list if they are excluded from the contact_ids input list, and add contact ids which were not existing members to the static list as new members",
         %{
           conn: conn,
           company_profile: company_profile,
           logged_in_user: logged_in_user,
           contact1: contact1,
           contact2: contact2,
           static_list: static_list
         } do
      contact3 = contact(company_profile)

      static_list_input = %{
        name: static_list.name,
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000",
        contactIds: [contact1.id, contact3.id]
      }

      %{"data" => %{"updateStaticList" => %{"totalMembers" => total_members, "membersContactIds" => members_contact_ids}}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"id" => static_list.id, "staticList" => static_list_input}
        )

      assert contact2.id not in members_contact_ids

      assert company_profile.id
             |> Contacts.get_static_list_member_contact_ids_from_static_list_ids([static_list.id])
             |> Enum.sort() ==
               members_contact_ids |> Enum.map(&String.to_integer(&1)) |> Enum.sort()

      assert members_contact_ids |> Enum.at(0) |> String.to_integer() == contact1.id
      assert total_members == 2
    end
  end
end
