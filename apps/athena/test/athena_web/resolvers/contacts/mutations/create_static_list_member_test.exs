defmodule AthenaWeb.Resolvers.Contacts.Mutations.CreateStaticListMemberTest do
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @query """
    mutation CreateStaticListMember($staticListId: ID!, $contactId: ID!) {
      createStaticListMember(staticListId: $staticListId, contactId: $contactId) {
        id
        staticList {
          id
          name
        }
        contact {
          id
        }
      }
    }
  """

  test "successfully add a new member to a static list", %{conn: conn} do
    company_profile = company_profile()
    logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

    contact = contact(company_profile)
    static_list = static_list(%{company_profile_id: company_profile.id})

    [company_profile: company_profile, logged_in_user: logged_in_user, contact: contact, static_list: static_list]

    %{
      "data" => %{"createStaticListMember" => %{"id" => res_id}}
    } =
      conn
      |> authenticate_company_user(logged_in_user)
      |> graphql_query(
        market_listing_key: company_profile.ticker.listing_key,
        query: @query,
        variables: %{"staticListId" => static_list.id, "contactId" => contact.id}
      )

    assert Contacts.get_static_list_member_by(%{static_list_id: static_list.id, contact_id: contact.id}).id ==
             String.to_integer(res_id)

    assert Contacts.get_static_list_member_count(static_list.id) == 1
  end
end
