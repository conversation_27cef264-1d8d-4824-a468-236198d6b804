defmodule AthenaWeb.Resolvers.Contacts.UpsertCustomContactsTest do
  use AthenaWeb.ConnCase

  import Ecto.Query

  alias Gaia.Contacts.Contact
  alias Gaia.FeatureFlags
  alias Gaia.Repo

  @query """
  mutation UpsertCustomContacts($customContacts: [ContactInput!]!, $audienceTags: [String!]!, $clientAnswerListSource: String, $clientAnswerLastUsage: String, $isGlobalUnsubscribe: Boolean!, $unsubscribeScopes: [String!]!, $applySubscriptionToNewContactOnly: Boolean!) {
    upsertCustomContacts(
      customContacts: $customContacts
      audienceTags: $audienceTags
      clientAnswerListSource: $clientAnswerListSource
      clientAnswerLastUsage: $clientAnswerLastUsage
      isGlobalUnsubscribe: $isGlobalUnsubscribe
      unsubscribeScopes: $unsubscribeScopes
      applySubscriptionToNewContactOnly: $applySubscriptionToNewContactOnly
    )
  }
  """

  def build_contacts_to_import(count) do
    Enum.map(1..count, fn _ ->
      %{
        email: Faker.Internet.email(),
        firstName: Faker.Person.first_name(),
        lastName: Faker.Person.last_name()
      }
    end)
  end

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      {:ok, company_profile: company_profile, logged_in_user: logged_in_user}
    end

    test "successfully imports the contacts with new tags / static lists", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      admin_user = admin_user()

      FeatureFlags.enable(admin_user, %{flag_name: :static_lists, description: ""})

      contacts_to_import = build_contacts_to_import(1)

      tags = ["tag1", "tag2"]

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:static_lists]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      first_contact = Enum.at(contacts_in_db, 0)

      assert Enum.all?(tags, fn tag -> tag in Enum.map(first_contact.static_lists, & &1.name) end)
      assert first_contact.first_name == Enum.at(contacts_to_import, 0).firstName
      assert first_contact.last_name == Enum.at(contacts_to_import, 0).lastName
      assert first_contact.email == Enum.at(contacts_to_import, 0).email
      assert first_contact.imported_at != nil
      assert first_contact.lead_identified_at != nil
      assert first_contact.creator_user_id == logged_in_user.id
      assert first_contact.creator_name == Gaia.Companies.User.build_creator_name(logged_in_user)
      assert first_contact.contact_source == :bulk_import

      assert_enqueued(
        worker: Gaia.Jobs.VerifyContactEmails,
        args: %{"company_profile_id" => company_profile.id}
      )

      refute_email_sent("EmailTransactional.Operations", "new_bulk_import")
    end

    test "contacts created via upsert_custom_contacts all have a contact_source value of :bulk_import",
         %{
           conn: conn,
           company_profile: company_profile,
           logged_in_user: logged_in_user
         } do
      admin_user = admin_user()
      FeatureFlags.enable(admin_user, %{flag_name: :static_lists, description: ""})
      contacts_to_import = build_contacts_to_import(10)
      tags = ["tag1", "tag2"]

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:static_lists]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      Enum.each(contacts_in_db, fn contact ->
        assert Enum.all?(tags, fn tag -> tag in Enum.map(contact.static_lists, & &1.name) end)
        assert contact.contact_source == :bulk_import
      end)
    end

    # Technically, this should never happen since the frontend prevents uploading an empty list
    test "handles empty contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contacts_to_import = []

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => [],
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] == 0
    end

    test "re-imported contact does not have certain fields changed on conflict", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contacts_to_import = build_contacts_to_import(1)
      first_contact_to_import = Enum.at(contacts_to_import, 0)

      profile_user_2 =
        company_profile_user(%{
          profile_id: company_profile.id,
          id: logged_in_user.id + 1
        })

      company_user = Gaia.Companies.get_user_by(%{id: profile_user_2.user_id})

      existing_contact = %Gaia.Contacts.Contact{
        first_name: "Old_First_Name",
        last_name: "Old_Last_Name",
        email: first_contact_to_import.email,
        company_profile_id: company_profile.id,
        creator_user_id: profile_user_2.user_id,
        creator_name: Gaia.Companies.User.build_creator_name(company_user),
        lead_identified_at: NaiveDateTime.utc_now(:second),
        imported_at: NaiveDateTime.utc_now(:second),
        contact_source: :manual_creation
      }

      {:ok, existing_contact} = Gaia.Repo.insert(existing_contact)

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => [],
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:tags]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      re_imported_contact = Enum.at(contacts_in_db, 0)

      # Ensure the fields specified in :replace_all_except remain unchanged
      assert re_imported_contact.id == existing_contact.id
      assert re_imported_contact.inserted_at == existing_contact.inserted_at
      assert re_imported_contact.imported_at == existing_contact.imported_at
      assert re_imported_contact.lead_identified_at == existing_contact.lead_identified_at
      assert re_imported_contact.creator_user_id == existing_contact.creator_user_id
      assert re_imported_contact.creator_name == existing_contact.creator_name
      assert re_imported_contact.contact_source == existing_contact.contact_source

      # Ensure that other fields like `first_name`, `last_name` may have been updated as per the new bulk import
      assert re_imported_contact.first_name == first_contact_to_import.firstName
      assert re_imported_contact.last_name == first_contact_to_import.lastName
      assert re_imported_contact.email == first_contact_to_import.email
    end

    test "creates a bulk import if more than 500 contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contacts_to_import = build_contacts_to_import(501)

      tags = ["tag1", "tag2"]
      list_source = "Mailchimp"
      last_usage = :less_than_three_months

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false,
            "clientAnswerListSource" => list_source,
            "clientAnswerLastUsage" => Atom.to_string(last_usage)
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      bulk_import =
        Gaia.Contacts.BulkImport
        |> Repo.get_by(company_profile_id: company_profile.id)
        |> Repo.preload(:contacts)

      assert Gaia.Contacts.Contact |> Repo.all() |> length() == 0

      assert bulk_import.client_answer_list_source == list_source
      assert bulk_import.client_answer_last_usage == last_usage
      assert length(bulk_import.contacts) == length(contacts_to_import)

      assert_email_sent("EmailTransactional.Operations", "new_bulk_import")
    end

    test "Global unsubscribe contacts via importing contacts list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      admin_user = admin_user()

      FeatureFlags.enable(admin_user, %{flag_name: :static_lists, description: ""})

      contacts_to_import = build_contacts_to_import(1)

      tags = ["tag1", "tag2"]

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => true,
            "unsubscribeScopes" => [],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:comms_unsubscribes, :global_unsubscribe]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      first_contact = Enum.at(contacts_in_db, 0)

      assert first_contact.comms_unsubscribes == []
      assert first_contact.global_unsubscribe != nil
    end

    test "Bulk unsubscribe new contacts from certain scopes via importing contacts list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      admin_user = admin_user()

      FeatureFlags.enable(admin_user, %{flag_name: :static_lists, description: ""})

      contacts_to_import = build_contacts_to_import(1)

      tags = ["tag1", "tag2"]

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => false,
            "unsubscribeScopes" => ["ANNOUNCEMENT", "ACTIVITY_UPDATE", "GENERAL"],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:comms_unsubscribes, :global_unsubscribe]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      first_contact = Enum.at(contacts_in_db, 0)

      assert first_contact.comms_unsubscribes != []
      assert first_contact.global_unsubscribe == nil

      # If contact is non hub member, the default settings of all hub-related subscriptions are off
      # hub_related_scopes = [:activity_follow, :qa, :raises, :new_follower]
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :announcement)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :activity_update)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :general)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :activity_follow)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :qa)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :raises)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :new_follower)) != []
    end

    test "Bulk unsubscribe existing contacts from certain scopes via importing contacts list", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      contacts_to_import = build_contacts_to_import(1)
      first_contact_to_import = Enum.at(contacts_to_import, 0)

      profile_user_2 =
        company_profile_user(%{
          profile_id: company_profile.id,
          id: logged_in_user.id + 1
        })

      company_user = Gaia.Companies.get_user_by(%{id: profile_user_2.user_id})

      inserted_at = Timex.shift(NaiveDateTime.utc_now(:second), days: -1)

      existing_contact = %Gaia.Contacts.Contact{
        first_name: "Old_First_Name",
        last_name: "Old_Last_Name",
        email: first_contact_to_import.email,
        company_profile_id: company_profile.id,
        creator_user_id: profile_user_2.user_id,
        creator_name: Gaia.Companies.User.build_creator_name(company_user),
        lead_identified_at: inserted_at,
        imported_at: inserted_at,
        contact_source: :manual_creation,
        inserted_at: inserted_at
      }

      {:ok, existing_contact} = Gaia.Repo.insert(existing_contact)

      preload_existing_contact =
        Gaia.Repo.preload(existing_contact, [:comms_unsubscribes, :global_unsubscribe])

      existing_comms_contact_unsubscribes = [
        %{
          company_profile_id: existing_contact.company_profile_id,
          contact_id: existing_contact.id,
          scope: :general
        },
        %{
          company_profile_id: existing_contact.company_profile_id,
          contact_id: existing_contact.id,
          scope: :qa
        },
        %{
          company_profile_id: existing_contact.company_profile_id,
          contact_id: existing_contact.id,
          scope: :raises
        }
      ]

      {:ok, updated_existing_contact} =
        Gaia.Contacts.update_contact(preload_existing_contact, %{
          comms_unsubscribes: existing_comms_contact_unsubscribes,
          global_unsubscribe: nil
        })

      preload_updated_existing_contact =
        Gaia.Repo.preload(updated_existing_contact, [:comms_unsubscribes, :global_unsubscribe])

      admin_user = admin_user()

      FeatureFlags.enable(admin_user, %{flag_name: :static_lists, description: ""})

      tags = ["tag1", "tag2"]

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            "customContacts" => contacts_to_import,
            "audienceTags" => tags,
            "isGlobalUnsubscribe" => false,
            "unsubscribeScopes" => ["ANNOUNCEMENT", "ACTIVITY_UPDATE", "GENERAL"],
            "applySubscriptionToNewContactOnly" => false
          }
        )

      assert response["data"]["upsertCustomContacts"]["upserted_count"] ==
               length(contacts_to_import)

      contacts_in_db =
        Repo.all(
          from(c in Contact,
            where: c.email in ^Enum.map(contacts_to_import, &Map.get(&1, :email)),
            where: c.company_profile_id == ^company_profile.id,
            preload: [:comms_unsubscribes, :global_unsubscribe]
          )
        )

      assert length(contacts_in_db) == length(contacts_to_import)

      first_contact = Enum.at(contacts_in_db, 0)

      assert first_contact.id == preload_updated_existing_contact.id
      assert first_contact.email == preload_updated_existing_contact.email

      assert first_contact.comms_unsubscribes != []
      assert first_contact.global_unsubscribe == nil

      # If contact is hub member, the default settings of hub-related subscriptions are same as current
      # hub_related_scopes = [:activity_follow, :qa, :raises, :new_follower]
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :announcement)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :activity_update)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :general)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :activity_follow)) == []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :qa)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :raises)) != []
      assert Enum.filter(first_contact.comms_unsubscribes, &(&1.scope == :new_follower)) == []
    end
  end
end
