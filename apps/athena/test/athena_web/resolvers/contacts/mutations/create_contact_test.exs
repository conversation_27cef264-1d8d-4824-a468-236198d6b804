defmodule AthenaWeb.Resolvers.Contacts.Mutations.CreateContactTest do
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @mutation """
    mutation CreateContact($contact: ContactInput!) {
      createContact(contact: $contact) {
        email
        firstName
        lastName
      }
    }
  """

  describe "resolve/3" do
    setup [:build_data, :build_attrs]

    test "ensure contact created with correct contact source", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id,
      attrs: attrs
    } do
      conn
      |> authenticate_company_user(logged_in_user)
      |> graphql_query(
        market_listing_key: company_profile.ticker.listing_key,
        query: @mutation,
        variables: %{"contact" => attrs}
      )

      assert %{contact_source: :manual_creation, creator_name: ^creator_name, creator_user_id: ^creator_user_id} =
               Contacts.get_contact_by(%{email: attrs["email"]})
    end

    test "ensure contact can't be created if email already exist", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id,
      attrs: attrs
    } do
      contact(%{
        email: attrs["email"],
        creator_name: creator_name,
        creator_user_id: creator_user_id,
        contact_source: :registry_import,
        company_profile_id: company_profile.id
      })

      [%{"message" => error}] =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @mutation,
          variables: %{"contact" => attrs}
        )
        |> Map.get("errors")

      assert "There is an existing contact with the email #{attrs["email"]}" == error

      assert %{contact_source: :registry_import, creator_name: ^creator_name, creator_user_id: ^creator_user_id} =
               Contacts.get_contact_by(%{email: attrs["email"]})
    end
  end

  defp build_data(_) do
    company_profile = company_profile()
    logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

    creator_name = Gaia.Companies.User.build_creator_name(logged_in_user)
    creator_user_id = logged_in_user.id

    [
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id
    ]
  end

  defp build_attrs(_) do
    [
      attrs: %{
        "email" => Faker.Internet.email(),
        "firstName" => Faker.Person.first_name(),
        "lastName" => Faker.Person.last_name()
      }
    ]
  end
end
