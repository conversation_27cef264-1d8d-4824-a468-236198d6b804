defmodule AthenaWeb.Resolvers.Contacts.Mutations.CreateStaticList do
  @moduledoc false
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @query """
    mutation CreateStaticList($staticList: StaticListInput!) {
      createStaticList(staticList: $staticList) {
      id
      name
      description
      backgroundColor
      textColor
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
      contact = contact(company_profile)
      contact2 = contact(company_profile)

      [
        company_profile: company_profile,
        logged_in_user: logged_in_user,
        contact: contact,
        contact2: contact2
      ]
    end

    test "creates a static list with no contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      static_list_input = %{
        name: "Test Static List",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000"
      }

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"staticList" => static_list_input}
        )

      static_list = response["data"]["createStaticList"]
      assert Contacts.get_static_list_member_count(static_list["id"]) == 0
      assert static_list["name"] == "Test Static List"
      assert static_list["description"] == "Test Static List Description"
      assert static_list["backgroundColor"] == "#FFFFFF"
      assert static_list["textColor"] == "#000000"
    end

    test "creates a static list with contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      contact: contact,
      contact2: contact2
    } do
      static_list_input = %{
        name: "Test Static List",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000",
        contactIds: [contact.id, contact2.id]
      }

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"staticList" => static_list_input}
        )

      static_list = response["data"]["createStaticList"]
      assert Contacts.get_static_list_member_count(static_list["id"]) == 2

      assert static_list["name"] == "Test Static List"
      assert static_list["description"] == "Test Static List Description"
      assert static_list["backgroundColor"] == "#FFFFFF"
      assert static_list["textColor"] == "#000000"
    end

    test "creates a static list with nil email contacts", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      contact: contact,
      contact2: contact2
    } do
      {:ok, contact3} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          lead_identified_at: NaiveDateTime.utc_now(:second),
          contact_source: :registry_import,
          email: nil
        })

      static_list_input = %{
        name: "Test Static List",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000",
        contactIds: [contact.id, contact2.id, contact3.id]
      }

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"staticList" => static_list_input}
        )

      static_list = response["data"]["createStaticList"]
      assert Contacts.get_static_list_member_count(static_list["id"]) == 3

      assert %{
               "name" => "Test Static List",
               "description" => "Test Static List Description",
               "backgroundColor" => "#FFFFFF",
               "textColor" => "#000000"
             } = static_list
    end

    test "does not create a static list if the name is already taken", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      static_list_input = %{
        name: "Test Static List",
        description: "Test Static List Description",
        backgroundColor: "#FFFFFF",
        textColor: "#000000"
      }

      conn
      |> authenticate_company_user(logged_in_user)
      |> graphql_query(
        market_listing_key: company_profile.ticker.listing_key,
        query: @query,
        variables: %{"staticList" => static_list_input}
      )

      %{"errors" => [error]} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"staticList" => static_list_input}
        )

      assert %{"message" => "Cannot create static list"} = error
    end
  end
end
