defmodule AthenaWeb.Resolvers.Contacts.Mutations.UpdateContactTest do
  use AthenaWeb.ConnCase

  alias Gaia.Contacts

  @mutation """
    mutation UpdateContact($id: ID!, $contact: ContactInput!) {
      updateContact(id: $id, contact: $contact) {
        id
      }
    }
  """

  describe "resolve/3" do
    setup [:build_data, :build_attrs]

    test "ensure contact won't update contact source", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id,
      attrs: attrs
    } do
      %{id: contact_id} =
        contact(%{
          creator_name: creator_name,
          creator_user_id: creator_user_id,
          contact_source: :registry_import,
          company_profile_id: company_profile.id
        })

      conn
      |> authenticate_company_user(logged_in_user)
      |> graphql_query(
        market_listing_key: company_profile.ticker.listing_key,
        query: @mutation,
        variables: %{"contact" => attrs, "id" => contact_id}
      )

      assert %{contact_source: :registry_import, creator_name: ^creator_name, creator_user_id: ^creator_user_id} =
               Contacts.get_contact_by(%{email: attrs["email"]})
    end

    test "ensure contact source won't accept contact source", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id,
      attrs: attrs
    } do
      %{id: contact_id} =
        contact(%{
          email: attrs["email"],
          creator_name: creator_name,
          creator_user_id: creator_user_id,
          contact_source: :registry_import,
          company_profile_id: company_profile.id
        })

      [%{"message" => error}] =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @mutation,
          variables: %{"contact" => Map.put(attrs, "contactSource", "manual_creation"), "id" => contact_id}
        )
        |> Map.get("errors")

      assert "Argument \"contact\" has invalid value $contact.\nIn field \"contactSource\": Unknown field." == error

      assert %{contact_source: :registry_import, creator_name: ^creator_name, creator_user_id: ^creator_user_id} =
               Contacts.get_contact_by(%{email: attrs["email"]})
    end

    test "ensure can't update contact with the same email of another contact", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id,
      attrs: attrs
    } do
      %{id: primary_contact_id} =
        contact(%{
          email: Faker.Internet.email(),
          creator_name: creator_name,
          creator_user_id: creator_user_id,
          contact_source: :registry_import,
          company_profile_id: company_profile.id
        })

      contact(%{
        email: attrs["email"],
        creator_name: creator_name,
        creator_user_id: creator_user_id,
        contact_source: :registry_import,
        company_profile_id: company_profile.id
      })

      [%{"message" => error}] =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @mutation,
          variables: %{"contact" => attrs, "id" => primary_contact_id}
        )
        |> Map.get("errors")

      assert "This email is already in use. Enter a unique email." == error

      assert %{contact_source: :registry_import, creator_name: ^creator_name, creator_user_id: ^creator_user_id} =
               Contacts.get_contact_by(%{email: attrs["email"]})
    end
  end

  defp build_data(_) do
    company_profile = company_profile()
    logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

    creator_name = Gaia.Companies.User.build_creator_name(logged_in_user)
    creator_user_id = logged_in_user.id

    [
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      creator_name: creator_name,
      creator_user_id: creator_user_id
    ]
  end

  defp build_attrs(_) do
    [
      attrs: %{
        "email" => Faker.Internet.email(),
        "firstName" => Faker.Person.first_name(),
        "lastName" => Faker.Person.last_name()
      }
    ]
  end
end
