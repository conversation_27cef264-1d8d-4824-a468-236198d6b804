defmodule AthenaWeb.Resolvers.Contacts.Mutations.DeleteStaticListMemberTest do
  use AthenaWeb.ConnCase

  @query """
  mutation DeleteStaticListMember($staticListId: ID!, $contactId: ID!) {
    deleteStaticListMember(staticListId: $staticListId, contactId: $contactId)
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()

      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      contact = contact(company_profile)

      static_list =
        static_list_with_contacts(
          %{
            name: "Test Static List",
            company_profile_id: company_profile.id,
            background_color: "#FFFFFF",
            text_color: "#000000"
          },
          [contact.id]
        )

      [
        company_profile: company_profile,
        contact: contact,
        static_list: static_list,
        logged_in_user: logged_in_user
      ]
    end

    test "errors if the static list does not exist", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      static_list: static_list,
      contact: contact
    } do
      %{"errors" => [error]} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"staticListId" => 888, "contactId" => contact.id}
        )

      assert %{"message" => "Cannot remove contact from static list"} = error
      assert Gaia.Contacts.get_static_list_member_count(static_list.id) == 1
    end
  end
end
