defmodule AthenaWeb.Resolvers.Tracking.Queries.UtmLinksTest do
  use AthenaWeb.ConnCase

  alias Gaia.Tracking

  @query """
    query UtmLinks(
      $after: String
      $before: String
      $first: Int
      $last: Int
      $options: OptionsInput
    ) {
      utmLinks(
        after: $after
        before: $before
        first: $first
        last: $last
        options: $options
      ) {
        edges {
          node {
            id

            hash
            destinationUrl
            utmCampaign
            utmSource
            utmMedium
            utmUrl
            insertedAt
            updatedAt
          }
        }
        options {
          filters {
            key
            value
          }
          orders {
            key
            value
          }
        }
        pageInfo {
          endCursor
          hasNextPage
          hasPreviousPage
          startCursor
        }
        total(options: $options)
      }
    }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      {:ok, company_profile: company_profile, logged_in_user: logged_in_user}
    end

    test "successfully retrieves utm links", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      {:ok, utm_link} =
        Tracking.create_utm_link(%{
          destination_url: "https://www.google.com",
          utm_campaign: "campaign",
          utm_source: "source",
          utm_medium: "medium",
          utm_url: "url",
          company_profile_id: company_profile.id
        })

      Tracking.create_utm_link(%{
        destination_url: "https://www.facebook.com",
        utm_campaign: "campaign_2",
        utm_source: "source_2",
        utm_medium: "medium_2",
        is_user_generated: false,
        utm_url: "url_2",
        company_profile_id: company_profile.id
      })

      %{"data" => data} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"first" => 10}
        )

      [%{"node" => return_link}] = data["utmLinks"]["edges"]

      assert get_in(data, ["utmLinks", "total"]) == 1

      assert return_link["id"] == to_string(utm_link.id)
      assert return_link["hash"] == utm_link.hash
      assert return_link["destinationUrl"] == utm_link.destination_url
      assert return_link["utmCampaign"] == utm_link.utm_campaign
      assert return_link["utmSource"] == utm_link.utm_source
      assert return_link["utmMedium"] == utm_link.utm_medium
      assert return_link["utmUrl"] == utm_link.utm_url
    end
  end
end
