defmodule AthenaWeb.Resolvers.Tracking.Queries.ExistingUtmFieldsTest do
  use AthenaWeb.ConnCase

  @query """
    query ExistingUtmFields {
    ExistingUtmFields {
      utmCampaigns
      utmMediumsAndSources {
        medium
        sources
      }
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user

      {:ok, company_profile: company_profile, logged_in_user: logged_in_user}
    end

    test "successfully returns only existing user_generated utm fields for a company",
         %{conn: conn, company_profile: company_profile, logged_in_user: logged_in_user} do
      Gaia.Tracking.create_utm_link(%{
        company_profile_id: company_profile.id,
        utm_medium: "MED1",
        utm_source: "SRC1",
        utm_campaign: "C1",
        destination_url: Gaia.Socials.Helper.encode("http://example.com"),
        utm_url: "http://example.com/?utm_source=SRC1&utm_medium=MED1&utm_campaign=C1"
      })

      Gaia.Tracking.create_utm_link(%{
        company_profile_id: company_profile.id,
        utm_medium: "MED2",
        utm_source: "SRC2",
        utm_campaign: "C2",
        destination_url: Gaia.Socials.Helper.encode("http://example.com"),
        utm_url: "http://example.com/?utm_source=SRC2&utm_medium=MED2&utm_campaign=C2",
        is_user_generated: false
      })

      Gaia.Tracking.create_utm_link(%{
        company_profile_id: company_profile.id,
        utm_medium: "MED3",
        utm_source: "SRC3",
        utm_campaign: "C3",
        destination_url: Gaia.Socials.Helper.encode("http://example.com"),
        utm_url: "http://example.com/?utm_source=SRC3&utm_medium=MED3&utm_campaign=C3"
      })

      Gaia.Tracking.create_utm_link(%{
        company_profile_id: company_profile.id,
        utm_medium: "MED3",
        utm_source: "SRC1",
        utm_campaign: "C4",
        destination_url: Gaia.Socials.Helper.encode("http://example.com"),
        utm_url: "http://example.com/?utm_source=SRC1&utm_medium=MED3&utm_campaign=C4"
      })

      Gaia.Tracking.create_utm_link(%{
        company_profile_id: company_profile.id,
        utm_medium: "MED3",
        utm_source: "SRC2",
        utm_campaign: "C4",
        destination_url: Gaia.Socials.Helper.encode("http://example.com"),
        utm_url: "http://example.com/?utm_source=SRC2&utm_medium=MED3&utm_campaign=C4"
      })

      %{"data" => data} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query
        )

      expected_utm_campaigns = ["C1", "C3", "C4"]

      expected_utm_medium_and_sources =
        Enum.sort([
          %{"medium" => "MED1", "sources" => ["SRC1"]},
          %{"medium" => "MED3", "sources" => ["SRC3", "SRC2", "SRC1"]}
        ])

      returned_utm_campaigns = data["ExistingUtmFields"]["utmCampaigns"]
      assert Enum.sort(expected_utm_campaigns) == Enum.sort(returned_utm_campaigns)

      returned_utm_medium_and_sources = data["ExistingUtmFields"]["utmMediumsAndSources"]

      returned_utm_medium_and_sources
      |> Enum.sort()
      |> Enum.zip(expected_utm_medium_and_sources)
      |> Enum.each(fn {a, b} -> assert Enum.sort(a["sources"]) == Enum.sort(b["sources"]) end)
    end
  end
end
