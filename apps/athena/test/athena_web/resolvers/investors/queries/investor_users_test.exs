defmodule AthenaWeb.Resolvers.Queries.InvestorsUserTest do
  use AthenaWeb.ConnCase

  def investor_users_graphql(additional_investor_fields \\ "") do
    """
    query InvestorUsers($after: String, $before: String, $first: Int, $last: Int, $options: OptionsInput) {
      investorUsers(after: $after, before: $before, first: $first, last: $last, options: $options) {
        edges {
          node {
            id

            #{additional_investor_fields}
          }
        }

        options {
          filters {
            key
            value
          }
          orders {
            key
            value
          }
        }

        pageInfo {
          endCursor
          hasNextPage
          hasPreviousPage
          startCursor
        }

        total(options: $options)
      }
    }
    """
  end

  describe "resolve/3" do
    setup context do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      authenticated_conn = authenticate_company_user(context.conn, company_profile_user.user)

      {:ok,
       company_profile: company_profile,
       investors_query: fn variables ->
         graphql_query(authenticated_conn,
           market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
           query: investor_users_graphql(),
           variables: variables
         )
       end}
    end

    test "investorUsers returning only investors that belongs to the current user's company", %{
      company_profile: company_profile,
      investors_query: investors_query
    } do
      investor = investor_user(company_profile)
      investor_id = "#{investor.id}"

      _investor_from_another_company = investor_user()

      assert %{
               "data" => %{
                 "investorUsers" => %{
                   "edges" => [%{"node" => %{"id" => ^investor_id}}],
                   "options" => %{"filters" => [], "orders" => []},
                   "total" => 1
                 }
               }
             } = investors_query.(%{"first" => 10})
    end
  end

  test "Querying activityStats field should return an error", %{conn: conn} do
    # Please do not use `:activity_stats` under `:investor_user` and use a separate `investor_activity_stats` query instead
    # This field could be very slow and blocking frontend from loading the page
    company_profile = company_profile()
    company_profile_user = company_profile_user(%{profile_id: company_profile.id})
    investor_user(company_profile)

    authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

    # Check that the valid query does not return activityStats
    refute authenticated_conn
           |> graphql_query(
             market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
             query: investor_users_graphql(),
             variables: %{"first" => 10}
           )
           |> get_in(["data", "investorUsers", "edges"])
           |> List.first()
           |> Map.has_key?("activityStats")

    # Querying activityStats field should return an error
    assert %{
             "errors" => [
               %{
                 "locations" => [%{"column" => _, "line" => _}],
                 "message" => "Cannot query field \"activityStats\" on type \"InvestorUser\"."
               }
             ]
           } =
             graphql_query(authenticated_conn,
               market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker),
               query: investor_users_graphql("activityStats { id }"),
               variables: %{"first" => 10}
             )
  end
end
