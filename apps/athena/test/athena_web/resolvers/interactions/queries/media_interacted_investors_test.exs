defmodule AthenaWeb.Resolvers.Interactions.Queries.MediaInteractedInvestorsTest do
  use AthenaWeb.ConnCase

  @query """
  query MediaInteractedInvestors(
    $after: String
    $before: String
    $first: Int
    $last: Int
    $options: OptionsInput
    $mediaId: ID!
    $mediaType: String!
    $slug: String
  ) {
    mediaInteractedInvestors(
      after: $after
      before: $before
      first: $first
      last: $last
      options: $options
      mediaId: $mediaId
      mediaType: $mediaType
      slug: $slug
    ) {
      edges {
        node {
          investorUser {
            id
            username
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()

      %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{} = logged_in_user} =
        company_profile_user(%{profile_id: company_profile.id})

      [company_profile: company_profile, logged_in_user: logged_in_user]
    end

    test "ensure successfully returns only investors who have interacted with the given media", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      %Gaia.Interactions.MediaComment{
        investor_user: %Gaia.Investors.User{} = investor_one,
        media: %Gaia.Interactions.Media{
          media_announcement: %Gaia.Interactions.MediaAnnouncement{} = announcement
        }
      } =
        media_comment_with_media(company_profile, :announcement)

      # This investor will not interact
      investor_two = investor_user(company_profile)

      %{"data" => %{"mediaInteractedInvestors" => %{"edges" => [expected]}}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{mediaId: announcement.id, mediaType: "MediaAnnouncement", first: 10}
        )

      assert expected["node"]["investorUser"]["id"] == "#{investor_one.id}"
      refute expected["node"]["investorUser"]["id"] == "#{investor_two.id}"
    end

    test "ensure 'engagementType' filter works", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user
    } do
      %Gaia.Interactions.MediaComment{
        investor_user: %Gaia.Investors.User{} = investor_one,
        media:
          %Gaia.Interactions.Media{
            media_announcement: %Gaia.Interactions.MediaAnnouncement{} = announcement
          } = media
      } =
        media_comment_with_media(company_profile, :announcement)

      # This investor will like the announcement
      investor_two = investor_user(company_profile)

      %Gaia.Interactions.MediaLike{} =
        media_like(media, investor_two)

      %{"data" => %{"mediaInteractedInvestors" => %{"edges" => [expected_to_have_investor_one]}}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            mediaId: announcement.id,
            mediaType: "MediaAnnouncement",
            first: 10,
            options: %{
              filters: [
                %{
                  key: "engagementType",
                  value: "Questions"
                }
              ]
            }
          }
        )

      %{"data" => %{"mediaInteractedInvestors" => %{"edges" => [expected_to_have_investor_two]}}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{
            mediaId: announcement.id,
            mediaType: "MediaAnnouncement",
            first: 10,
            options: %{
              filters: [
                %{
                  key: "engagementType",
                  value: "Likes"
                }
              ]
            }
          }
        )

      assert expected_to_have_investor_one["node"]["investorUser"]["id"] == "#{investor_one.id}"
      assert expected_to_have_investor_two["node"]["investorUser"]["id"] == "#{investor_two.id}"
    end
  end
end
