defmodule AthenaWeb.Resolvers.Raises.DeleteShareholderOfferPrivateViewerTest do
  use AthenaWeb.ConnCase

  alias Gaia.Raises

  @query """
  mutation DeleteShareholderOfferPrivateViewer(
    $id: ID!
  ) {
    deleteShareholderOfferPrivateViewer(
      id: $id
    )
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
      shareholder_offer = shareholder_offer(company_profile)

      {:ok, company_profile: company_profile, logged_in_user: logged_in_user, shareholder_offer: shareholder_offer}
    end

    test "successfully deletes a private viewer", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      shareholder_offer: shareholder_offer
    } do
      {:ok, private_viewer} =
        Raises.create_private_viewer(%{email: "<EMAIL>", shareholder_offer_id: shareholder_offer.id})

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"id" => private_viewer.id}
        )

      assert %{"data" => %{"deleteShareholderOfferPrivateViewer" => true}} = response
      assert Raises.get_private_viewer(private_viewer.id) == nil
    end
  end
end
