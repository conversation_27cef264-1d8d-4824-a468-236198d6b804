defmodule AthenaWeb.Resolvers.Raises.Mutations.ChangeShareholderOfferUsCitzenCheckTest do
  use AthenaWeb.ConnCase, async: false

  alias Gaia.Companies.ShareholderOfferPage

  @query """
  mutation ChangeShareholderOfferUsCitzenCheck(
    $updatedCheck: Boolean!
    $shareholderOfferPageId: ID!
  ) {
    changeShareholderOfferUsCitzenCheck(
      updatedCheck: $updatedCheck
      shareholderOfferPageId: $shareholderOfferPageId
    ) {
      id
      usCitizenPageEnabled
    }
  }
  """

  describe "change_shareholder_offer_us_citzen_check_resolver graph" do
    setup [:build]

    test "ensure us citzen check has changed successfuly", %{
      conn: conn,
      logged_in_user: %{id: user_id} = logged_in_user,
      company_profile: company_profile,
      shareholder_offer_page: %{id: sop_id}
    } do
      assert %ShareholderOfferPage{us_citizen_page_enabled: false} = Gaia.Repo.get(ShareholderOfferPage, sop_id)

      response =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"updatedCheck" => true, "shareholderOfferPageId" => sop_id}
        )

      assert response == %{
               "data" => %{
                 "changeShareholderOfferUsCitzenCheck" => %{
                   "id" => "#{sop_id}",
                   "usCitizenPageEnabled" => true
                 }
               }
             }

      assert %ShareholderOfferPage{us_citizen_page_enabled: true, last_edited_by_user_id: ^user_id} =
               Gaia.Repo.get(ShareholderOfferPage, sop_id)
    end

    test "ensure error returned if page does not exist", %{
      conn: conn,
      logged_in_user: logged_in_user,
      company_profile: company_profile
    } do
      errors =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"updatedCheck" => true, "shareholderOfferPageId" => 666}
        )
        |> Map.get("errors")

      assert [%{"message" => "Shareholder offer page not found"}] = errors
    end

    test "ensure error returned if params are incorrect", %{
      conn: conn,
      logged_in_user: logged_in_user,
      company_profile: company_profile,
      shareholder_offer_page: %{id: sop_id}
    } do
      errors =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"updatedCheck" => "yes", "shareholderOfferPageId" => sop_id}
        )
        |> Map.get("errors")

      assert [%{"message" => "Argument \"updatedCheck\" has invalid value $updatedCheck."}] = errors
    end

    test "ensure error returned user from different company", %{
      conn: conn,
      shareholder_offer_page: %{id: sop_id}
    } do
      new_company_profile = company_profile()
      new_in_user = company_profile_user(%{profile_id: new_company_profile.id}).user

      errors =
        conn
        |> authenticate_company_user(new_in_user)
        |> graphql_query(
          market_listing_key: new_company_profile.ticker.listing_key,
          query: @query,
          variables: %{"updatedCheck" => true, "shareholderOfferPageId" => sop_id}
        )
        |> Map.get("errors")

      assert [%{"message" => "It looks like you don't have permission to do this."}] = errors
    end
  end

  defp build(_) do
    company_profile = company_profile()
    logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
    shareholder_offer = shareholder_offer(company_profile)

    shareholder_offer_page =
      shareholder_offer_page(company_profile,
        shareholder_offer_id: shareholder_offer.id,
        us_citizen_page_enabled: false
      )

    [shareholder_offer_page: shareholder_offer_page, logged_in_user: logged_in_user, company_profile: company_profile]
  end
end
