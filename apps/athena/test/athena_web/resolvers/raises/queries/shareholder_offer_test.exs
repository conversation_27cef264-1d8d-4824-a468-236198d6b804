defmodule AthenaWeb.Resolvers.Queries.ShareholderOfferTest do
  use AthenaWeb.ConnCase

  alias Gaia.Raises

  @query """
  query ShareholderOffer($id: ID!) {
    shareholderOffer(id: $id) {
      id
      title
      type
      isLive
      publishedAt
      scheduledAt
      companyProfile {
        id
        registry
        __typename
      }
      lastEditedByUser {
        email
        firstName
        lastName
        __typename
      }
      insertedAt
      updatedAt
      totalViewCount
      totalViewCountFromHubUsers
      totalUniqueVisitorsCount
      totalUniqueVisitorsCountFromHubUsers
      signUpsDuringOfferPeriod
      totalInvestorPresentationDownloadsCount
      totalOfferBookletDownloadsCount
      shareholderOfferEngagement {
        date
        totalViewCount
        totalUniqueVisitorsCount
        __typename
      }
      companyShareholderOfferPage {
        id
        bannerUrl
        closingDate
        interactiveAnnouncementLink
        introductionHeading
        introductionMessage
        investorPresentationUrl
        offerBookletUrl
        offerPrice
        raiseApplyInstructionMessage
        raiseDefinitionMessage
        raiseDefinitionTimelineDiagramUrl
        raiseReasonHeroMediaImageUrl
        raiseReasonHeroMediaVideoUrl
        raiseReasonHeroMediaSocialVideoUrl
        raiseReasonMessage
        raisingTarget
        insertedAt
        updatedAt
        usCitizenPageEnabled
        lastEditedByUser {
          email
          firstName
          lastName
          __typename
        }
        faqs {
          id
          answer
          question
          orderId
          shareholderOfferPageId
          __typename
        }
        instructions {
          id
          subheading
          message
          buttonUrl
          buttonText
          orderId
          shareholderOfferPageId
          __typename
        }
        shareholderOffer {
          id
          type
          isLive
          publishedAt
          scheduledAt
          __typename
        }
        __typename
      }
      privateViewers {
        id
        email
      }
      __typename
    }
  }
  """

  describe "resolve/3" do
    setup do
      company_profile = company_profile()
      logged_in_user = company_profile_user(%{profile_id: company_profile.id}).user
      shareholder_offer = shareholder_offer(company_profile)
      shareholder_offer_page = shareholder_offer_page(company_profile, shareholder_offer_id: shareholder_offer.id)
      private_viewer_email = "<EMAIL>"

      {:ok, _} =
        Raises.create_private_viewer(%{
          shareholder_offer_id: shareholder_offer.id,
          email: private_viewer_email
        })

      {:ok,
       company_profile: company_profile,
       logged_in_user: logged_in_user,
       shareholder_offer: shareholder_offer,
       shareholder_offer_page: shareholder_offer_page,
       private_viewer_email: private_viewer_email}
    end

    test "successfully returns the shareholder offer", %{
      conn: conn,
      company_profile: company_profile,
      logged_in_user: logged_in_user,
      shareholder_offer: shareholder_offer,
      shareholder_offer_page: shareholder_offer_page,
      private_viewer_email: private_viewer_email
    } do
      %{"data" => %{"shareholderOffer" => data}} =
        conn
        |> authenticate_company_user(logged_in_user)
        |> graphql_query(
          market_listing_key: company_profile.ticker.listing_key,
          query: @query,
          variables: %{"id" => shareholder_offer.id}
        )

      assert data["id"] == Integer.to_string(shareholder_offer.id)
      assert data["companyProfile"]["id"] == Integer.to_string(company_profile.id)
      assert data["companyShareholderOfferPage"]["id"] == Integer.to_string(shareholder_offer_page.id)
      assert data["companyShareholderOfferPage"]["usCitizenPageEnabled"] == shareholder_offer_page.us_citizen_page_enabled
      assert get_in(data["privateViewers"], [Access.at(0)])["email"] == private_viewer_email
    end
  end
end
