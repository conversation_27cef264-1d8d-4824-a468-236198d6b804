defmodule AthenaWeb.Middleware.TZTest do
  use ExUnit.Case, async: true

  alias Absinthe.Resolution
  alias AthenaWeb.Middleware.TZ

  setup do
    # Define a sample timezone and naive datetime inputs
    timezone = "America/New_York"
    start_date = ~N[2024-11-21 12:00:00]
    end_date = ~N[2024-11-21 18:00:00]

    # Return data to be used in tests
    {:ok, %{timezone: timezone, start_date: start_date, end_date: end_date}}
  end

  test "middleware converts naive datetimes to timezone-aware naive datetimes", %{
    timezone: timezone,
    start_date: start_date,
    end_date: end_date
  } do
    # Create a mock resolution with context and arguments
    resolution = %Resolution{
      arguments: %{start_date: start_date, end_date: end_date},
      context: %{
        current_company_profile_user: %Gaia.Companies.ProfileUser{
          profile: %Gaia.Companies.Profile{
            timezone: timezone
          }
        }
      },
      errors: []
    }

    # Call the middleware
    new_resolution = TZ.call(resolution, nil)

    # Extract the updated arguments
    updated_args = new_resolution.arguments

    # Assertions
    assert updated_args.start_date != start_date
    assert updated_args.end_date != end_date
    assert start_date |> Timex.to_datetime(timezone) |> Timex.to_naive_datetime() == updated_args.start_date
    assert end_date |> Timex.to_datetime(timezone) |> Timex.to_naive_datetime() == updated_args.end_date
  end

  test "middleware does not modify resolution without required context", %{start_date: start_date, end_date: end_date} do
    # Create a mock resolution without the required context
    resolution = %Resolution{
      arguments: %{start_date: start_date, end_date: end_date},
      context: %{},
      errors: []
    }

    # Call the middleware
    new_resolution = TZ.call(resolution, nil)

    # Assertions
    assert new_resolution == resolution
  end

  test "middleware does not modify resolution if errors exist", %{
    timezone: timezone,
    start_date: start_date,
    end_date: end_date
  } do
    # Create a mock resolution with existing errors
    resolution = %Resolution{
      arguments: %{start_date: start_date, end_date: end_date},
      context: %{
        current_company_profile_user: %{
          profile: %{
            timezone: timezone
          }
        }
      },
      errors: ["An error occurred"]
    }

    # Call the middleware
    new_resolution = TZ.call(resolution, nil)

    # Assertions
    assert new_resolution == resolution
  end
end
