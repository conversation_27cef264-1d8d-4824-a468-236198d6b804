#: lib/athena_web/resolvers/companies/mutations/publish_welcome_page.ex:66
#: lib/athena_web/resolvers/companies/mutations/publish_welcome_page.ex:66
#, elixir-autogen, elixir-format
msgctxt "Athena - PublishWelcomePage mutation"
msgid "Unable to publish welcome page. Please try again later."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:59
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:59
#, elixir-autogen, elixir-format
msgctxt "Mark investor certificate as verified mutation resolver"
msgid "Could not find certificate"
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:41
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:41
#, elixir-autogen, elixir-format
msgctxt "Reject Certificate Mutation"
msgid "Can not reject certificate which already approved"
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:60
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:60
#, elixir-autogen, elixir-format
msgctxt "Reject Certificate Mutation"
msgid "Can not reject selected certificate."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:49
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:49
#, elixir-autogen, elixir-format
msgctxt "Reject Certificate Mutation"
msgid "The Certificate has already been rejected."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:51
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:51
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Mark investor certificate as verified mutation resolver"
msgid "Certificate has already been rejected."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:43
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Mark investor certificate as verified mutation resolver"
msgid "Certificate has already been verified."
msgstr ""

#: lib/athena_web/resolvers/investors/queries/certificate_by_id.ex:39
#: lib/athena_web/resolvers/investors/queries/certificate_by_id.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Certificate by ID query resolver"
msgid "Could not find certificate by ID"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/mark_feature_as_onboarded.ex:23
#: lib/athena_web/resolvers/companies/mutations/mark_feature_as_onboarded.ex:23
#, elixir-autogen, elixir-format
msgctxt "MarkFeatureAsOnboarded mutation resolver"
msgid "Could not mark feature as onboarded for company user"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/invalidate_past_placement_participant.ex:35
#: lib/athena_web/resolvers/raises/mutations/invalidate_past_placement_participant.ex:35
#, elixir-autogen, elixir-format
msgctxt "Company user invalidate past placement participant mutation"
msgid "Could not invalidate past placement participant"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/validate_past_placement_participant.ex:35
#: lib/athena_web/resolvers/raises/mutations/validate_past_placement_participant.ex:35
#, elixir-autogen, elixir-format
msgctxt "Company user validate past placement participant mutation"
msgid "Could not validate past placement participant"
msgstr ""

#: lib/athena_web/resolvers/raises/queries/past_placement.ex:25
#: lib/athena_web/resolvers/raises/queries/past_placement.ex:25
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Past placement query"
msgid "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/upsert_past_placement_participant_list.ex:48
#: lib/athena_web/resolvers/raises/mutations/upsert_past_placement_participant_list.ex:48
#, elixir-autogen, elixir-format
msgctxt "Upsert past placement participant list mutation resolver"
msgid "No participant found in the uploaded list. Please re-upload or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/upsert_past_placement_participant_list.ex:59
#: lib/athena_web/resolvers/raises/mutations/upsert_past_placement_participant_list.ex:59
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert past placement participant list mutation resolver"
msgid "Unfortunately the participant list uploaded was not processed successfully. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/raises/queries/all_past_placement_participants.ex:36
#: lib/athena_web/resolvers/raises/queries/all_past_placement_participants.ex:36
#, elixir-autogen, elixir-format, fuzzy
msgctxt "All past placement participants query"
msgid "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/raises/queries/past_placement_aftermarket_stats.ex:30
#: lib/athena_web/resolvers/raises/queries/past_placement_aftermarket_stats.ex:30
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Past placement aftermarket stats"
msgid "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/raises/queries/past_placement_participants_cursor.ex:53
#: lib/athena_web/resolvers/raises/queries/past_placement_participants_cursor.ex:53
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Past placement participants cursor query"
msgid "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/remove_custom_emails.ex:26
#: lib/athena_web/resolvers/comms/mutations/remove_custom_emails.ex:26
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - RemoveCustomEmails mutation"
msgid "Unfortunately, we could not remove your custom emails at this time, please try again later."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:48
#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:48
#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:56
#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:56
#, elixir-autogen, elixir-format
msgctxt "Athena - upsert custom email mutation"
msgid "Custom domain is not connected"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:40
#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:40
#, elixir-autogen, elixir-format
msgctxt "Athena - upsert custom email mutation"
msgid "Please set up custom domain first"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:67
#: lib/athena_web/resolvers/comms/mutations/upsert_custom_emails.ex:67
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - upsert custom email mutation"
msgid "Unfortunately we could not update the custom email at this time, please check your input and try again later."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/create_custom_domain.ex:62
#: lib/athena_web/resolvers/companies/mutations/create_custom_domain.ex:62
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - CreateCustomDomain mutation"
msgid "Unfortunately, we could not create your custom domain at this time, please try again later."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_comment.ex:30
#: lib/athena_web/resolvers/interactions/mutations/create_media_comment.ex:30
#, elixir-autogen, elixir-format
msgctxt "Create media comment mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_comment.ex:44
#: lib/athena_web/resolvers/interactions/mutations/create_media_comment.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create media comment mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:44
#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media announcement video signed url mutation"
msgid "Announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:63
#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:63
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media announcement video signed url mutation"
msgid "Unable to generate signed url."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:52
#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_video_signed_url.ex:52
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media announcement video signed url mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:26
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:26
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media comment mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:45
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media comment mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:34
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media_comment.ex:34
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media comment mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:53
#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:53
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reply to media comment mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:72
#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:72
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reply to media comment mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:61
#: lib/athena_web/resolvers/interactions/mutations/reply_to_media_comment.ex:61
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reply to media comment mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:25
#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:25
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reset media announcement mutation"
msgid "Media announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:44
#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reset media announcement mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:33
#: lib/athena_web/resolvers/interactions/mutations/reset_media_announcement.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reset media announcement mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:35
#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:35
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Toggle media comment privacy mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:57
#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:57
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Toggle media comment privacy mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:46
#: lib/athena_web/resolvers/interactions/mutations/toggle_media_comment_privacy.ex:46
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Toggle media comment privacy mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:25
#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:25
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media announcement mutation"
msgid "Media announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:44
#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media announcement mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:33
#: lib/athena_web/resolvers/interactions/mutations/update_media_announcement.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media announcement mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/investor-hub-analysis/queries/media_survey_response_stats.ex:33
#: lib/athena_web/resolvers/investor-hub-analysis/queries/media_survey_response_stats.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media survey response stats query"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/investor-hub-analysis/queries/media_survey_response_stats.ex:22
#: lib/athena_web/resolvers/investor-hub-analysis/queries/media_survey_response_stats.ex:22
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media survey response stats query"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:27
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment read mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:46
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:46
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment read mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:35
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_read.ex:35
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment read mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:27
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment star mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:46
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:46
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment star mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:35
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_comment_star.ex:35
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media comment star mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:23
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:23
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:42
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:42
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:31
#: lib/athena_web/resolvers/interactions/mutations/invalidate_media.ex:31
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Invalidate media mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_contact_unsubscribes.ex:38
#: lib/athena_web/resolvers/comms/mutations/upsert_contact_unsubscribes.ex:38
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpsertContactUnsubscribes mutation"
msgid "Unfortunately we could not update the subscription settings at this time, please try again later or contact us for support."
msgstr ""

#: lib/athena_web/resolvers/contacts/mutations/upsert_custom_contacts.ex:117
#: lib/athena_web/resolvers/contacts/mutations/upsert_custom_contacts.ex:117
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpsertCustomContacts mutation"
msgid "Unfortunately, we could not update your contact list at this time, please try again later."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:49
#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:49
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpsertBaseEmailTemplate mutation"
msgid "Unfortunately we could not update the base email template at this time, please check your input and try again later."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_update_attachment_signed_url.ex:35
#: lib/athena_web/resolvers/interactions/mutations/generate_media_update_attachment_signed_url.ex:35
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media update attachment signed url mutation"
msgid "Unable to generate signed url."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media_update.ex:32
#: lib/athena_web/resolvers/interactions/queries/media_update.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media update query"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:39
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media update mutation"
msgid "Unable to publish update."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:30
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:30
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media update mutation"
msgid "Update does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:33
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media update mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:89
#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:89
#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:116
#: lib/athena_web/resolvers/comms/mutations/upsert_base_email_template.ex:116
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpsertBaseEmailTemplate mutation"
msgid "Unfortunately, we could not get your email template at this time. Please check your investor hub settings and try again."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:25
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:25
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment done mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:44
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment done mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:33
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_done.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment done mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media_comments_company_author.ex:25
#: lib/athena_web/resolvers/interactions/queries/media_comments_company_author.ex:25
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media comments company author query"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:66
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:66
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Mark investor certificate as verified mutation resolver"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:56
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:56
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Reject Certificate Mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/flows/queries/active_distribution_flows.ex:33
#: lib/athena_web/resolvers/flows/queries/active_distribution_flows.ex:33
#: lib/athena_web/resolvers/flows/queries/active_distribution_flows.ex:45
#: lib/athena_web/resolvers/flows/queries/active_distribution_flows.ex:45
#, elixir-autogen, elixir-format
msgctxt "Active distribution flows query"
msgid "Unable to get active distribution flows"
msgstr ""

#: lib/athena_web/resolvers/flows/queries/distribution_settings_for_channel_for_flow_type.ex:46
#: lib/athena_web/resolvers/flows/queries/distribution_settings_for_channel_for_flow_type.ex:46
#, elixir-autogen, elixir-format
msgctxt "Distribution settings for channel for flow type query"
msgid "Unable to get distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/queries/distribution_settings_for_channel_for_flow_type.ex:34
#: lib/athena_web/resolvers/flows/queries/distribution_settings_for_channel_for_flow_type.ex:34
#, elixir-autogen, elixir-format
msgctxt "Distribution settings for channel for flow type query"
msgid "Unable to get distribution settings for channel and type"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_for_flow_type.ex:67
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_for_flow_type.ex:67
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_for_flow_type.ex:74
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_for_flow_type.ex:74
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create distribution settings mutation"
msgid "Unable to get create distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:57
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:57
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:69
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:69
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update distribution settings mutation"
msgid "Unable to update distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:45
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:45
#, elixir-autogen, elixir-format
msgctxt "Update distribution settings mutation"
msgid "Unable to update distribution settings, as new settings do not match the same flow as existing settings"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:34
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings.ex:34
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update distribution settings mutation"
msgid "Unable to update distribution settings, as settings do not exist"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:59
#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:59
#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:71
#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:71
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Activate distribution settings mutation"
msgid "Unable to activate distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:47
#: lib/athena_web/resolvers/flows/mutations/activate_distribution_settings_for_email.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Activate distribution settings mutation"
msgid "Unable to activate distribution settings, as settings do not exist"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:56
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:56
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:92
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:92
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create distribution settings email mutation"
msgid "Unable to create email settings for distribution"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:79
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:79
#, elixir-autogen, elixir-format
msgctxt "Create distribution settings email mutation"
msgid "Unauthorized: Unable to create email settings for distribution"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:30
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:30
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:54
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:54
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update distribution settings email mutation"
msgid "Unable to update email settings for distribution"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:41
#: lib/athena_web/resolvers/flows/mutations/update_distribution_settings_email.ex:41
#, elixir-autogen, elixir-format
msgctxt "Update distribution settings email mutation"
msgid "Unauthorized: Unable to update email settings for distribution"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:68
#: lib/athena_web/resolvers/flows/mutations/create_distribution_settings_email.ex:68
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create distribution settings email mutation"
msgid "Unable to generate default templates"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:39
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment & reply mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:66
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:66
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment & reply mutation"
msgid "Unfortunately, your comment cannot be updated at this time, please check your input and try again later."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:55
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:55
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media comment & reply mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:47
#: lib/athena_web/resolvers/interactions/mutations/update_media_comment_reply.ex:47
#, elixir-autogen, elixir-format
msgctxt "Update media comment & reply mutation"
msgid "Media comment cannot be updated since it was not created by the company."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/update_social_connection.ex:27
#: lib/athena_web/resolvers/companies/mutations/update_social_connection.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpdateSocialConnection mutation"
msgid "Could not update social connection for company"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/disconnect_social_connection.ex:63
#: lib/athena_web/resolvers/companies/mutations/disconnect_social_connection.ex:63
#, elixir-autogen, elixir-format
msgctxt "Athena - DisconnectSocialConnection mutation"
msgid "Unable to disconnect"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/disconnect_social_connection.ex:51
#: lib/athena_web/resolvers/companies/mutations/disconnect_social_connection.ex:51
#, elixir-autogen, elixir-format
msgctxt "Athena - DisconnectSocialConnection mutation"
msgid "Unable to disconnect your social media account"
msgstr ""

#: lib/athena_web/resolvers/companies/queries/linkedin_organisations.ex:30
#: lib/athena_web/resolvers/companies/queries/linkedin_organisations.ex:30
#: lib/athena_web/resolvers/companies/queries/linkedin_organisations.ex:38
#: lib/athena_web/resolvers/companies/queries/linkedin_organisations.ex:38
#, elixir-autogen, elixir-format
msgctxt "LinkedinOrganisations query"
msgid "Unable to retrieve LinkedIn organisations"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/send_manual_social_post.ex:52
#: lib/athena_web/resolvers/flows/mutations/send_manual_social_post.ex:52
#: lib/athena_web/resolvers/flows/mutations/send_manual_social_post.ex:60
#: lib/athena_web/resolvers/flows/mutations/send_manual_social_post.ex:60
#, elixir-autogen, elixir-format
msgctxt "SendManualSocialPost mutation"
msgid "Unable to send social post manually"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/activate_current_company_distribution_settings.ex:22
#: lib/athena_web/resolvers/flows/mutations/activate_current_company_distribution_settings.ex:22
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - ActivateCurrentCompanyDistributionSettings"
msgid "Unable to activate distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/upsert_distribution_settings.ex:54
#: lib/athena_web/resolvers/flows/mutations/upsert_distribution_settings.ex:54
#: lib/athena_web/resolvers/flows/mutations/upsert_distribution_settings.ex:62
#: lib/athena_web/resolvers/flows/mutations/upsert_distribution_settings.ex:62
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpsertDistributionSettings"
msgid "Unable to upsert distribution settings"
msgstr ""

#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:52
#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:52
#, elixir-autogen, elixir-format
msgctxt "LinkedinPostStatistics query"
msgid "LinkedIn not connected"
msgstr ""

#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:46
#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:46
#, elixir-autogen, elixir-format
msgctxt "LinkedinPostStatistics query"
msgid "LinkedIn post not found"
msgstr ""

#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:60
#: lib/athena_web/resolvers/flows/queries/linkedin_post_statistics.ex:60
#, elixir-autogen, elixir-format, fuzzy
msgctxt "LinkedinPostStatistics query"
msgid "Unable to retrieve LinkedIn post statistics"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement.ex:27
#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement.ex:27
#, elixir-autogen, elixir-format
msgctxt "Create prepared announcement mutation"
msgid "Cannot create a prepared announcement"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/delete_prepared_announcement.ex:41
#: lib/athena_web/resolvers/interactions/mutations/delete_prepared_announcement.ex:41
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Delete prepared announcement mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/delete_prepared_announcement.ex:27
#: lib/athena_web/resolvers/interactions/mutations/delete_prepared_announcement.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Delete prepared announcement mutation"
msgid "Prepared announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:47
#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate prepared announcement video signed url mutation"
msgid "Announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:66
#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:66
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate prepared announcement video signed url mutation"
msgid "Unable to generate signed url."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:55
#: lib/athena_web/resolvers/interactions/mutations/generate_prepared_announcement_video_signed_url.ex:55
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate prepared announcement video signed url mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:86
#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:86
#, elixir-autogen, elixir-format
msgctxt "Link prepared announcement to announcement"
msgid "There was an error linking the prepared announcement to the announcement, please contact support"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:62
#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:62
#, elixir-autogen, elixir-format
msgctxt "Link prepared announcement to announcement"
msgid "Unable to get announcement for media to link prepared announcement to"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:50
#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:50
#, elixir-autogen, elixir-format
msgctxt "Link prepared announcement to announcement"
msgid "Unable to get announcement to link prepared announcement to"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:74
#: lib/athena_web/resolvers/interactions/mutations/link_prepared_announcement_to_announcement.ex:74
#, elixir-autogen, elixir-format
msgctxt "Link prepared announcement to announcement"
msgid "Unable to get prepared announcement to link announcement to"
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/non_draft_not_linked_prepared_announcements.ex:28
#: lib/athena_web/resolvers/interactions/queries/non_draft_not_linked_prepared_announcements.ex:28
#, elixir-autogen, elixir-format
msgctxt "Non draft not linked prepared announcements query"
msgid "Unable to get non draft prepared announcements"
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/prepared_announcement.ex:38
#: lib/athena_web/resolvers/interactions/queries/prepared_announcement.ex:38
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Prepared announcement query"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/prepared_announcement.ex:27
#: lib/athena_web/resolvers/interactions/queries/prepared_announcement.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Prepared announcement query"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:51
#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:51
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update prepared announcement mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:32
#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update prepared announcement mutation"
msgid "Prepared announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:40
#: lib/athena_web/resolvers/interactions/mutations/update_prepared_announcement.ex:40
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update prepared announcement mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media_announcement_viewer_stats.ex:32
#: lib/athena_web/resolvers/interactions/queries/media_announcement_viewer_stats.ex:32
#: lib/athena_web/resolvers/interactions/queries/media_announcement_viewer_stats.ex:45
#: lib/athena_web/resolvers/interactions/queries/media_announcement_viewer_stats.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media announcement viewer stats query"
msgid "Unable to get media announcement viewer stats"
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/deactivate_current_company_distribution_settings.ex:22
#: lib/athena_web/resolvers/flows/mutations/deactivate_current_company_distribution_settings.ex:22
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - DeactivateCurrentCompanyDistributionSettings"
msgid "Unable to deactivate distribution settings"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/create_email.ex:38
#: lib/athena_web/resolvers/comms/mutations/create_email.ex:38
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - CreateEmail mutation"
msgid "Unfortunately we could not create an email campaign at this time, please try again later or contact us for support."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer.ex:41
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer.ex:41
#, elixir-autogen, elixir-format
msgctxt "Athena - CreateShareholderOffer mutation"
msgid "Could not create shareholder offer"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/delete_draft_email.ex:42
#: lib/athena_web/resolvers/comms/mutations/delete_draft_email.ex:42
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - DeleteDraftEmail mutation"
msgid "Unfortunately we could not delete an email campaign at this time, please try again later or contact us for support."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer.ex:36
#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer.ex:36
#, elixir-autogen, elixir-format
msgctxt "Athena - DeleteShareholderOffer mutation"
msgid "Could not delete shareholder offer"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer.ex:29
#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer.ex:29
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - DeleteShareholderOffer mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/duplicate_email_and_email_recipients.ex:43
#: lib/athena_web/resolvers/comms/mutations/duplicate_email_and_email_recipients.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - DuplicateEmailAndEmailRecipients mutation"
msgid "Unfortunately we could not duplicate an email campaign at this time, please try again later or contact us for support."
msgstr ""

#: lib/athena_web/resolvers/comms/queries/estimate_email_recipients_count.ex:29
#: lib/athena_web/resolvers/comms/queries/estimate_email_recipients_count.ex:29
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - EstimateEmailRecipientsCount Query"
msgid "Could not estimate"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/send_email.ex:47
#: lib/athena_web/resolvers/comms/mutations/send_email.ex:47
#, elixir-autogen, elixir-format
msgctxt "Athena - SendEmail mutation"
msgid "Cannot send email campaign."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/send_test_email.ex:34
#: lib/athena_web/resolvers/comms/mutations/send_test_email.ex:34
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - SendTestEmail mutation"
msgid "Unfortunately we could not send a test email at this time, please try again later or contact us for support."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:61
#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:61
#, elixir-autogen, elixir-format
msgctxt "Athena - UpdateEmailAndEmailRecipients mutation"
msgid "Cannot update email campaign."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:60
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:60
#, elixir-autogen, elixir-format
msgctxt "Athena - UpdateEmailSchedule mutation"
msgid "Cannot update email campaign."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_status.ex:54
#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_status.ex:54
#, elixir-autogen, elixir-format
msgctxt "Athena - UpdateShareholderOfferStatus mutation"
msgid "Could not update shareholder offer status"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_status.ex:47
#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_status.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpdateShareholderOfferStatus mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_title.ex:44
#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_title.ex:44
#, elixir-autogen, elixir-format
msgctxt "Athena - UpdateShareholderOfferTitle mutation"
msgid "Could not update shareholder offer title"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_title.ex:37
#: lib/athena_web/resolvers/raises/mutations/update_shareholder_offer_title.ex:37
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - UpdateShareholderOfferTitle mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_comment_from_other_source.ex:39
#: lib/athena_web/resolvers/interactions/mutations/create_media_comment_from_other_source.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create media comment with reply mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_comment_from_other_source.ex:53
#: lib/athena_web/resolvers/interactions/mutations/create_media_comment_from_other_source.ex:53
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create media comment with reply mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_new_media_update.ex:42
#: lib/athena_web/resolvers/interactions/mutations/create_new_media_update.ex:42
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create new media update mutation"
msgid "Oops! Something went wrong. Please try again!"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/create_shareholder_offer_page_instruction.ex:53
#: lib/athena_web/resolvers/companies/mutations/create_shareholder_offer_page_instruction.ex:53
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPageInstruction mutation resolver"
msgid "Could not create instruction"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:83
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:83
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPrivateViewer mutation resolver"
msgid "Email has already been added for this offer"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:76
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:76
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPrivateViewer mutation resolver"
msgid "Email is invalid"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:90
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:90
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPrivateViewer mutation resolver"
msgid "Private viewer could not be created"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:49
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:49
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPrivateViewer mutation resolver"
msgid "Shareholder offer not found"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:65
#: lib/athena_web/resolvers/raises/mutations/create_shareholder_offer_private_viewer.ex:65
#, elixir-autogen, elixir-format
msgctxt "CreateShareholderOfferPrivateViewer mutation resolver"
msgid "You do not have permission to add a private viewer to this shareholder offer"
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/create_utm_link.ex:26
#: lib/athena_web/resolvers/tracking/mutations/create_utm_link.ex:26
#, elixir-autogen, elixir-format
msgctxt "CreateUtmLink mutation resolver"
msgid "Could not create Utm link"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/delete_shareholder_offer_page_faq.ex:34
#: lib/athena_web/resolvers/companies/mutations/delete_shareholder_offer_page_faq.ex:34
#, elixir-autogen, elixir-format
msgctxt "DeleteShareholderOfferPageFaq mutation resolver"
msgid "Unable to delete faq"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/delete_shareholder_offer_page_instruction.ex:41
#: lib/athena_web/resolvers/companies/mutations/delete_shareholder_offer_page_instruction.ex:41
#, elixir-autogen, elixir-format, fuzzy
msgctxt "DeleteShareholderOfferPageInstruction mutation resolver"
msgid "Unable to delete instruction"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:67
#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:67
#, elixir-autogen, elixir-format
msgctxt "DeleteShareholderOfferPrivateViewer mutation resolver"
msgid "Private viewer could not be deleted."
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:36
#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:36
#, elixir-autogen, elixir-format
msgctxt "DeleteShareholderOfferPrivateViewer mutation resolver"
msgid "Private viewer not found"
msgstr ""

#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:52
#: lib/athena_web/resolvers/raises/mutations/delete_shareholder_offer_private_viewer.ex:52
#, elixir-autogen, elixir-format
msgctxt "DeleteShareholderOfferPrivateViewer mutation resolver"
msgid "You do not have permission to add a private viewer to this shareholder offer"
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:44
#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "DeleteUtmLink mutation resolver"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:25
#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:25
#, elixir-autogen, elixir-format
msgctxt "DeleteUtmLink mutation resolver"
msgid "Utm link does not exist."
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:33
#: lib/athena_web/resolvers/tracking/mutations/delete_utm_link.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "DeleteUtmLink mutation resolver"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_ai_summary.ex:18
#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_ai_summary.ex:18
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media announcement Ai Summary mutation"
msgid "Announcement does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_ai_summary.ex:29
#: lib/athena_web/resolvers/interactions/mutations/generate_media_announcement_ai_summary.ex:29
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate media announcement Ai Summary mutation"
msgid "Unable to generate AI summary."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/generate_corporate_page_section_attachment_file_signed_url.ex:39
#: lib/athena_web/resolvers/companies/mutations/generate_corporate_page_section_attachment_file_signed_url.ex:39
#, elixir-autogen, elixir-format
msgctxt "GenerateCorporatePageSectionAttachmentFileSignedUrl mutation"
msgid "Could not create section attachment file"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/generate_shareholder_offer_page_raise_reason_hero_media_video_signed_url.ex:65
#: lib/athena_web/resolvers/companies/mutations/generate_shareholder_offer_page_raise_reason_hero_media_video_signed_url.ex:65
#, elixir-autogen, elixir-format
msgctxt "GenerateShareholderOfferPageRaiseReasonHeroMediaVideoSignedUrl mutation"
msgid "Could not create hero media video"
msgstr ""

#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:43
#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "GetUtmLink query resolver"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:24
#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:24
#, elixir-autogen, elixir-format
msgctxt "GetUtmLink query resolver"
msgid "Utm link does not exist."
msgstr ""

#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:32
#: lib/athena_web/resolvers/tracking/queries/get_utm_link.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "GetUtmLink query resolver"
msgid "You are not authorised."
msgstr ""

#: lib/athena_web/resolvers/raises/queries/shareholder_offer.ex:32
#: lib/athena_web/resolvers/raises/queries/shareholder_offer.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Shareholder offer query"
msgid "Unfortunately the shareholder offer could not be found. Please try again later or contact InvestorHub for support."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:34
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:34
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update posted_at mutation"
msgid "Media update does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:53
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:53
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update posted_at mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:42
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_posted_at.ex:42
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update posted_at mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:35
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:35
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update title mutation"
msgid "Media update does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:68
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:68
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update title mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:57
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:57
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update title mutation"
msgid "Update titles need to be unique. An update with this title already exists, please try another."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:43
#: lib/athena_web/resolvers/interactions/mutations/update_media_update_title.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media update title mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:22
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:22
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:56
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:56
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update pinned media update"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:37
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:37
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:71
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:71
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update pinned media update"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:26
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:26
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:60
#: lib/athena_web/resolvers/interactions/mutations/update_pinned_media_update.ex:60
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update pinned media update"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/upsert_corporate_page.ex:39
#: lib/athena_web/resolvers/companies/mutations/upsert_corporate_page.ex:39
#, elixir-autogen, elixir-format
msgctxt "UpdateCorporatePageStatus mutation"
msgid "Could not update corporate page status"
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:45
#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "UpdateUtmLink mutation resolver"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:26
#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:26
#, elixir-autogen, elixir-format
msgctxt "UpdateUtmLink mutation resolver"
msgid "Utm link does not exist."
msgstr ""

#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:34
#: lib/athena_web/resolvers/tracking/mutations/update_utm_link.ex:34
#, elixir-autogen, elixir-format, fuzzy
msgctxt "UpdateUtmLink mutation resolver"
msgid "You are not authorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_update.ex:55
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_update.ex:55
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media update mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_update.ex:45
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_update.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media update mutation"
msgid "Update titles need to be unique. An update with this title already exists, please try another."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/upsert_shareholder_offer_page.ex:49
#: lib/athena_web/resolvers/companies/mutations/upsert_shareholder_offer_page.ex:49
#, elixir-autogen, elixir-format
msgctxt "UpsertShareholderOfferPage mutation resolver"
msgid "Could not upsert shareholder offer page"
msgstr ""

#: lib/athena_web/resolvers/companies/queries/company_profile_users.ex:43
#: lib/athena_web/resolvers/companies/queries/company_profile_users.ex:43
#, elixir-autogen, elixir-format
msgctxt "Company profile users query"
msgid "Unable to get company profile users"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_update.ex:48
#: lib/athena_web/resolvers/interactions/mutations/create_media_update.ex:48
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create media update mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media_update.ex:37
#: lib/athena_web/resolvers/interactions/mutations/create_media_update.ex:37
#, elixir-autogen, elixir-format
msgctxt "Create media update mutation"
msgid "You are not authorised to create a media update for this media."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement_for_media.ex:32
#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement_for_media.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Create prepared announcement for media mutation"
msgid "Cannot create a prepared announcement"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement_for_media.ex:44
#: lib/athena_web/resolvers/interactions/mutations/create_prepared_announcement_for_media.ex:44
#, elixir-autogen, elixir-format
msgctxt "Create prepared announcement for media mutation"
msgid "Media not found"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:36
#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:36
#, elixir-autogen, elixir-format
msgctxt "Delete media mutation"
msgid "Media not found"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:48
#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:48
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Delete media mutation"
msgid "Oops! Something went wrong. Please try again!"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:24
#: lib/athena_web/resolvers/interactions/mutations/delete_media.ex:24
#, elixir-autogen, elixir-format
msgctxt "Delete media mutation"
msgid "You don't have permission to delete this media"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:31
#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:31
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Edit media update mutation"
msgid "Media update not found."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:50
#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:50
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Edit media update mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:39
#: lib/athena_web/resolvers/interactions/mutations/edit_media_update.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Edit media update mutation"
msgid "You are not authorised to edit this media update."
msgstr ""

#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:45
#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate beneficial owners disclosed interest document signed url mutation"
msgid "Report does not exist."
msgstr ""

#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:64
#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:64
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate beneficial owners disclosed interest document signed url mutation"
msgid "Unable to generate signed url."
msgstr ""

#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:53
#: lib/athena_web/resolvers/beneficial-owners/mutations/generate_disclosed_interest_document_signed_url.ex:53
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate beneficial owners disclosed interest document signed url mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/flows/mutations/generate_social_thumbnail_signed_url.ex:31
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Generate social thumbnail signed url mutation"
msgid "Unable to generate signed url."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media.ex:21
#: lib/athena_web/resolvers/interactions/queries/media.ex:21
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media query"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media.ex:29
#: lib/athena_web/resolvers/interactions/queries/media.ex:29
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media query"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/media_viewer_stats.ex:38
#: lib/athena_web/resolvers/interactions/queries/media_viewer_stats.ex:38
#: lib/athena_web/resolvers/interactions/queries/media_viewer_stats.ex:49
#: lib/athena_web/resolvers/interactions/queries/media_viewer_stats.ex:49
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Media viewer stats query"
msgid "Unable to get media viewer stats"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:33
#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:33
#, elixir-autogen, elixir-format
msgctxt "Publish media mutation"
msgid "Media not found."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:47
#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:40
#: lib/athena_web/resolvers/interactions/mutations/publish_media.ex:40
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media mutation"
msgid "You are not authorised to publish this media."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:33
#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media social post mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:43
#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media social post mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:36
#: lib/athena_web/resolvers/interactions/mutations/publish_media_social_post.ex:36
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media social post mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:30
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:30
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media update content mutation"
msgid "Media update not found."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:44
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:44
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media update content mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:37
#: lib/athena_web/resolvers/interactions/mutations/publish_media_update_content.ex:37
#, elixir-autogen, elixir-format
msgctxt "Publish media update content mutation"
msgid "You are not authorised to publish this media update content."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:37
#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:37
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Sort content calendar mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:56
#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:56
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Sort content calendar mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:45
#: lib/athena_web/resolvers/interactions/mutations/sort_content_calendar.ex:45
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Sort content calendar mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:24
#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:24
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:43
#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:43
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:32
#: lib/athena_web/resolvers/interactions/mutations/update_media.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Update media mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/beneficial-owners/mutations/upload_beneficial_owners_report.ex:47
#: lib/athena_web/resolvers/beneficial-owners/mutations/upload_beneficial_owners_report.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "UploadBeneficialOwnersReport mutation resolver"
msgid "Could not upload file."
msgstr ""

#: lib/athena_web/resolvers/beneficial-owners/mutations/upload_beneficial_owners_report.ex:55
#: lib/athena_web/resolvers/beneficial-owners/mutations/upload_beneficial_owners_report.ex:55
#, elixir-autogen, elixir-format
msgctxt "UploadBeneficialOwnersReport mutation resolver"
msgid "Unexpected error."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:29
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:29
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media social post mutation"
msgid "Media does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:39
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:39
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media social post mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:32
#: lib/athena_web/resolvers/interactions/mutations/upsert_media_social_post.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Upsert media social post mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/create_media.ex:83
#: lib/athena_web/resolvers/interactions/mutations/create_media.ex:83
#, elixir-autogen, elixir-format
msgctxt "CreateMedia mutation"
msgid "Media creation failed"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/create_custom_domain_swap.ex:59
#: lib/athena_web/resolvers/companies/mutations/create_custom_domain_swap.ex:59
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - CreateCustomDomainSwap mutation"
msgid "Unfortunately, we could not create your custom domain swap at this time, please try again later."
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/switch_custom_domain.ex:127
#: lib/athena_web/resolvers/companies/mutations/switch_custom_domain.ex:127
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Athena - SwitchCustomDomain mutation"
msgid "Unfortunately, we could not create your custom domain at this time, please try again later."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:36
#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:36
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Duplicate media mutation"
msgid "Media not found"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:48
#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:48
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Duplicate media mutation"
msgid "Oops! Something went wrong. Please try again!"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:24
#: lib/athena_web/resolvers/interactions/mutations/duplicate_media.ex:24
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Duplicate media mutation"
msgid "You don't have permission to duplicate this media"
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:47
#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media selected distribution channels mutation"
msgid "Media not found."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:66
#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:66
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media selected distribution channels mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:55
#: lib/athena_web/resolvers/interactions/mutations/publish_media_selected_distribution_channels.ex:55
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Publish media selected distribution channels mutation"
msgid "You are not authorised to publish this media."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:42
#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:42
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Unpublish media update mutation"
msgid "Unable to unpublish update."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:33
#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Unpublish media update mutation"
msgid "Update does not exist."
msgstr ""

#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:36
#: lib/athena_web/resolvers/interactions/mutations/unpublish_media_update.ex:36
#, elixir-autogen, elixir-format, fuzzy
msgctxt "Unpublish media update mutation"
msgid "You are unauthorised."
msgstr ""
