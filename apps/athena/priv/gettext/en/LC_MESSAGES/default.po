# # "msgid"s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove "msgid"s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use "mix gettext.extract --merge" or "mix gettext.merge"
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"

#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:99
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:99
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:211
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:211
#, elixir-autogen, elixir-format
msgid "Fail to add user into your company."
msgstr "Oops! Something went wrong. Please try to add the user again."

#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:69
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:69
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:181
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:181
#, elixir-autogen, elixir-format
msgid "Unable to create user at the moment, please try again."
msgstr "Oops! Something went wrong. Please try to add the user again."

#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:84
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:84
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:196
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:196
#, elixir-autogen, elixir-format
msgid "User already exist in your company."
msgstr "It looks like this user already exists."

#: lib/athena_web/resolvers/companies/mutations/change_password.ex:18
#: lib/athena_web/resolvers/companies/mutations/change_password.ex:18
#, elixir-autogen, elixir-format
msgid "Invalid password."
msgstr "The password you entered is incorrect."

#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:22
#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:22
#: lib/athena_web/resolvers/companies/mutations/change_password.ex:24
#: lib/athena_web/resolvers/companies/mutations/change_password.ex:24
#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:55
#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:55
#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:39
#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:39
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:129
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:129
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:226
#: lib/athena_web/resolvers/companies/mutations/invite_and_create_company_profile_users.ex:226
#: lib/athena_web/resolvers/companies/mutations/resend_invitation_token.ex:37
#: lib/athena_web/resolvers/companies/mutations/resend_invitation_token.ex:37
#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:35
#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:35
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:70
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_rejected.ex:70
#, elixir-autogen, elixir-format
msgid "Oops! Something went wrong."
msgstr "Oops! Something went wrong."

#: lib/athena_web/middleware/permission.ex:38
#: lib/athena_web/middleware/permission.ex:38
#: lib/athena_web/middleware/permission.ex:54
#: lib/athena_web/middleware/permission.ex:54
#: lib/athena_web/resolvers/companies/mutations/change_shareholder_offer_us_citzen_check_resolver.ex:37
#: lib/athena_web/resolvers/companies/mutations/change_shareholder_offer_us_citzen_check_resolver.ex:37
#, elixir-autogen, elixir-format
msgid "You are unauthorized or don't have required permission!"
msgstr "It looks like you don't have permission to do this."

#: lib/athena_web/resolvers/companies/queries/company_profile_user_from_invitation_token.ex:35
#: lib/athena_web/resolvers/companies/queries/company_profile_user_from_invitation_token.ex:35
#, elixir-autogen, elixir-format
msgid "Your invitation token is invalid! Please ask your admin to invite you again"
msgstr "Your invitation has expired! Please ask your admin to invite you again."

#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:27
#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:27
#, elixir-autogen, elixir-format`
msgid "Password already set, please login"
msgstr "You have already set a password for you account. Please use your existing details to login or click 'fogot password' to reset your password"

#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:30
#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:30
#, elixir-autogen, elixir-format
msgid "Please enter the same password."
msgstr "The passwords you have entered don't match."

#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:33
#: lib/athena_web/resolvers/companies/mutations/set_password_from_invitation_token.ex:33
#, elixir-autogen, elixir-format
msgid "Your invitation token is invalid. Contact the person who invited you or InvestorHub"
msgstr "Your invitation has expired! Please ask your admin to invite you again."

#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:46
#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:46
#, elixir-autogen, elixir-format
msgid "Can not confirm user details information!"
msgstr "Oops! Something went wrong, please try again."

#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:32
#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:32
#, elixir-autogen, elixir-format
msgid "can not update company name"
msgstr "Oops! Something went wrong when changing your company name, please try again."

#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:35
#: lib/athena_web/resolvers/companies/mutations/confirm_company_profile_information.ex:35
#, elixir-autogen, elixir-format
msgid "can not update ticker"
msgstr "Oops! Something went wrong when updating your ticker, please try again."

#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:29
#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:29
#, elixir-autogen, elixir-format
msgid "Can not change title for this user"
msgstr "Oops! Something went wrong when changing this user's title, please try again."

#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:26
#: lib/athena_web/resolvers/companies/mutations/update_company_user_and_company_profile_user_information.ex:26
#, elixir-autogen, elixir-format
msgid "Can not update user details information!"
msgstr "Oops! Something went wrong when updating your details, please try again."

#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:25
#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:25
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:28
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:28
#, elixir-autogen, elixir-format
msgid "can not create new ticker"
msgstr "This ticker is already being used."

#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:22
#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:22
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:25
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:25
#, elixir-autogen, elixir-format
msgid "can not invalidate existing ticker"
msgstr "Oops! Something went wrong"

#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:19
#: lib/athena_web/resolvers/companies/mutations/update_company_infomation.ex:19
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:22
#: lib/athena_web/resolvers/companies/mutations/update_company_investor_hub.ex:22
#, elixir-autogen, elixir-format
msgid "can not update company profile"
msgstr "Oops! Something went wrong when updating your company details, please try again."

#: lib/athena_web/resolvers/companies/mutations/activate_company_profile_user.ex:38
#: lib/athena_web/resolvers/companies/mutations/activate_company_profile_user.ex:38
#, elixir-autogen, elixir-format
msgid "can not activate user to this company"
msgstr "We cannot activate this user at this time. Please try again later or contact your InvestorHub Account Manager."

#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:60
#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:60
#, elixir-autogen, elixir-format
msgid "Could not create notification preference in database"
msgstr "Oops! Something went wrong when updating your notification preferences, please try again."

#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:74
#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:74
#, elixir-autogen, elixir-format
msgid "Could not update notification preference in database"
msgstr "Oops! Something went wrong when updating your notification preferences, please try again."

#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:28
#: lib/athena_web/resolvers/comms/mutations/upsert_notification_preference.ex:28
#, elixir-autogen, elixir-format
msgid "Invalid notification preference scope"
msgstr "Oops! Something went wrong when updating your notification preferences. Please try again."

#: lib/athena_web/resolvers/investor-hub-analysis/queries/current_company.ex:10
#: lib/athena_web/resolvers/investor-hub-analysis/queries/current_company.ex:10
#, elixir-autogen, elixir-format
msgid "Unable to get current company"
msgstr "Oops! Something went wrong."

#: lib/athena_web/resolvers/companies/mutations/invalidate_company_profile_user.ex:27
#: lib/athena_web/resolvers/companies/mutations/invalidate_company_profile_user.ex:27
#, elixir-autogen, elixir-format
msgid "Can not remove yourself."
msgstr "You cannot remove yourself from the company."

#: lib/athena_web/resolvers/dashboard/mutations/send_start_planning_spp_email.ex:30
#: lib/athena_web/resolvers/dashboard/mutations/send_start_planning_spp_email.ex:30
#, elixir-autogen, elixir-format
msgid "Oops, looks like something went wrong. Please fill in the form again and press submit. If the issue persists, contact InvestorHub."
msgstr ""

#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:69
#: lib/athena_web/resolvers/investors/mutations/mark_investor_certificate_as_verified.ex:69
#, elixir-autogen, elixir-format
msgid "Could not mark certificate as verified"
msgstr ""

#: lib/athena_web/resolvers/registers/queries/campaign_shareholders_by_activity_type.ex:40
#: lib/athena_web/resolvers/registers/queries/campaign_shareholders_by_activity_type.ex:40
#, elixir-autogen, elixir-format
msgid "Unable to get shareholdings for campaign channel by activity type"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/remove_custom_domain.ex:32
#: lib/athena_web/resolvers/companies/mutations/remove_custom_domain.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgid "Unable to remove custom domain"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/verify_custom_domain_swap.ex:57
#: lib/athena_web/resolvers/companies/mutations/verify_custom_domain_swap.ex:57
#: lib/athena_web/resolvers/companies/mutations/verify_custom_domain_v2.ex:58
#: lib/athena_web/resolvers/companies/mutations/verify_custom_domain_v2.ex:58
#, elixir-autogen, elixir-format
msgid "Unfortunately we could not verify the connection at this time, please try again later."
msgstr ""

#: lib/athena_web/middleware/block_cloud_ip.ex:113
#: lib/athena_web/middleware/block_cloud_ip.ex:113
#, elixir-autogen, elixir-format
msgid "Access denied!"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:97
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:97
#, elixir-autogen, elixir-format
msgid "Campaign does not exist"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:109
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:109
#, elixir-autogen, elixir-format, fuzzy
msgid "Cannot update email campaign"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:46
#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:46
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:45
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:45
#, elixir-autogen, elixir-format
msgid "Email campaign does not exist."
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:103
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:103
#, elixir-autogen, elixir-format
msgid "Email campaign has already been sent"
msgstr ""

#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:52
#: lib/athena_web/resolvers/comms/mutations/update_email_and_email_recipients.ex:52
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:51
#: lib/athena_web/resolvers/comms/mutations/update_email_schedule.ex:51
#, elixir-autogen, elixir-format
msgid "Email campaign has already been sent."
msgstr ""

#: lib/athena_web/resolvers/webinars/create_webinar.ex:53
#: lib/athena_web/resolvers/webinars/create_webinar.ex:53
#, elixir-autogen, elixir-format
msgid "Could not create webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:30
#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:30
#, elixir-autogen, elixir-format
msgid "Could not create webinar document"
msgstr ""

#: lib/athena_web/resolvers/webinars/delete_webinar.ex:30
#: lib/athena_web/resolvers/webinars/delete_webinar.ex:30
#, elixir-autogen, elixir-format
msgid "Could not delete webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:33
#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:33
#, elixir-autogen, elixir-format
msgid "Could not delete webinar document"
msgstr ""

#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:32
#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:32
#, elixir-autogen, elixir-format
msgid "Could not sort webinar documents"
msgstr ""

#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:32
#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:32
#, elixir-autogen, elixir-format
msgid "Could not start webinar recording"
msgstr ""

#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:36
#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:36
#, elixir-autogen, elixir-format
msgid "Could not stop webinar recording"
msgstr ""

#: lib/athena_web/resolvers/webinars/update_webinar.ex:31
#: lib/athena_web/resolvers/webinars/update_webinar.ex:31
#, elixir-autogen, elixir-format
msgid "Could not update webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:30
#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:30
#, elixir-autogen, elixir-format
msgid "Could not update webinar document"
msgstr ""

#: lib/athena_web/resolvers/webinars/create_webinar.ex:45
#: lib/athena_web/resolvers/webinars/create_webinar.ex:45
#, elixir-autogen, elixir-format
msgid "Failed to create webinar: %{reason}"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinars.ex:28
#: lib/athena_web/resolvers/webinars/list_webinars.ex:28
#, elixir-autogen, elixir-format
msgid "Failed to fetch webinars"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinars.ex:48
#: lib/athena_web/resolvers/webinars/list_webinars.ex:48
#, elixir-autogen, elixir-format
msgid "Failed to get the total number of webinars"
msgstr ""

#: lib/athena_web/resolvers/webinars/delete_webinar.ex:23
#: lib/athena_web/resolvers/webinars/delete_webinar.ex:23
#, elixir-autogen, elixir-format
msgid "Unauthorised"
msgstr ""

#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:23
#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:23
#, elixir-autogen, elixir-format
msgid "Unauthorised to create document for this webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:23
#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:23
#, elixir-autogen, elixir-format
msgid "Unauthorised to update this webinar document"
msgstr ""

#: lib/athena_web/resolvers/webinars/sync_webinar.ex:33
#: lib/athena_web/resolvers/webinars/sync_webinar.ex:33
#, elixir-autogen, elixir-format
msgid "Unauthorized"
msgstr ""

#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:26
#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:26
#, elixir-autogen, elixir-format
msgid "Unauthorized to delete this webinar document"
msgstr ""

#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:25
#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:25
#, elixir-autogen, elixir-format
msgid "Unauthorized to sort documents for this webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:25
#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:25
#, elixir-autogen, elixir-format
msgid "Unauthorized to start recording for this webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:29
#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:29
#, elixir-autogen, elixir-format
msgid "Unauthorized to stop recording for this webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/update_webinar.ex:24
#: lib/athena_web/resolvers/webinars/update_webinar.ex:24
#, elixir-autogen, elixir-format
msgid "Unauthorized to update this webinar"
msgstr ""

#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:19
#: lib/athena_web/resolvers/webinars/delete_webinar_document.ex:19
#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:16
#: lib/athena_web/resolvers/webinars/update_webinar_document.ex:16
#, elixir-autogen, elixir-format
msgid "Webinar document not found"
msgstr ""

#: lib/athena_web/resolvers/webinars/check_webinar_has_synced_with_hms.ex:16
#: lib/athena_web/resolvers/webinars/check_webinar_has_synced_with_hms.ex:16
#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:16
#: lib/athena_web/resolvers/webinars/create_webinar_document.ex:16
#: lib/athena_web/resolvers/webinars/delete_webinar.ex:16
#: lib/athena_web/resolvers/webinars/delete_webinar.ex:16
#: lib/athena_web/resolvers/webinars/get_post_webinar_audience_movement.ex:28
#: lib/athena_web/resolvers/webinars/get_post_webinar_audience_movement.ex:28
#: lib/athena_web/resolvers/webinars/get_webinar.ex:13
#: lib/athena_web/resolvers/webinars/get_webinar.ex:13
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:40
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:40
#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:39
#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:39
#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:18
#: lib/athena_web/resolvers/webinars/sort_webinar_documents.ex:18
#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:18
#: lib/athena_web/resolvers/webinars/start_webinar_recording.ex:18
#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:22
#: lib/athena_web/resolvers/webinars/stop_webinar_recording.ex:22
#: lib/athena_web/resolvers/webinars/sync_webinar.ex:13
#: lib/athena_web/resolvers/webinars/sync_webinar.ex:13
#: lib/athena_web/resolvers/webinars/update_webinar.ex:17
#: lib/athena_web/resolvers/webinars/update_webinar.ex:17
#, elixir-autogen, elixir-format
msgid "Webinar not found"
msgstr ""

#: lib/athena_web/resolvers/webinars/sync_webinar.ex:26
#: lib/athena_web/resolvers/webinars/sync_webinar.ex:26
#, elixir-autogen, elixir-format
msgid "Failed to sync."
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/list_medias.ex:41
#: lib/athena_web/resolvers/interactions/queries/list_medias.ex:41
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to fetch medias"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:47
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:47
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to fetch webinar attendees"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:46
#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:46
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to fetch webinar investor users"
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/list_medias.ex:61
#: lib/athena_web/resolvers/interactions/queries/list_medias.ex:61
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to get the total number of medias"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:65
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:65
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to get the total number of webinar attendees"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:71
#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:71
#, elixir-autogen, elixir-format, fuzzy
msgid "Failed to get the total number of webinar investor users"
msgstr ""

#: lib/athena_web/resolvers/csm/mutations/register_interest_in_feature.ex:20
#: lib/athena_web/resolvers/csm/mutations/register_interest_in_feature.ex:20
#, elixir-autogen, elixir-format
msgid "Feature not found"
msgstr ""

#: lib/athena_web/resolvers/interactions/queries/ai_answer_comments.ex:27
#: lib/athena_web/resolvers/interactions/queries/ai_answer_comments.ex:27
#, elixir-autogen, elixir-format, fuzzy
msgid "Unable to generate AI answer at the moment, please try again."
msgstr "Oops! Something went wrong. Please try to add the user again."

#: lib/athena_web/resolvers/webinars/check_webinar_has_synced_with_hms.ex:23
#: lib/athena_web/resolvers/webinars/check_webinar_has_synced_with_hms.ex:23
#, elixir-autogen, elixir-format, fuzzy
msgid "Unauthorized to check if this webinar ended"
msgstr ""

#: lib/athena_web/resolvers/webinars/get_post_webinar_audience_movement.ex:21
#: lib/athena_web/resolvers/webinars/get_post_webinar_audience_movement.ex:21
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:33
#: lib/athena_web/resolvers/webinars/list_webinar_attendees.ex:33
#, elixir-autogen, elixir-format, fuzzy
msgid "Unauthorized to fetch webinar attendees"
msgstr ""

#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:32
#: lib/athena_web/resolvers/webinars/list_webinar_investor_users.ex:32
#, elixir-autogen, elixir-format, fuzzy
msgid "Unauthorized to fetch webinar investor users"
msgstr ""

#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:49
#: lib/athena_web/resolvers/companies/mutations/comfirm_company_user_information_and_active_company_profile_user.ex:49
#, elixir-autogen, elixir-format, fuzzy
msgid "Can not activate user for this company"
msgstr "Oops! Something went wrong, please try again."

#: lib/athena_web/resolvers/companies/mutations/remove_custom_domain_swap.ex:22
#: lib/athena_web/resolvers/companies/mutations/remove_custom_domain_swap.ex:22
#, elixir-autogen, elixir-format
msgid "Unable to cancel custom domain swap"
msgstr ""
