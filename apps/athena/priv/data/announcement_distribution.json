{"counters": {"u_row": 9, "u_column": 9, "u_content_html": 3, "u_content_text": 11, "u_content_image": 1, "u_content_button": 1, "u_content_social": 2, "u_content_divider": 1}, "body": {"id": "20b2WVuj4e", "rows": [{"id": "h78px8UADS", "cells": [1], "columns": [{"id": "-3sySGDXhg", "contents": [{"id": "JCQaW4oItU", "type": "html", "values": {"html": " <!--[if mso | IE]><table role=\"presentation\" width=\"600\" cellspacing=\"0\" border=\"0\" cellpadding=\"0\" align=\"center\" style=\"width:600px;\"><tr><td style=\"line-height:0px;font-size:0px;mso-line-height-rule:exactly;\"><![endif]-->\n    <div style=\"margin:0px auto;max-width:600px;\">\n      <table cellpadding=\"0\" role=\"presentation\" align=\"center\" border=\"0\" cellspacing=\"0\" style=\"width:100%;\">\n        <tbody>\n          <tr>\n            <td style=\"direction:ltr;font-size:0px;padding:24px;text-align:center;\">\n              <!--[if mso | IE]><table border=\"0\" cellpadding=\"0\" role=\"presentation\" cellspacing=\"0\"><![endif]-->\n              <!--[if mso | IE]><tr><![endif]-->\n              <!--[if mso | IE]><td style=\"vertical-align:top;width:600px;\"><![endif]-->\n              <div class=\"mj-column-per-100 mj-outlook-group-fix\"\n                style=\"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;\">\n                <table width=\"100%\" role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"\n                  style=\"vertical-align:top;\">\n                  <tbody>\n                    <tr>\n                      <td align=\"center\" style=\"font-size:0px;padding:10px 25px;word-break:break-word;\">\n                        <table border=\"0\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\"\n                          style=\"border-collapse:collapse;border-spacing:0px;\">\n                          <tbody>\n                            <tr>\n                              <td style=\"width:96px;\"><img src=\"{{ company_logo }}\" width=\"96\" height=\"96\"\n                                  style=\"border:0;display:block;outline:none;text-decoration:none;height:96px;width:100%;font-size:13px;\" />\n                              </td>\n                            </tr>\n                          </tbody>\n                        </table>\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n              <!--[if mso | IE]></td><![endif]-->\n              <!--[if mso | IE]></tr><![endif]-->\n              <!--[if mso | IE]></table><![endif]-->\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>", "hideDesktop": false, "displayCondition": null, "containerPadding": "10px", "anchor": "", "_meta": {"htmlID": "u_content_html_1", "htmlClassNames": "u_content_html"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}], "values": {"_meta": {"htmlID": "u_column_6", "htmlClassNames": "u_column"}, "border": {}, "padding": "0px", "borderRadius": "0px", "backgroundColor": ""}}], "values": {"displayCondition": null, "columns": false, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "top-center", "customPosition": ["50%", "0%"]}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_6", "htmlClassNames": "u_row"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}, {"id": "BNmTw9vXXh", "cells": [1], "columns": [{"id": "--OtQHdw5N", "contents": [{"id": "-GszJCQHL3", "type": "text", "values": {"containerPadding": "32px 0px 0px", "anchor": "", "textAlign": "left", "lineHeight": "130%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_2", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 130%;\"><span style=\"font-size: 16px; line-height: 20.8px;\">Hi {{ first_name }},</span></p>"}}, {"id": "7IrfeSrmNJ", "type": "text", "values": {"containerPadding": "32px 0px 0px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_3", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%;\"><span style=\"font-size: 16px; line-height: 22.4px; font-family: arial, helvetica, sans-serif;\">We've just released the following announcement to the {{ market }}:</span></p>"}}, {"id": "jBKs16uJZ7", "type": "text", "values": {"containerPadding": "8px 0px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_9", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%;\"><strong><span style=\"font-size: 16px; line-height: 22.4px; font-family: arial, helvetica, sans-serif;\">{{ market }}:{{ ticker }} {{ announcement_title }}</span></strong></p>"}}, {"id": "LIfz59x4nv", "type": "text", "values": {"containerPadding": "8px 0px 16px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_10", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%;\"><span style=\"font-size: 16px; line-height: 22.4px;\">To view the full announcement and join in with the conversation, go to our investor hub:</span></p>"}}, {"id": "wDwvt_o0WL", "type": "button", "values": {"containerPadding": "0px", "anchor": "", "href": {"name": "web", "values": {"href": "{{ announcement_url }}", "target": "_blank"}, "attrs": {"href": "{{href}}", "target": "{{target}}"}}, "buttonColors": {"color": "#FFFFFF", "backgroundColor": "#000c0a", "hoverColor": "#FFFFFF", "hoverBackgroundColor": "#3AAEE0"}, "size": {"autoWidth": true, "width": "100%"}, "textAlign": "left", "lineHeight": "120%", "padding": "13px 16px", "border": {}, "borderRadius": "8px", "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_button_1", "htmlClassNames": "u_content_button"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<span style=\"font-size: 14px; line-height: 16.8px; font-family: arial, helvetica, sans-serif;\">Go to investor hub</span>", "calculatedWidth": 150, "calculatedHeight": 43}}, {"id": "1Jm9QIR7g2", "type": "divider", "values": {"width": "100%", "border": {"borderTopWidth": "1px", "borderTopStyle": "solid", "borderTopColor": "#BBBBBB"}, "textAlign": "center", "containerPadding": "32px 0px", "anchor": "", "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_divider_1", "htmlClassNames": "u_content_divider"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}, {"id": "rBA8xvFVdi", "type": "text", "values": {"containerPadding": "0px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_4", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%;\"><span style=\"font-size: 16px; line-height: 22.4px;\">Thanks,</span></p>\n<p style=\"font-size: 14px; line-height: 140%;\"><span style=\"font-size: 16px; line-height: 22.4px;\">{{ company_name }}</span></p>"}}, {"id": "qY-mhzwUCL", "type": "text", "values": {"containerPadding": "24px 0px 16px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_5", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%;\"><span style=\"font-size: 14px; line-height: 19.6px; color: #7e8c8d;\">Want to stop seeing email notifications? <a rel=\"noopener\" href=\"{{ unsubscribe_url }}\" target=\"_blank\" data-u-link-value=\"eyJuYW1lIjoid2ViIiwiYXR0cnMiOnsiaHJlZiI6Int7aHJlZn19IiwidGFyZ2V0Ijoie3t0YXJnZXR9fSJ9LCJ2YWx1ZXMiOnsiaHJlZiI6Int7IHVuc3Vic2NyaWJlX2xpbmsgfX0iLCJ0YXJnZXQiOiJfYmxhbmsifX0=\">Unsubscribe</a></span></p>"}}], "values": {"_meta": {"htmlID": "u_column_3", "htmlClassNames": "u_column"}, "border": {}, "padding": "32px", "borderRadius": "0px", "backgroundColor": "#ffffff"}}], "values": {"displayCondition": null, "columns": false, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "top-center", "customPosition": ["50%", "0%"]}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}, {"id": "ghHpg1rhsN", "cells": [1], "columns": [{"id": "BjIVR6Bl8_", "contents": [{"id": "prUkCVYVhT", "type": "text", "values": {"containerPadding": "10px", "anchor": "", "textAlign": "left", "lineHeight": "140%", "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkHoverColor": "#0000ee", "linkUnderline": true, "linkHoverUnderline": true}, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_text_11", "htmlClassNames": "u_content_text"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true, "text": "<p style=\"font-size: 14px; line-height: 140%; text-align: center;\">Visit us at <a rel=\"noopener\" href=\"{{ investor_hub_url }}\" target=\"_blank\" data-u-link-value=\"eyJuYW1lIjoid2ViIiwiYXR0cnMiOnsiaHJlZiI6Int7aHJlZn19IiwidGFyZ2V0Ijoie3t0YXJnZXR9fSJ9LCJ2YWx1ZXMiOnsiaHJlZiI6Int7IGludmVzdG9yX2h1Yl91cmwgfX0iLCJ0YXJnZXQiOiJfYmxhbmsifX0=\">{{ investor_hub_url }}</a></p>"}}], "values": {"_meta": {"htmlID": "u_column_7", "htmlClassNames": "u_column"}, "border": {}, "padding": "0px", "borderRadius": "0px", "backgroundColor": ""}}], "values": {"displayCondition": null, "columns": false, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "top-center", "customPosition": ["50%", "0%"]}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_7", "htmlClassNames": "u_row"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}, {"id": "CpNcjF7r5S", "cells": [1], "columns": [{"id": "Jy1kT6yDnT", "contents": [{"id": "uW8nf3_-a-", "type": "social", "values": {"containerPadding": "6px", "anchor": "", "icons": {"iconType": "rounded-black", "icons": [{"url": "{{ facebook_link }}", "name": "Facebook"}, {"url": "{{ linkedin_link }}", "name": "LinkedIn"}, {"url": "{{ twitter_link }}", "name": "Twitter"}], "editor": {"data": {"showDefaultIcons": true, "showDefaultOptions": true, "customIcons": [], "customOptions": []}}}, "align": "center", "spacing": 30, "hideDesktop": false, "displayCondition": null, "_meta": {"htmlID": "u_content_social_2", "htmlClassNames": "social_icons"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}], "values": {"_meta": {"htmlID": "u_column_9", "htmlClassNames": "u_column"}, "border": {}, "padding": "0px", "borderRadius": "0px", "backgroundColor": ""}}], "values": {"displayCondition": null, "columns": false, "backgroundColor": "", "columnsBackgroundColor": "", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "center"}, "padding": "0px", "anchor": "", "hideDesktop": false, "_meta": {"htmlID": "u_row_9", "htmlClassNames": "u_row"}, "selectable": true, "draggable": true, "duplicatable": true, "deletable": true, "hideable": true}}], "values": {"popupPosition": "center", "popupWidth": "600px", "popupHeight": "auto", "borderRadius": "10px", "contentAlign": "center", "contentVerticalAlign": "center", "contentWidth": "600px", "fontFamily": {"label": "Roboto", "value": "'Roboto', sans-serif", "url": "https://fonts.googleapis.com/css2?family=Roboto&display=swap", "defaultFont": false}, "textColor": "#1a1a1a", "popupBackgroundColor": "#FFFFFF", "popupBackgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "cover", "position": "top-center", "customPosition": ["50%", "0%"]}, "popupOverlay_backgroundColor": "rgba(0, 0, 0, 0.1)", "popupCloseButton_position": "top-right", "popupCloseButton_backgroundColor": "#DDDDDD", "popupCloseButton_iconColor": "#000000", "popupCloseButton_borderRadius": "0px", "popupCloseButton_margin": "0px", "popupCloseButton_action": {"name": "close_popup", "attrs": {"onClick": "document.querySelector('.u-popup-container').style.display = 'none';"}}, "backgroundColor": "#f9fafb", "backgroundImage": {"url": "", "fullWidth": true, "repeat": "no-repeat", "size": "custom", "position": "top-center", "customPosition": ["50%", "0%"]}, "preheaderText": "", "linkStyle": {"body": true, "linkColor": "#1a1a1a", "linkHoverColor": "#0000ee", "linkUnderline": false, "linkHoverUnderline": true, "inherit": false}, "_meta": {"htmlID": "u_body", "htmlClassNames": "u_body"}}}, "schemaVersion": 11}