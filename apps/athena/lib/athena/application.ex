defmodule Athena.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    Logger.add_backend(Sentry.LoggerBackend)
    Logger.add_backend(AthenaWeb.LoggerBackend)

    CloudIpFetcher.add_cloud_ips()

    children = [
      # Start the Telemetry supervisor
      AthenaWeb.Telemetry,
      # Start the Endpoint (http/https)
      AthenaWeb.Endpoint,
      {Absinthe.Subscription, AthenaWeb.Endpoint},
      AthenaWeb.EventCache
    ]

    :telemetry.attach_many(
      :athena,
      [
        [:absinthe, :execute, :operation, :start],
        [:absinthe, :subscription, :publish, :start],
        [:absinthe, :resolve, :field, :start],
        [:absinthe, :middleware, :batch, :start]
      ],
      &Helper.LogResponseHandler.handle_event/4,
      [AthenaWeb.EventCache]
    )

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Athena.Supervisor]
    Supervisor.start_link(children, opts)
  end

  def extract_event([:absinthe, event, _, _]), do: Atom.to_string(event)
  def extract_event(_), do: ""

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    AthenaWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
