defmodule AthenaWeb.Middleware.Analytics do
  @moduledoc false

  def call(
        %Absinthe.Resolution{
          arguments: arguments,
          context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{id: user_id}}},
          errors: []
        } = resolution,
        %{event: event} = config
      ) do
    if Map.get(config, :hide_args, false) do
      Task.async(fn -> Analytics.track("athena", event, user_id) end)
    else
      Task.async(fn -> Analytics.track("athena", event, user_id, arguments) end)
    end

    resolution
  end

  def call(resolution, _config), do: resolution
end
