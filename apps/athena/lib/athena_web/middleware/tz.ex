defmodule AthenaWeb.Middleware.TZ do
  @moduledoc false
  @behaviour Absinthe.Middleware

  def call(
        %Absinthe.Resolution{
          arguments: %{start_date: _start_date, end_date: _end_date} = arguments,
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{timezone: timezone}
            }
          },
          errors: []
        } = resolution,
        _config
      ) do
    arguments =
      arguments
      |> Map.update!(:start_date, &convert_naive_with_tz(&1, timezone))
      |> Map.update!(:end_date, &convert_naive_with_tz(&1, timezone))

    %{resolution | arguments: arguments}
  end

  def call(resolution, _config), do: resolution

  defp convert_naive_with_tz(%NaiveDateTime{} = date, timezone) do
    date
    |> Timex.to_datetime(timezone)
    |> Timex.to_naive_datetime()
  end

  defp convert_naive_with_tz(date, _timezone), do: date
end
