defmodule AthenaWeb.Middleware.Permission do
  @moduledoc "Permission Middleware"

  @behaviour Absinthe.Middleware

  use Gettext, backend: AthenaWeb.Gettext

  def call(%{context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{}}} = resolution, %{
        permission: "authorized"
      }),
      do: resolution

  def call(%{context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{}}} = resolution, %{
        permission: "authorised"
      }),
      do: resolution

  # The `current_company_user` pattern match is for when user first logged in and has not yet selected a company
  def call(%{context: %{current_company_user: %Gaia.Companies.User{}}} = resolution, %{permission: "authorized"}),
    do: resolution

  def call(%{context: %{current_company_user: %Gaia.Companies.User{}}} = resolution, %{permission: "authorised"}),
    do: resolution

  def call(
        %{context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{permissions: permissions}}} = resolution,
        %{permission: permission} = input
      ) do
    has_permission = Enum.any?(permissions, &(&1.name == permission))

    cond do
      !has_permission and Map.has_key?(input, :error_return) and is_tuple(input.error_return) ->
        Absinthe.Resolution.put_result(resolution, input.error_return)

      !has_permission ->
        Absinthe.Resolution.put_result(
          resolution,
          {:error, gettext("You are unauthorized or don't have required permission!")}
        )

      true ->
        resolution
    end
  end

  def call(resolution, %{error_return: error_return}) when is_tuple(error_return) do
    Absinthe.Resolution.put_result(resolution, error_return)
  end

  def call(resolution, _opts),
    do:
      Absinthe.Resolution.put_result(
        resolution,
        {:error, gettext("You are unauthorized or don't have required permission!")}
      )
end
