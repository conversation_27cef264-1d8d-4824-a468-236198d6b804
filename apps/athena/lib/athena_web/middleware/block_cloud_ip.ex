defmodule AthenaWeb.Middleware.BlockCloudIP do
  @moduledoc """
  BlockCloudIP middleware
  """
  @behaviour Absinthe.Middleware

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.CloudIps
  alias Gaia.CloudIps.CloudIp
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User

  require Logger

  def call(
        %Absinthe.Resolution{
          context:
            %{
              cloud_ip: cloud_ip,
              user_agent: user_agent,
              request_path: request_path,
              current_company_profile_user: %ProfileUser{profile_id: company_profile_id, user: %User{email: email}}
            } = _context
        } = resolution,
        _config
      ) do
    cloud_ip = cloud_ip |> Tuple.to_list() |> Enum.join(".")
    graphql_name = resolution |> Map.get(:definition) |> Map.get(:name)

    user_agent
    |> String.contains?(CloudIps.allowed_user_agent_keywords())
    |> case do
      true ->
        resolution

      false ->
        lookup_cloud_ip(%{
          cloud_ip: cloud_ip,
          graphql_name: graphql_name,
          resolution: resolution,
          user_agent: user_agent,
          request_path: request_path,
          company_profile_id: company_profile_id,
          email: email
        })
    end
  rescue
    e ->
      Sentry.capture_exception(e)
      resolution
  end

  def call(
        %Absinthe.Resolution{
          context:
            %{
              cloud_ip: cloud_ip,
              user_agent: user_agent,
              request_path: request_path,
              current_company_user: %User{id: company_profile_id, email: email}
            } = _context
        } = resolution,
        _config
      ) do
    cloud_ip = cloud_ip |> Tuple.to_list() |> Enum.join(".")
    graphql_name = resolution |> Map.get(:definition) |> Map.get(:name)

    lookup_cloud_ip(%{
      cloud_ip: cloud_ip,
      graphql_name: graphql_name,
      resolution: resolution,
      user_agent: user_agent,
      request_path: request_path,
      company_profile_id: company_profile_id,
      email: email
    })
  rescue
    e ->
      Sentry.capture_exception(e)
      resolution
  end

  def call(resolution, _config), do: resolution

  defp deliver_slack_notification(
         %{
           cloud_ip: cloud_ip,
           graphql_name: graphql_name,
           blocked_reason: blocked_reason,
           request_path: request_path,
           company_profile_id: company_profile_id,
           email: email
         },
         resolution
       ) do
    Slack.Message.send(
      %{
        attachments: [
          %Slack.Message{
            title: "Cloud IP Fetched - Athena",
            title_link: "https://www.ipqualityscore.com/free-ip-lookup-proxy-vpn-test/lookup/#{cloud_ip}",
            text:
              "IP Address: #{cloud_ip}\nEmail: #{email}\nRequest Path: #{request_path}\nGraphql: #{graphql_name}\nCompany Profile ID: #{company_profile_id}\nReason: #{blocked_reason}"
          }
        ]
      },
      :cloud_ip_fetched_notifier_webhook_url
    )

    Absinthe.Resolution.put_result(
      resolution,
      {:error, gettext("Access denied!")}
    )
  end

  defp lookup_cloud_ip(%{
         cloud_ip: cloud_ip,
         graphql_name: graphql_name,
         resolution: resolution,
         user_agent: user_agent,
         request_path: request_path,
         company_profile_id: company_profile_id,
         email: email
       }) do
    iptrie = :persistent_term.get({CloudIpFetcher, :cloud_trie})

    try do
      lookup_cloud_ip_from_iptrie = Iptrie.lookup(iptrie, cloud_ip)

      lookup_cloud_ip_from_ban_list =
        CloudIps.get_cloud_ip_by(%{
          ip_address: cloud_ip,
          graphql_name: graphql_name,
          request_path: request_path,
          company_profile_id: company_profile_id,
          source: :athena
        })

      lookup_cloud_ip_from_ban_list
      |> Map.get(:user_agent, "")
      |> String.contains?(CloudIps.allowed_user_agent_keywords())
      |> case do
        true ->
          resolution

        false ->
          ban_cloud_ip(
            %{
              lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie,
              lookup_cloud_ip_from_ban_list: lookup_cloud_ip_from_ban_list,
              cloud_ip: cloud_ip,
              graphql_name: graphql_name,
              user_agent: user_agent,
              request_path: request_path,
              company_profile_id: company_profile_id,
              email: email
            },
            resolution
          )
      end
    rescue
      e ->
        Logger.info("Iptrie lookup error: #{inspect(e)}", %{
          "cloud_ip" => cloud_ip,
          "user_agent" => user_agent
        })

        # because lookup_cloud_ip_from_iptrie is nil, calling ban_cloud_ip/2 just
        # returns the resolution. TODO: Do we want to ban the invalid IP?
        resolution
    end
  end

  defp ban_cloud_ip(%{lookup_cloud_ip_from_iptrie: nil, lookup_cloud_ip_from_ban_list: _}, resolution) do
    resolution
  end

  defp ban_cloud_ip(
         %{
           lookup_cloud_ip_from_iptrie: _lookup_cloud_ip_from_iptrie,
           lookup_cloud_ip_from_ban_list: %CloudIp{is_allowed: true}
         },
         resolution
       ) do
    resolution
  end

  defp ban_cloud_ip(
         %{
           lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie,
           lookup_cloud_ip_from_ban_list: %CloudIp{allowed_by: allowd_by}
         } = params,
         resolution
       )
       when not is_nil(allowd_by) do
    utc_now = NaiveDateTime.utc_now(:second)

    utc_now
    |> Timex.compare(allowd_by)
    |> case do
      1 ->
        deliver_slack_notification(
          %{
            cloud_ip: Map.get(params, :cloud_ip),
            graphql_name: Map.get(params, :graphql_name),
            blocked_reason: elem(lookup_cloud_ip_from_iptrie, 1),
            request_path: Map.get(params, :request_path),
            company_profile_id: Map.get(params, :company_profile_id),
            email: Map.get(params, :email)
          },
          resolution
        )

      _ ->
        resolution
    end
  end

  defp ban_cloud_ip(
         %{lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie, lookup_cloud_ip_from_ban_list: _} = params,
         resolution
       ) do
    CloudIps.create_cloud_ip(%{
      ip_address: Map.get(params, :cloud_ip),
      is_allowed: false,
      source: :athena,
      user_agent: Map.get(params, :user_agent),
      category: elem(lookup_cloud_ip_from_iptrie, 1),
      request_path: Map.get(params, :request_path),
      graphql_name: Map.get(params, :graphql_name),
      company_profile_id: Map.get(params, :company_profile_id),
      email: Map.get(params, :email)
    })

    deliver_slack_notification(
      %{
        cloud_ip: Map.get(params, :cloud_ip),
        graphql_name: Map.get(params, :graphql_name),
        blocked_reason: elem(lookup_cloud_ip_from_iptrie, 1),
        request_path: Map.get(params, :request_path),
        company_profile_id: Map.get(params, :company_profile_id),
        email: Map.get(params, :email)
      },
      resolution
    )
  end
end
