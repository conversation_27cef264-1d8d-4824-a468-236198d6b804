defmodule AthenaWeb.Middleware.Tracker do
  @moduledoc false

  @behaviour Absinthe.Middleware

  def call(
        %Absinthe.Resolution{
          arguments: arguments,
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{id: company_user_id}},
            simulating_admin_user_id: simulating_admin_user_id
          },
          definition: %Absinthe.Blueprint.Document.Field{name: name},
          errors: []
        } = resolution,
        config
      ) do
    try do
      %{
        admin_user_id: simulating_admin_user_id,
        company_user_id: company_user_id,
        mutation: name
      }
      |> maybe_put_args(config, arguments)
      |> Gaia.Audits.create_mutation()
    rescue
      e ->
        Sentry.capture_exception(e)
    end

    resolution
  end

  def call(
        %Absinthe.Resolution{
          arguments: arguments,
          context: %{
            current_company_user: %Gaia.Companies.User{id: company_user_id},
            simulating_admin_user_id: simulating_admin_user_id
          },
          definition: %Absinthe.Blueprint.Document.Field{name: name},
          errors: []
        } = resolution,
        config
      ) do
    try do
      %{
        admin_user_id: simulating_admin_user_id,
        company_user_id: company_user_id,
        mutation: name
      }
      |> maybe_put_args(config, arguments)
      |> Gaia.Audits.create_mutation()
    rescue
      e ->
        Sentry.capture_exception(e)
    end

    resolution
  end

  def call(
        %Absinthe.Resolution{arguments: arguments, definition: %Absinthe.Blueprint.Document.Field{name: name}, errors: []} =
          resolution,
        config
      ) do
    try do
      %{
        mutation: name
      }
      |> maybe_put_args(config, arguments)
      |> Gaia.Audits.create_mutation()
    rescue
      e ->
        Sentry.capture_exception(e)
    end

    resolution
  end

  def call(resolution, _config), do: resolution

  defp maybe_put_args(attrs, %{hide_args: true}, _args), do: attrs

  defp maybe_put_args(attrs, _config, args) do
    case Jason.encode(args) do
      {:ok, encoded} ->
        Map.put(attrs, :args, encoded)

      _ ->
        attrs
    end
  end
end
