defmodule <PERSON>Web.Schema.Types do
  @moduledoc false
  use <PERSON><PERSON><PERSON>, :type

  ########################################################
  # DateShort Types                                      #
  ########################################################
  scalar :date_short do
    description("""
    The `DateShort` scalar type represents a date.
    The format is equivalent to `D MMM YY` on dayjs.
    The format is equivalent to `{D} {Mshort} {YY}` on Timex.
    """)

    serialize(&to_date_short/1)
    parse(&parse_date_short/1)
  end

  def to_date_short(%Date{} = date) do
    Timex.format!(date, "{D} {Mshort} {YY}")
  end

  def parse_date_short(%Absinthe.Blueprint.Input.String{value: value}) do
    case Timex.parse(value, "{D} {Mshor<PERSON>} {YY}") do
      {:ok, naive_datetime} -> {:ok, Timex.to_date(naive_datetime)}
      _error -> :error
    end
  end

  def parse_date_short(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  def parse_date_short(_) do
    :error
  end

  ########################################################
  # ISO Naive DateTime Types                             #
  ########################################################
  scalar :iso_naive_datetime, description: "ISO Naive DateTime" do
    parse(&decode_naive/1)
    serialize(&encode_naive/1)
  end

  defp encode_naive(%NaiveDateTime{} = val), do: Timex.format!(val, "{ISO:Extended:Z}")

  defp decode_naive(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp decode_naive(%Absinthe.Blueprint.Input.String{value: value}) do
    Timex.parse(value, "{ISO:Extended:Z}")
  end

  ########################################################
  # ISO DateTime Types                                   #
  ########################################################
  scalar :iso_datetime, description: "ISO DateTime with timezone" do
    parse(&decode_datetime/1)
    serialize(&encode_datetime/1)
  end

  defp encode_datetime(%DateTime{} = val), do: DateTime.to_iso8601(val)

  defp decode_datetime(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp decode_datetime(%Absinthe.Blueprint.Input.String{value: value}) do
    case DateTime.from_iso8601(value) do
      {:ok, datetime, _offset} -> {:ok, datetime}
      error -> error
    end
  end

  ########################################################
  # Map Types                                            #
  ########################################################
  scalar :map, name: "Map" do
    description("""
    The `Map` scalar type represents an Elixir Map as JSON
    """)

    serialize(&to_map/1)
    parse(&parse_map/1)
  end

  defp to_map(%{} = value), do: value
  defp to_map(_), do: :error

  @spec parse_map(Absinthe.Blueprint.Input.String.t()) :: {:ok, term()} | :error
  @spec parse_map(Absinthe.Blueprint.Input.Null.t()) :: {:ok, nil}
  defp parse_map(%Absinthe.Blueprint.Input.String{value: value}) do
    case Jason.decode(value) do
      {:ok, result} -> {:ok, result}
      _ -> :error
    end
  end

  defp parse_map(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp parse_map(_) do
    :error
  end

  ########################################################
  # Options Types                                        #
  ########################################################
  input_object :options_input do
    field(:filters, list_of(:filter_input))
    field(:orders, list_of(:order_input))
  end

  object :options do
    field(:filters, list_of(:filter))
    field(:orders, list_of(:order))
  end

  ########################################################
  # Filter Types                                         #
  ########################################################
  input_object :filter_input do
    field(:key, :string)
    field(:value, :string)
  end

  object :filter do
    field(:key, :string)
    field(:value, :string)
  end

  ########################################################
  # Order Types                                          #
  ########################################################
  input_object :order_input do
    field(:key, :string)
    field(:value, :string)
  end

  object :order do
    field(:key, :string)
    field(:value, :string)
  end

  enum :sort_order do
    value(:asc)
    value(:desc)
  end
end
