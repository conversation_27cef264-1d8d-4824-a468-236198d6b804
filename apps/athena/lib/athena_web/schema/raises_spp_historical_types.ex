defmodule AthenaWeb.Schema.SppHistoricalTypes do
  @moduledoc """
  SppOutcomes GraphQL Schema
  """

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers.Raises

  object :raise_spp_historical do
    field(:ticker, non_null(:string))
    field(:media_announcement_id, :integer)
    field(:company_profile_id, :integer)
    field(:ann_type, :string)
    field(:amount_raised, :float)
    field(:shares_alloc, :float)
    field(:discount, :float)
    field(:offer_price, :float)
    field(:shortfall, :boolean)
    field(:oversub, :boolean)
    field(:placement_comp, :boolean)
    field(:placement_amount, :float)
    field(:date_ann, :naive_datetime)
    field(:date_rec, :naive_datetime)
    field(:date_open, :naive_datetime)
    field(:date_close, :naive_datetime)
    field(:date_allot, :naive_datetime)
    field(:min_sub, :float)
    field(:max_sub, :float)
    field(:market_cap, non_null(:float))
    field(:sector_ticker, non_null(:string))

    field(:belongs_to_current_company, non_null(:boolean)) do
      resolve(&Raises.SppHistorical.belongs_to_current_company/3)
    end
  end

  object :raise_spp_regress_params do
    field(:coefficients, non_null(:float))
    field(:intercept, non_null(:float))
  end

  object :raise_spp_shareholder_stats do
    field(:total_shareholders, non_null(:integer))
    field(:average_hold_size, non_null(:float))
    field(:average_hold_length, non_null(:float))
    field(:total_hnws, non_null(:integer))
    field(:total_qual, non_null(:integer))
    field(:total_active, non_null(:integer))
    field(:total_profit, non_null(:integer))
  end

  object :raise_spp_reachability_stats do
    field(:total_hub_users, non_null(:integer))
    field(:engaged_hub_users, non_null(:integer))
    field(:total_emails, non_null(:integer))
    field(:open_rate, non_null(:float))
    field(:click_rate, non_null(:float))
    field(:total_phones, non_null(:integer))
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :raises_spp_historical_queries do
    @desc "Get historical SPP"
    field :raises_spp_historical, non_null(list_of(non_null(:raise_spp_historical))) do
      arg(:sector_ticker, non_null(list_of(:string)))
      arg(:year, non_null(:integer))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Raises.SppHistorical.resolve/3)
      middleware(Middleware.Analytics, %{event: "spp_historical_fetched", hide_args: true})
    end
  end

  object :raises_spp_historical_for_current_company_queries do
    @desc "Get historical SPP for current company"
    field :raises_spp_historical_for_current_company, non_null(list_of(non_null(:raise_spp_historical))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Raises.SppHistorical.resolve_current_company/3)
      middleware(Middleware.Analytics, %{event: "spp_historical_for_current_company_fetched", hide_args: true})
    end
  end

  object :regress_raises_spp_historical_queries do
    @desc "Get best fit line for historical SPP"
    field :regress_raises_spp_historical, :raise_spp_regress_params do
      arg(:sector_ticker, non_null(list_of(:string)))
      arg(:year, non_null(:integer))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Raises.SppHistorical.regress/3)
      middleware(Middleware.Analytics, %{event: "regressed_spp_historical_fetched", hide_args: true})
    end
  end

  object :raises_spp_shareholder_stats_queries do
    @desc "Get summary stats about shareholders for SPPs"
    field :raises_spp_shareholder_stats, :raise_spp_shareholder_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Raises.SppHistorical.get_raise_spp_shareholder_stats/3)
      middleware(Middleware.Analytics, %{event: "spp_shareholder_stats_fetched", hide_args: true})
    end
  end

  object :raises_spp_reachability_stats_queries do
    @desc "Get summary stats about reachability for SPPs"
    field :raises_spp_reachability_stats, :raise_spp_reachability_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Raises.SppHistorical.get_raise_spp_reachability_stats/3)
      middleware(Middleware.Analytics, %{event: "spp_reachability_stats_fetched", hide_args: true})
    end
  end
end
