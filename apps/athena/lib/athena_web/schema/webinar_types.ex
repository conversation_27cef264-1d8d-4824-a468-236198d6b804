defmodule AthenaWeb.Schema.WebinarsTypes do
  @moduledoc false
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  object :webinar_queries do
    @desc "Get a single webinar by ID"
    field :webinar, :webinar do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.GetWebinar.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_fetched"})
    end

    @desc "Check if a webinar has synced with 100ms.live"
    field :check_webinar_has_synced_with_hms, :boolean do
      arg(:id, non_null(:id))
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.CheckWebinarHasSyncedWithHMS.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_has_synced_with_hms"})
    end

    @desc "Get a paginated list of webinars for the current company"
    connection field(:webinars, node_type: :webinar) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.ListWebinars.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinars_listed"})
    end

    @desc "Get a paginated list of attendees for a webinar"
    connection field(:webinar_attendees, node_type: :attendee) do
      arg(:webinar_id, non_null(:id))
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.ListWebinarAttendees.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_attendees_listed"})
    end

    @desc "Get a paginated list of investor users for a webinar"
    connection field(:webinar_investor_users, node_type: :webinar_investor_user) do
      arg(:webinar_id, non_null(:id))
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.ListWebinarInvestorUsers.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_investor_users_listed"})
    end

    @desc "Get the post webinar audience movement stats"
    field :post_webinar_audience_movement, :post_webinar_audience_movement do
      arg(:webinar_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.GetPostWebinarAudienceMovement.resolve/3)
      middleware(Middleware.Analytics, %{event: "post_webinar_audience_movement_stats_fetched"})
    end
  end

  object :webinar_mutations do
    @desc "Create a new webinar for the current company"
    field :create_webinar, :webinar do
      arg(:title, non_null(:string))
      arg(:type, non_null(:string))
      arg(:summary, :map)
      arg(:start_time, :datetime)
      arg(:end_time, :datetime)
      arg(:timezone, :string)
      arg(:allow_pre_webinar_comments, :boolean)
      arg(:recording_needs_login, :boolean)
      arg(:attendance_needs_login, :boolean)
      arg(:discoverable_on_hub, :boolean)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.CreateWebinar.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_created"})
    end

    @desc "Update an existing webinar"
    field :update_webinar, :webinar do
      arg(:id, non_null(:id))
      arg(:type, :string)
      arg(:title, :string)
      arg(:summary, :map)
      arg(:start_time, :datetime)
      arg(:end_time, :datetime)
      arg(:timezone, :string)
      arg(:image_cloudinary_id, :string)

      arg(:allow_pre_webinar_comments, :boolean)
      arg(:published_recording_url, :string)
      arg(:poster_image_url, :string)
      arg(:state, :string)
      arg(:discoverable_on_hub, :boolean)
      arg(:transcript, :map)
      arg(:transcript_summary, :map)
      arg(:show_transcript_on_hub, :boolean)
      arg(:show_transcript_summary_on_hub, :boolean)
      arg(:recording_needs_login, :boolean)
      arg(:attendance_needs_login, :boolean)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.UpdateWebinar.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_updated"})
    end

    @desc "Start a webinar recording"
    field :start_webinar_recording, :webinar do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.StartWebinarRecording.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_recording_started"})
    end

    @desc "Stop a webinar recording"
    field :stop_webinar_recording, :webinar do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.StopWebinarRecording.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_recording_stopped"})
    end

    @desc "Delete a webinar"
    field :delete_webinar, :webinar do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.DeleteWebinar.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_deleted"})
    end

    @desc "Sync a webinar with 100ms.live"
    field :sync_webinar, :webinar do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.SyncWebinar.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_synced"})
    end

    @desc "Create a new document for a webinar"
    field :create_webinar_document, :webinar_document do
      arg(:webinar_id, non_null(:id))
      arg(:cloudinary_id, non_null(:string))
      arg(:file_name, non_null(:string))
      arg(:file_type, non_null(:string))
      arg(:file_size, non_null(:integer))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.CreateWebinarDocument.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_document_created"})
    end

    @desc "Update an existing webinar document"
    field :update_webinar_document, :webinar_document do
      arg(:id, non_null(:id))
      arg(:cloudinary_id, :string)
      arg(:file_name, :string)
      arg(:file_type, :string)
      arg(:file_size, :integer)
      arg(:position, :integer)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.UpdateWebinarDocument.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_document_updated"})
    end

    @desc "Delete a webinar document"
    field :delete_webinar_document, :webinar_document do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.DeleteWebinarDocument.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_document_deleted"})
    end

    @desc "Sort documents for a given webinar"
    field :sort_webinar_documents, :boolean do
      arg(:webinar_id, non_null(:id))
      arg(:document_ids, list_of(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "webinars.admin"})
      resolve(&Resolvers.Webinars.SortWebinarDocuments.resolve/3)
      middleware(Middleware.Analytics, %{event: "webinar_documents_sorted"})
    end
  end

  connection node_type: :webinar do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Webinars.ListWebinars.resolve_total/3)
    end

    edge do
      field(:webinar, non_null(:webinar))
    end
  end

  connection node_type: :webinar_investor_user do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:webinar_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Webinars.ListWebinarInvestorUsers.resolve_total/3)
    end

    edge do
      field(:node, non_null(:webinar_attendee))
    end
  end

  connection node_type: :attendee do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:webinar_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Webinars.ListWebinarAttendees.resolve_total/3)
    end

    edge do
      field(:node, non_null(:webinar_attendee))
    end
  end

  object :webinar do
    field(:id, non_null(:id))
    field(:type, :string)
    field(:company_webinar_id, non_null(:integer))
    field(:hms_room_id, :string)
    field(:title, :string)
    field(:summary, :map)
    field(:start_time, :naive_datetime)
    field(:end_time, :naive_datetime)
    field(:started_broadcasting_at, :naive_datetime)
    field(:stopped_broadcasting_at, :naive_datetime)
    field(:timezone, :string)
    field(:hms_broadcaster_room_code, :string)
    field(:hms_viewer_room_code, :string)
    field(:hms_speaker_room_code, :string)
    field(:state, :string)
    field(:allow_pre_webinar_comments, :boolean)
    field(:hms_recording_state, :string)
    field(:image_cloudinary_id, :string)

    field(:recording_url, :string)
    field(:published_recording_url, :string)
    field(:poster_image_url, :string)
    field(:recording_needs_login, :boolean)
    field(:attendance_needs_login, :boolean)
    field(:discoverable_on_hub, :boolean)
    field(:transcript, :map)
    field(:transcript_summary, :map)
    field(:show_transcript_on_hub, :boolean)
    field(:show_transcript_summary_on_hub, :boolean)
    field(:organiser_company_profile_user, :company_profile_user, resolve: dataloader(Gaia.Repo))
    field(:media_update, :media_update, resolve: dataloader(Gaia.Repo))
    field(:documents, list_of(:webinar_document), resolve: dataloader(Gaia.Repo))
    field(:attendees, list_of(:webinar_attendee), resolve: dataloader(Gaia.Repo))
    field(:questions, list_of(:webinar_question), resolve: dataloader(Gaia.Repo))
    field(:recording_views, list_of(:webinar_recording_view), resolve: dataloader(Gaia.Repo))

    field(:slug, non_null(:string)) do
      resolve(&Resolvers.Webinars.Fields.Webinar.slug/3)
    end

    field(:has_published_webinar_smart_block, non_null(:boolean)) do
      resolve(&Resolvers.Webinars.Fields.Webinar.has_published_webinar_smart_block/3)
    end
  end

  object :webinar_document do
    field(:id, non_null(:id))
    field(:cloudinary_id, :string)
    field(:file_name, :string)
    field(:file_type, :string)
    field(:file_size, :integer)
    field(:position, :integer)
    field(:downloads, list_of(:webinar_document_download), resolve: dataloader(Gaia.Repo))
  end

  object :webinar_attendee do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:naive_datetime))
    field(:is_new_investor_user, :boolean)
    field(:attended, :boolean)
    field(:duration_watched, :integer)
    field(:watch_sessions, list_of(:map))
    field(:viewed_recording_at, :naive_datetime)
    field(:hms_name_entered, :string)
    field(:investor_user, :investor_user, resolve: dataloader(Gaia.Repo))
    field(:downloads, list_of(:webinar_document_download), resolve: dataloader(Gaia.Repo))
    field(:webinar, :webinar, resolve: dataloader(Gaia.Repo))
    field(:recording_views, list_of(:webinar_recording_view), resolve: dataloader(Gaia.Repo))

    field :shareholding_at_watch_time, :shareholding_snapshot do
      resolve(&Resolvers.Webinars.Fields.WebinarAttendee.shareholding_at_watch_time/3)
    end

    field :shareholding_up_to_one_week_after_watch_time, :shareholding_snapshot do
      resolve(&Resolvers.Webinars.Fields.WebinarAttendee.shareholding_up_to_one_week_after_watch_time/3)
    end
  end

  object :webinar_question do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:naive_datetime))
    field(:updated_at, non_null(:naive_datetime))
    field(:content, non_null(:string))
    field(:attendee, non_null(:webinar_attendee), resolve: dataloader(Gaia.Repo))
  end

  object :webinar_document_download do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:naive_datetime))
    field(:attendee, :webinar_attendee, resolve: dataloader(Gaia.Repo))
    field(:document, :webinar_document, resolve: dataloader(Gaia.Repo))
  end

  object :shareholding_snapshot do
    field(:share_count, :integer)
    field(:date, :date)
  end

  object :webinar_recording_view do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:naive_datetime))
    field(:attendee_id, :id)
    field(:attendee, :webinar_attendee, resolve: dataloader(Gaia.Repo))
    field(:webinar, :webinar, resolve: dataloader(Gaia.Repo))
    field(:watch_time_seconds, :integer)
    field(:is_mobile, :boolean)
    field(:user_agent, :string)
    field(:visitor_cookie_id, :string)
  end

  object :post_webinar_audience_movement do
    field(:total_buyers, :integer)
    field(:total_sellers, :integer)
    field(:total_volume_traded, :integer)
    field(:net_changes, :integer)
  end

  object :webinar_subscriptions do
    field :webinar_updated, :webinar do
      arg(:id, non_null(:id))

      config(fn
        %{id: webinar_id},
        %{
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{id: current_company_profile_id}
            }
          }
        } ->
          with %Gaia.Webinars.Webinar{company_profile_id: company_profile_id} <-
                 Gaia.Webinars.get_webinar(webinar_id),
               true <- company_profile_id == current_company_profile_id do
            {:ok, topic: "webinar_updated_#{webinar_id}"}
          else
            _ ->
              {:error, "You are unauthorised."}
          end

        _, _ ->
          {:error, "You are unauthorised."}
      end)

      trigger(:update_webinar,
        topic: fn webinar ->
          "webinar_updated_#{webinar.id}"
        end
      )

      trigger(:start_webinar_recording,
        topic: fn webinar ->
          "webinar_updated_#{webinar.id}"
        end
      )

      trigger(:stop_webinar_recording,
        topic: fn webinar ->
          "webinar_updated_#{webinar.id}"
        end
      )
    end
  end
end
