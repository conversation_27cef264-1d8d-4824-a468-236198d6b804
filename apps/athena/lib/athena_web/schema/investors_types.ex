defmodule AthenaWeb.Schema.InvestorsTypes do
  @moduledoc """
  Investors GraphQL Schema
  """
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic
  use Gettext, backend: AthenaWeb.Gettext

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  connection node_type: :investor_user do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Investors.Total.total/3)
    end

    edge do
      field(:investor_user, non_null(:investor_user))
    end
  end

  object :activity_stats do
    field(:id, non_null(:id))

    field(
      :announcements_viewed,
      non_null(:integer),
      do: resolve(&Resolvers.Investors.InvestorUserFields.announcements_viewed/3)
    )

    field(
      :updates_viewed,
      non_null(:integer),
      do: resolve(&Resolvers.Investors.InvestorUserFields.updates_viewed/3)
    )

    field(:questions_asked, non_null(:integer))
    field(:likes, non_null(:integer))
    field(:survey_responses, non_null(:integer))
    field(:followers, non_null(:integer))
  end

  object :investor_user do
    field(:id, non_null(:id))
    field(:confirmed_at, :iso_naive_datetime)
    field(:email, non_null(:string))
    field(:first_name, :string)
    field(:hnw_identified_at, :iso_naive_datetime)
    field(:hnw_status, :investor_hnw_statuses)

    field(:last_name, :string)
    field(:provider, :string)
    field(:username, :string)
    field(:inserted_at, non_null(:iso_naive_datetime))

    field(:certificate, :investor_certificate, do: resolve(dataloader(Gaia.Repo)))
    field(:contact, :contact, do: resolve(dataloader(Gaia.Repo)))

    field(:is_self_nominated_shareholder, :boolean)
    field(:self_nominated_shareholder_identified_at, :iso_naive_datetime)

    field(
      :is_holding_verified,
      non_null(:boolean),
      do: resolve(&Resolvers.Investors.InvestorUserFields.is_holding_verified/3)
    )

    # Deprecated 09/12/2024 - use `shareholder_informations` instead
    field(:shareholder_information, :investor_shareholder_information, do: resolve(dataloader(Gaia.Repo)))

    field(:shareholder_informations, list_of(non_null(:investor_shareholder_information)),
      do: resolve(dataloader(Gaia.Repo))
    )

    # Deprecated 09/12/2024 - use `shareholder_informations_uk` instead
    field(:shareholder_information_uk, :investor_shareholder_information_uk, do: resolve(dataloader(Gaia.Repo)))

    field(:shareholder_informations_uk, list_of(non_null(:investor_shareholder_information_uk)),
      do: resolve(dataloader(Gaia.Repo))
    )

    field(:shareholdings, non_null(list_of(non_null(:shareholding))), do: resolve(dataloader(Gaia.Repo)))

    field(
      :notification_preferences,
      non_null(list_of(non_null(:investor_notification_preference))),
      do: resolve(dataloader(Gaia.Repo))
    )

    field(:source_type, :string)
    field(:source_id, :integer)
    field(:hub_engagement_score, :float)
  end

  object :investor_shareholder_information do
    field(:id, non_null(:id))

    field(:partial_hin, non_null(:string))
    field(:country, non_null(:string))
    field(:postcode, :string)
  end

  object :investor_shareholder_information_uk do
    field(:id, non_null(:id))

    field(:broker, non_null(:string))
    field(:account_name, non_null(:string))
    field(:postcode, :string)
    field(:shares_owned, :integer)
  end

  enum(:investor_certificate_status, values: Gaia.Investors.Certificate.get_status_values())
  enum(:investor_certificate_type, values: Gaia.Investors.Certificate.get_type_values())
  enum(:investor_hnw_statuses, values: Gaia.Investors.User.hnw_statuses())

  object :investor_certificate do
    field(:id, non_null(:id))

    field :certificate_url, non_null(:string) do
      resolve(&Resolvers.Investors.CertificateFields.certificate_url/3)
    end

    field(:investor_user, non_null(:investor_user), do: resolve(dataloader(Gaia.Repo)))

    field(:expires_at, :iso_naive_datetime)

    field(:is_expired, non_null(:boolean), do: resolve(&Resolvers.Investors.InvestorCertificateFields.is_expired/3))

    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:status, non_null(:investor_certificate_status))
    field(:type, :investor_certificate_type)
  end

  object :certificate_by_id do
    field(:certificate, non_null(:investor_certificate))
    field(:total_pending, non_null(:integer))
    field(:next_pending_id, :id)
  end

  enum(:investor_notification_channel,
    values: Gaia.Comms.InvestorUsers.NotificationPreference.get_channel_values()
  )

  enum(:investor_notification_scope,
    values: Gaia.Comms.InvestorUsers.NotificationPreference.get_scope_values()
  )

  object :investor_notification_preference do
    field(:id, non_null(:id))
    field(:channel, :investor_notification_channel)
    field(:has_eoi, :boolean)
    field(:is_on, :boolean)
    field(:scope, :investor_notification_scope)
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  object :investors_mutations do
    @desc "Mark investor certificate (s708 or AFSL) as verified"
    field :mark_investor_certificate_as_verified, non_null(:investor_certificate) do
      arg(:certificate_id, non_null(:id))
      arg(:expires_at, :iso_naive_datetime)
      arg(:type, non_null(:investor_certificate_type))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return:
          {:error,
           dpgettext(
             "error",
             "Mark investor certificate as verified mutation resolver",
             "You do not have the required permission to verify an investor certificate."
           )}
      })

      resolve(&Resolvers.Investors.MarkInvestorCertificateAsVerified.resolve/3)
      middleware(Middleware.Analytics, %{event: "investor_certificate_marked_as_verified"})
      middleware(Middleware.Tracker)
    end

    @desc "Mark investor certificate (s708 or AFSL) as rejected"
    field :mark_investor_certificate_as_rejected, non_null(:investor_certificate) do
      arg(:certificate_id, non_null(:id))
      arg(:rejection_comment, :string)

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return:
          {:error,
           dpgettext(
             "error",
             "Reject Certificate Mutation",
             "You do not have the required permission to reject an investor certificate."
           )}
      })

      resolve(&AthenaWeb.Resolvers.Investors.MarkInvestorCertificateAsRejected.resolve/3)
      middleware(Middleware.Analytics, %{event: "investor_certificate_marked_as_rejected"})
      middleware(Middleware.Tracker)
    end
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################
  object :investors_queries do
    @desc "Get all current company investor users for CSV download"
    field :all_current_company_investor_users, non_null(list_of(non_null(:investor_user))) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Investors.AllCurrentCompanyInvestorUsers.resolve/3)
      middleware(Middleware.Analytics, %{event: "all_current_company_investor_users_fetched"})
    end

    @desc "Get all pending certificates"
    field :pending_review_certificates, non_null(list_of(non_null(:investor_certificate))) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Investors.PendingReviewCertificates.resolve/3)
      middleware(Middleware.Analytics, %{event: "pending_review_certificates_fetched"})
    end

    @desc "Gets certificate by ID"
    field(:certificate_by_id, non_null(:certificate_by_id)) do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Investors.CertificateById.resolve/3)
      middleware(Middleware.Analytics, %{event: "certificate_by_id_fetched"})
    end

    field :investor_activity_stats, :activity_stats do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Investors.InvestorActivityStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "investor_activity_stats_fetched"})
    end

    @desc "Get paginated investor users"
    connection field(:investor_users, node_type: :investor_user) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Investors.Users.cursor/3)
      middleware(Middleware.Analytics, %{event: "investor_users_fetched"})
    end
  end
end
