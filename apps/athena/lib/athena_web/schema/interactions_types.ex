defmodule AthenaWeb.Schema.InteractionsTypes do
  @moduledoc false

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers.Interactions

  connection node_type: :media do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.ListMedias.resolve_total/3)
    end

    edge do
      field(:media, non_null(:media))
    end
  end

  connection node_type: :media_announcement do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalMediaAnnouncements.resolve/3)
    end

    edge do
      field(:media_announcement, non_null(:media_announcement))
    end
  end

  connection node_type: :announcement_list do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalAnnouncementsList.resolve/3)
    end

    edge do
      field(:announcement_list, non_null(:announcement_list))
    end
  end

  connection node_type: :media_update do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalMediaUpdates.resolve/3)
    end

    field :pinned_media_update_exists, non_null(:boolean) do
      resolve(&Interactions.PinnedMediaUpdateExists.resolve/3)
    end

    edge do
      field(:media_update, non_null(:media_update))
    end
  end

  connection node_type: :media_comment do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalMediaQuestions.resolve/3)
    end

    field :total_active, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalActiveMediaQuestions.resolve/3)
    end

    field :total_starred, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalStarredMediaQuestions.resolve/3)
    end

    field :total_done, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalDoneMediaQuestions.resolve/3)
    end

    field :total_all, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalAllMediaQuestions.resolve/3)
    end

    edge do
      field(:media_comment, non_null(:media_comment))
    end
  end

  connection node_type: :media_interacted_investor do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      arg(:media_id, non_null(:id))
      arg(:media_type, non_null(:string))
      arg(:slug, :string)
      resolve(&Interactions.MediaInteractedInvestors.total/3)
    end

    edge do
      field(:investor_user, non_null(:investor_user))
      field(:commented, non_null(:boolean))
      field(:liked, non_null(:boolean))
      field(:surveyed, non_null(:boolean))
      field(:last_interacted_at, non_null(:iso_naive_datetime))
      field(:surveys, list_of(non_null(:string)))
      field(:source, :string)
    end
  end

  enum(:media_update_attachment_type, values: Gaia.Interactions.MediaUpdateAttachment.get_types())

  enum(:media_update_type, values: Gaia.Interactions.MediaUpdate.get_types())

  enum(:included_media_update_type, values: Gaia.Interactions.MediaUpdate.get_included_types())

  enum(:media_survey_answer_type, values: Gaia.Interactions.MediaSurveyAnswer.get_answers())

  enum(:media_survey_question_type, values: Gaia.Interactions.MediaSurveyAnswer.get_questions())

  enum(:email_distribution_method,
    values: Gaia.Interactions.Media.get_distribution_method_types()
  )

  enum(:comment_source, values: Gaia.Interactions.MediaComment.get_comment_sources())

  input_object :upsert_media_update_attachment_input do
    field(:title, :string)
    field(:description, :string)
    field(:type, non_null(:media_update_attachment_type))
    field(:url, non_null(:string))
    field(:thumbnail_url, :string)
    field(:order_id, non_null(:integer))
  end

  input_object :upsert_media_update_input do
    field(:attachments, list_of(:upsert_media_update_attachment_input))
    field(:comment_content, :string)
    field(:comment_use_company_as_username, :boolean)
    field(:content, :string)
    field(:title, non_null(:string))
  end

  input_object :media_comment_annotation_metadata_input do
    field(:left, non_null(:float))
    field(:page_index, non_null(:integer))
    field(:top, non_null(:float))
  end

  input_object :update_media_announcement_input do
    field(:featured_on_hub, :boolean)
    field(:social_video_url, :string)
    field(:header, :string)
    field(:summary, :string)
    field(:summary_ai, :string)
    field(:video, :upload)
    field(:video_url, :string)
    field(:german_translated_url, :string)
    field(:german_translated_header, :string)
    field(:german_translated_video_url, :string)
    field(:german_translated_summary, :string)
  end

  # -------- START OF NEW NEWSFLOW FEATURE INPUT OBJECTS --------

  input_object :create_media_input do
    field(:title, :string)
    field(:draft_content, :map)
    field(:target_date, :iso_datetime)
    field(:distribution_announcement_enabled, :boolean)
    field(:distribution_update_enabled, :boolean)
    field(:distribution_linkedin_enabled, :boolean)
    field(:distribution_twitter_enabled, :boolean)
    field(:distribution_email_enabled, :boolean)
    field(:primary_distribution, :string)
    field(:pdf, :upload)
    field(:image, :upload)
    field(:audio, :upload)
    field(:file_type, :string)
  end

  input_object :update_media_input do
    field(:title, :string)
    field(:draft_content, :map)
    field(:target_date, :iso_datetime)
    field(:distribution_announcement_enabled, :boolean)
    field(:distribution_update_enabled, :boolean)
    field(:distribution_linkedin_enabled, :boolean)
    field(:distribution_twitter_enabled, :boolean)
    field(:distribution_email_enabled, :boolean)
    field(:primary_distribution, :string)
  end

  input_object :create_media_update_input do
    field(:ai_generate_body, :boolean)
    field(:media_id, non_null(:id))
    field(:preview_secret, :string)
  end

  input_object :update_media_update_input do
    field(:content_draft, :map)
    field(:title, :string)
    field(:preview_secret, :string)
    field(:thumbnail_url, :string)
  end

  input_object :media_file_input do
    field(:cloudinary_id, non_null(:string))
    field(:file_type, non_null(:string))
  end

  input_object :social_post_input do
    field(:content, :map)
    field(:content_formatted, :string)
    field(:platform, non_null(:social_post_platform))
    field(:status, non_null(:social_post_status))
    field(:attachments, list_of(:map))
  end

  # -------- END OF NEW NEWSFLOW FEATURE INPUT OBJECTS --------

  object :media_tag do
    field(:id, non_null(:id))

    field(:invalidated, non_null(:boolean))
    field(:name, non_null(:string))
  end

  input_object :media_tag_input do
    field(:invalidated, :boolean)
    field(:name, :string)
    field(:id, :id)
  end

  object :media_interacted_investor do
    field(:investor_user, non_null(:investor_user))
    field(:commented, non_null(:boolean))
    field(:liked, non_null(:boolean))
    field(:surveyed, non_null(:boolean))
    field(:last_interacted_at, non_null(:iso_naive_datetime))
    field(:surveys, list_of(non_null(:string)))
    field(:source, :string)
  end

  object :interactive_media_stats do
    field(:total_active_questions, non_null(:integer))
    field(:total_likes_last_week, non_null(:integer))
    field(:total_likes_this_week, non_null(:integer))
    field(:total_likes, non_null(:integer))
    field(:total_questions_last_week, non_null(:integer))
    field(:total_questions_this_week, non_null(:integer))
    field(:total_questions, non_null(:integer))
    field(:total_survey_responses_last_week, non_null(:integer))
    field(:total_survey_responses_this_week, non_null(:integer))
    field(:total_survey_responses, non_null(:integer))
    field(:total_announcements_last_week, non_null(:integer))
    field(:total_announcements_this_week, non_null(:integer))
    field(:total_announcements, non_null(:integer))
    field(:total_announcements_last_month, non_null(:integer))
    field(:total_announcements_this_month, non_null(:integer))
    field(:total_announcements_this_year, non_null(:integer))
    field(:total_updates_last_week, non_null(:integer))
    field(:total_updates_this_week, non_null(:integer))
    field(:total_updates_last_month, non_null(:integer))
    field(:total_updates_this_month, non_null(:integer))
    field(:total_updates_this_year, non_null(:integer))
    field(:total_updates, non_null(:integer))
  end

  object :media do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:title, :string)
    field(:draft_content, :map)
    field(:target_date, :iso_datetime)
    field(:distribution_announcement_enabled, :boolean)
    field(:distribution_update_enabled, :boolean)
    field(:distribution_linkedin_enabled, :boolean)
    field(:distribution_twitter_enabled, :boolean)
    field(:distribution_email_enabled, :boolean)
    field(:primary_distribution, :string)

    # PDF AI processing fields - status is still useful but based on ai_media_conversions now
    field(:pdf_ai_processing_status, :string) do
      resolve(&Gaia.Interactions.Media.media_ai_processing_status/3)
    end

    # Total impressions across all distribution channels
    field(:total_impressions, non_null(:integer)) do
      resolve(&Interactions.MediaFields.total_impressions/3)
    end

    # Cached stats fields
    field(:cached_stats_total_impressions, :integer)
    field(:cached_stats, :map)
    field(:cached_stats_last_updated, :iso_datetime)

    field(:company_profile, :company_profile, do: resolve(dataloader(Gaia.Repo)))
    field(:email_distribution_method, :email_distribution_method)
    field(:media_announcement, :media_announcement, do: resolve(dataloader(Gaia.Repo)))
    field(:prepared_announcement, :prepared_announcement, do: resolve(dataloader(Gaia.Repo)))
    field(:media_update, :media_update, do: resolve(dataloader(Gaia.Repo)))
    field(:linkedin_social_post, :social_post, do: resolve(dataloader(Gaia.Repo)))
    field(:twitter_social_post, :social_post, do: resolve(dataloader(Gaia.Repo)))
    field(:email, :email, do: resolve(dataloader(Gaia.Repo)))
    field(:tags, non_null(list_of(non_null(:media_tag))), do: resolve(dataloader(Gaia.Repo)))
    field(:created_by_profile_user, :company_profile_user, do: resolve(dataloader(Gaia.Repo)))

    field(:comments, non_null(list_of(non_null(:media_comment))), do: resolve(dataloader(Gaia.Repo)))

    field(:likes, non_null(list_of(non_null(:media_like))), do: resolve(dataloader(Gaia.Repo)))

    # Add ai_media_conversions field
    field(:ai_media_conversions, non_null(list_of(non_null(:ai_media_conversion))), do: resolve(dataloader(Gaia.Repo)))

    # To be deprecated:
    field(:distributed_social, :distributed_social, do: resolve(dataloader(Gaia.Repo)))
  end

  enum(:social_post_status, values: Gaia.Interactions.SocialPost.get_status_types())
  enum(:social_post_platform, values: Gaia.Interactions.SocialPost.get_platform_types())

  object :social_post do
    field(:id, non_null(:id))
    field(:content, :map)
    field(:content_formatted, :string)
    field(:social_post_id, :string)
    field(:status, :social_post_status)
    field(:platform, :social_post_platform)
    field(:scheduled_at, :iso_naive_datetime)
    field(:published_at, :iso_naive_datetime)
    field(:error_message, :string)
    field(:analytics_data, :map)
    field(:is_invalidated, :boolean)
    field(:attachments, list_of(:map))
    field(:published_url, :string)
  end

  object :media_announcement do
    field(:id, non_null(:id))

    field(:email, :email, do: resolve(dataloader(Gaia.Repo)))
    field(:distributed_social, :distributed_social, do: resolve(dataloader(Gaia.Repo)))
    field(:header, non_null(:string))
    field(:listing_key, non_null(:string))
    field(:market_key, non_null(:string))
    field(:market_sensitive, :boolean)

    field(:featured_on_hub, non_null(:boolean))

    field(:media_id, non_null(:id))
    field(:media, non_null(:media), do: resolve(dataloader(Gaia.Repo)))

    field(:prepared_announcement, :prepared_announcement, do: resolve(dataloader(Gaia.Repo)))

    field(:posted_at, non_null(:iso_naive_datetime))
    field(:rectype, non_null(:string))
    field(:social_video_url, :string)
    field(:subtypes, non_null(list_of(:string)))
    field(:summary, :string)
    field(:summary_ai, :string)
    field(:thumbnail_is_portrait, :boolean)
    field(:url, non_null(:string))
    field(:video_url, :string)
    field(:german_translated_url, :string)
    field(:german_translated_header, :string)
    field(:german_translated_video_url, :string)
    field(:german_translated_summary, :string)
    field(:inserted_at, non_null(:iso_naive_datetime))

    field(:likes, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.likes/3)
    end

    field(:total_active_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_active_comment_count/3)
    end

    field(:total_company_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_company_comment_count/3)
    end

    field(:total_active_question_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_active_question_count/3)
    end

    field(:total_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_comment_count/3)
    end

    field(:total_question_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_question_count/3)
    end

    field(:total_survey_responses, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_survey_responses/3)
    end

    field(:total_signups, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_signups_count/3)
    end

    field(:thumbnail_url, :string) do
      resolve(&Interactions.MediaAnnouncementFields.thumbnail_url/3)
    end

    field(:total_view_count, non_null(:integer)) do
      resolve(&Interactions.MediaAnnouncementFields.total_view_count/3)
    end

    field(:total_view_count_from_time_period, non_null(:integer)) do
      arg(:start_date, non_null(:naive_datetime))
      arg(:end_date, non_null(:naive_datetime))
      resolve(&Interactions.MediaAnnouncementFields.total_view_count_from_time_period/3)
    end

    field(:total_unique_visitors, non_null(:integer)) do
      resolve(&Interactions.MediaAnnouncementFields.total_unique_visitors_count/3)
    end

    field(:total_signups_last_week, non_null(:integer)) do
      resolve(&Interactions.MediaAnnouncementFields.total_signups_last_week_count/3)
    end
  end

  object :media_comment do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:annotation_metadata, :media_comment_annotation_metadata)
    field(:children, list_of(non_null(:media_comment)))
    field(:comment_source, :comment_source)
    field(:comment_source_url, :string)
    field(:content, non_null(:string))
    field(:done, non_null(:boolean))
    field(:likes, non_null(:integer))
    field(:parent_id, :id)
    field(:private, non_null(:boolean))
    field(:use_company_as_username, non_null(:boolean))

    field(:last_edited_by_user, :company_user, do: resolve(dataloader(Gaia.Repo)))
    field(:company_author, :company_user, do: resolve(dataloader(Gaia.Repo)))
    field(:investor_user, :investor_user, do: resolve(dataloader(Gaia.Repo)))
    field(:media, :media, do: resolve(dataloader(Gaia.Repo)))
    field(:comment_star, :media, do: resolve(dataloader(Gaia.Repo)))

    field(:user_starred, :media_comment_star, do: resolve(&Interactions.MediaCommentFields.user_starred/3))

    field(:user_read, :media_comment_read, do: resolve(&Interactions.MediaCommentFields.user_read/3))
  end

  object :media_like do
    field(:id, non_null(:id))
    field(:investor_user, :investor_user, do: resolve(dataloader(Gaia.Repo)))
    field(:like, non_null(:boolean))
  end

  object :media_comment_annotation_metadata do
    field(:left, non_null(:float))
    field(:page_index, non_null(:integer))
    field(:top, non_null(:float))
  end

  object :media_comment_read do
    field(:id, non_null(:id))

    field(:read, non_null(:boolean))
  end

  object :media_comment_star do
    field(:id, non_null(:id))

    field(:starred, non_null(:boolean))
  end

  object :media_survey_answer do
    field(:id, non_null(:id))

    field(:answer, non_null(:media_survey_answer_type))
    field(:question, non_null(:media_survey_question_type))
  end

  object :media_update do
    field(:id, non_null(:id))
    field(:content_draft, :map)
    field(:content_published, :map)
    field(:preview_secret, :string)

    field(:thumbnail_attachment, :media_update_attachment) do
      resolve(&Interactions.MediaUpdateFields.thumbnail_attachment/3)
    end

    field(:is_draft, non_null(:boolean))
    field(:media_id, non_null(:id))
    field(:posted_at, :iso_naive_datetime)
    field(:slug, non_null(:string))

    field(:newsflow_slug, non_null(:string)) do
      resolve(&Interactions.MediaUpdateFields.slug/3)
    end

    field(:title, non_null(:string))
    field(:included_types, list_of(non_null(:media_update_type)))
    field(:is_pinned, :boolean)
    field(:inserted_at, non_null(:iso_naive_datetime))

    field(:attachments, non_null(list_of(non_null(:media_update_attachment))), do: resolve(dataloader(Gaia.Repo)))

    field(:content, :media_update_content, do: resolve(dataloader(Gaia.Repo)))
    field(:email, :email, do: resolve(dataloader(Gaia.Repo)))
    field(:distributed_social, :distributed_social, do: resolve(dataloader(Gaia.Repo)))
    field(:media, non_null(:media), do: resolve(dataloader(Gaia.Repo)))
    field(:posted_by, :company_user, do: resolve(dataloader(Gaia.Repo)))
    field(:last_updated_by, non_null(:company_user), do: resolve(dataloader(Gaia.Repo)))
    field(:thumbnail_url, :string)

    field(:likes, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.likes/3)
    end

    field(:total_parent_company_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_parent_company_comment_count/3)
    end

    field(:total_active_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_active_comment_count/3)
    end

    field(:total_active_question_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_active_question_count/3)
    end

    field(:total_comment_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_comment_count/3)
    end

    field(:total_question_count, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_question_count/3)
    end

    field(:total_survey_responses, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_survey_responses/3)
    end

    field(:total_signups, non_null(:integer)) do
      resolve(&Interactions.MediaSharedFields.total_signups_count/3)
    end

    field(:total_unique_visitors, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.total_unique_visitors_count/3)
    end

    field(:total_view_count, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.total_view_count/3)
    end
  end

  object :media_update_attachment do
    field(:id, non_null(:id))

    field(:thumbnail_url, :string)
    field(:title, :string)
    field(:description, :string)
    field(:type, non_null(:media_update_attachment_type))
    field(:url, non_null(:string))
    field(:order_id, non_null(:integer))

    field(:thumbnail, :string) do
      resolve(&Interactions.MediaUpdateAttachmentFields.thumbnail/3)
    end
  end

  object :media_update_content do
    field(:id, non_null(:id))

    field(:content, non_null(:string))
    field(:comment_content, :string)
    field(:comment_use_company_as_username, :boolean)
  end

  object :media_viewer_stats do
    field(:campaign_views, :integer)
    field(:prepared_link_views, :integer)
    field(:twitter_views, :integer)
    field(:linkedin_views, :integer)
    field(:other, :integer)
  end

  input_object :prepared_announcement_input do
    field(:title, :string)
    field(:video_url, :string)
    field(:social_video_url, :string)
    field(:summary, :string)
    field(:summary_tiptap, :map)
    field(:comment_content, :string)
    field(:comment_content_tiptap, :map)
    field(:comment_use_company_as_username, :boolean)
    field(:is_draft, :boolean)
    field(:media_id, :id)
  end

  object :prepared_announcement do
    field(:id, non_null(:id))
    field(:title, :string)
    field(:video_url, :string)
    field(:social_video_url, :string)
    field(:summary, :string)
    field(:summary_tiptap, :map)
    field(:comment_content, :string)
    field(:comment_content_tiptap, :map)
    field(:comment_use_company_as_username, :boolean)
    field(:is_draft, non_null(:boolean))
    field(:media_id, :id)
    field(:german_translated_url, :string)
    field(:german_translated_header, :string)
    field(:german_translated_video_url, :string)
    field(:german_translated_summary, :string)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:hash_id, non_null(:string)) do
      resolve(&Interactions.PreparedAnnouncementFields.hash_id/3)
    end

    field(:media_announcement, :media_announcement, do: resolve(dataloader(Gaia.Repo)))
  end

  object :announcement_list do
    field(:media_announcement, :media_announcement)
    field(:prepared_announcement, :prepared_announcement)
  end

  object :ai_media_conversion do
    field(:id, non_null(:id))
    field(:file_name, :string)
    field(:size, :integer)
    field(:content, :map)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:page_count, :integer)
  end

  object :ai_generated_content do
    field(:update, :map)
    field(:announcement, :string)
    field(:x, :string)
    field(:linkedin, :string)
  end

  object :interactions_mutations do
    field :create_media_comment, :media_comment do
      arg(:annotation_metadata, :media_comment_annotation_metadata_input)
      arg(:content, non_null(:string))
      arg(:media_id, non_null(:id))
      arg(:use_company_as_username, :boolean)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.CreateMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_created"})
      middleware(Middleware.Tracker)
    end

    @desc "Create a media comment from another source, with option to add a reply"
    field :create_media_comment_from_other_source, :media_comment do
      arg(:comment_source, non_null(:comment_source))
      arg(:comment_source_url, :string)
      arg(:media_id, non_null(:id))
      arg(:reply_content, :string)
      arg(:reply_use_company_as_username, :boolean)
      arg(:content, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.CreateMediaCommentFromOtherSource.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_from_other_source_created"})
      middleware(Middleware.Tracker)
    end

    field :create_new_media_update, :media_update do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.CreateNewMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "new_media_update_created"})
      middleware(Middleware.Tracker)
    end

    field :generate_media_announcement_video_signed_url, non_null(:string) do
      arg(:file_size, non_null(:integer))
      arg(:media_announcement_id, non_null(:id))
      arg(:mime_type, non_null(:string))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.GenerateMediaAnnouncementVideoSignedUrl.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_video_signed_url_generated"})
      middleware(Middleware.Tracker)
    end

    field :generate_media_announcement_ai_summary, non_null(:string) do
      arg(:media_announcement_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.GenerateMediaAnnouncementAiSummary.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_ai_summary_generated"})
      middleware(Middleware.Tracker)
    end

    field :generate_prepared_announcement_video_signed_url, non_null(:string) do
      arg(:file_size, non_null(:integer))
      arg(:prepared_announcement_id, non_null(:id))
      arg(:mime_type, non_null(:string))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.GeneratePreparedAnnouncementVideoSignedUrl.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "prepared_announcement_video_signed_url_generated"
      })

      middleware(Middleware.Tracker)
    end

    field :generate_media_update_attachment_signed_url, non_null(:string) do
      arg(:mime_type, non_null(:string))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.GenerateMediaUpdateAttachmentSignedUrl.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_attachment_signed_url_generated"})
    end

    field :invalidate_media, :media do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.InvalidateMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_invalidated"})
      middleware(Middleware.Tracker)
    end

    field :invalidate_media_comment, :media_comment do
      arg(:media_comment_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.InvalidateMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_invalidated"})
      middleware(Middleware.Tracker)
    end

    field :publish_media_update, :media_update do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.PublishMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_published"})
      middleware(Middleware.Tracker)
    end

    field :unpublish_media_update, :media_update do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UnpublishMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_unpublished"})
      middleware(Middleware.Tracker)
    end

    field :reply_to_media_comment, :media_comment do
      arg(:content, non_null(:string))
      arg(:parent_id, non_null(:id))
      arg(:use_company_as_username, :boolean)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.ReplyToMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_replied"})
      middleware(Middleware.Tracker)
    end

    field :reset_media_announcement, :media_announcement do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.ResetMediaAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_reset"})
      middleware(Middleware.Tracker)
    end

    field :toggle_media_comment_privacy, :boolean do
      arg(:id, non_null(:id))
      arg(:private, non_null(:boolean))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.ToggleMediaCommentPrivacy.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_privacy_toggled"})
      middleware(Middleware.Tracker)
    end

    field :sort_content_calendar, non_null(:boolean) do
      arg(:source_year, :integer)
      arg(:source_month, :integer)
      arg(:source_media_ids, list_of(:id))
      arg(:target_year, :integer)
      arg(:target_month, :integer)
      arg(:target_media_ids, list_of(:id))

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.SortContentCalendar.resolve/3)
      middleware(Middleware.Analytics, %{event: "content_calendar_sorted"})
      middleware(Middleware.Tracker)
    end

    field :update_media_announcement, :media_announcement do
      arg(:id, non_null(:id))
      arg(:input, non_null(:update_media_announcement_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpdateMediaAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_updated", hide_args: true})
      middleware(Middleware.Tracker)
    end

    field :update_media_update_title, :media_update do
      arg(:id, non_null(:id))
      arg(:title, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpdateMediaUpdateTitle.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_title_updated"})
      middleware(Middleware.Tracker)
    end

    field :update_media_update_posted_at, :media_update do
      arg(:id, non_null(:id))
      arg(:posted_at, non_null(:iso_naive_datetime))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpdateMediaUpdatePostedAt.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_posted_at_updated"})
      middleware(Middleware.Tracker)
    end

    field :upsert_media_update, :media_update do
      arg(:id, non_null(:id))
      arg(:media_update, non_null(:upsert_media_update_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpsertMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_upserted"})
      middleware(Middleware.Tracker)
    end

    field :update_pinned_media_update, :media_update do
      arg(:id, non_null(:id))
      arg(:value, non_null(:boolean))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpdatePinnedMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "pinned_media_update_updated"})
      middleware(Middleware.Tracker)
    end

    field :upsert_media_comment_read, :media_comment_read do
      arg(:media_comment_id, non_null(:id))
      arg(:read, non_null(:boolean))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.UpsertMediaCommentRead.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_read_upserted"})
      middleware(Middleware.Tracker)
    end

    field :upsert_media_comment_star, :media_comment_star do
      arg(:media_comment_id, non_null(:id))
      arg(:starred, non_null(:boolean))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.UpsertMediaCommentStar.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_star_upserted"})
      middleware(Middleware.Tracker)
    end

    field :update_media_comment_done, :media_comment do
      arg(:media_comment_id, non_null(:id))
      arg(:done, non_null(:boolean))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.UpdateMediaCommentDone.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_done_updated"})
      middleware(Middleware.Tracker)
    end

    field :update_media_comment_reply, :media_comment do
      arg(:media_comment_id, non_null(:id))
      arg(:content, non_null(:string))
      arg(:use_company_as_username, :boolean)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.UpdateMediaCommentReply.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_reply_updated"})
      middleware(Middleware.Tracker)
    end

    @desc "Create prepared announcement"
    field :create_prepared_announcement, :prepared_announcement do
      arg(:title, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.CreatePreparedAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_created"})
      middleware(Middleware.Tracker)
    end

    @desc "Update prepared announcement"
    field :update_prepared_announcement, :prepared_announcement do
      arg(:id, non_null(:id))
      arg(:prepared_announcement, :prepared_announcement_input)

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.UpdatePreparedAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_updated"})
      middleware(Middleware.Tracker)
    end

    @desc "Delete prepared announcement"
    field :delete_prepared_announcement, :boolean do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.DeletePreparedAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_deleted"})
      middleware(Middleware.Tracker)
    end

    @desc "Manually link prepared announcement to media announcement"
    field :link_prepared_announcement_to_announcement, :media_announcement do
      # announcement_media_id so can get by id and company_profile for security
      arg(:announcement_media_id, non_null(:id))
      arg(:prepared_announcement_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.LinkPreparedAnnouncementToAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "link_prepared_announcement_to_announcement"})
      middleware(Middleware.Tracker)
    end

    @desc "Create a tag on an announcement or update"
    field :create_media_tag, non_null(:media_tag) do
      arg(:media_id, non_null(:id))
      arg(:tag, non_null(:media_tag_input))
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      middleware(Middleware.BlockCloudIP)
      resolve(&Interactions.CreateMediaTag.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "media_tag_created"})
    end

    @desc "Update tag on an announcement or update"
    field :update_media_tag, non_null(:media_tag) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      arg(:id, non_null(:id))
      arg(:tag, non_null(:media_tag_input))
      resolve(&Interactions.UpdateTag.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "media_tag_updated"})
    end

    # -------- START OF NEW NEWSFLOW FEATURE ENDPOINTS --------

    field :create_media, :media do
      arg(:media, non_null(:create_media_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.CreateMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_created"})
      middleware(Middleware.Tracker)
    end

    field :update_media, :media do
      arg(:id, non_null(:id))
      arg(:media, non_null(:update_media_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpdateMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_updated", hide_args: true})
      middleware(Middleware.Tracker)
    end

    field :delete_media, :media do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.DeleteMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_deleted"})
      middleware(Middleware.Tracker)
    end

    field :duplicate_media, :media do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.DuplicateMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_duplicated"})
      middleware(Middleware.Tracker)
    end

    field :create_media_update, :media_update do
      arg(:media_update, non_null(:create_media_update_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.CreateMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_created"})
      middleware(Middleware.Tracker)
    end

    field :update_media_update, :media_update do
      arg(:media_id, non_null(:id))
      arg(:media_update, non_null(:update_media_update_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.EditMediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_updated"})
      middleware(Middleware.Tracker)
    end

    @desc "Parse a PDF file with AI and generate content based on requested distributions"
    field :parse_pdf_with_ai, :ai_generated_content do
      arg(:pdf, non_null(:upload))
      arg(:distributions, non_null(list_of(non_null(:string))))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.ParsePdfWithAi.resolve/3)
      middleware(Middleware.Analytics, %{event: "pdf_parsed_with_ai"})
      middleware(Middleware.Tracker)
    end

    @desc "Upload media file (PDF, image, audio) to be analysed by AI"
    field :upload_media_files, list_of(:ai_media_conversion) do
      arg(:media_id, non_null(:id))
      arg(:files, non_null(list_of(non_null(:media_file_input))))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UploadMediaFiles.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_files_uploaded"})
      middleware(Middleware.Tracker)
    end

    field :publish_media_update_content, :media_update do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.PublishMediaUpdateContent.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_content_published"})
      middleware(Middleware.Tracker)
    end

    field(:upsert_media_social_post, :social_post) do
      arg(:media_id, non_null(:id))
      arg(:social_post_input, non_null(:social_post_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.UpsertMediaSocialPost.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_social_post_upserted"})
      middleware(Middleware.Tracker)
    end

    field(:publish_media_social_post, :social_post) do
      arg(:media_id, non_null(:id))
      arg(:platform, non_null(:social_post_platform))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.PublishMediaSocialPost.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_social_post_published"})
      middleware(Middleware.Tracker)
    end

    field :publish_media, :media do
      arg(:id, non_null(:id))
      arg(:distributions, list_of(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.PublishMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_published"})
      middleware(Middleware.Tracker)
    end

    @desc "Create prepared announcement for media"
    field :create_prepared_announcement_for_media, :prepared_announcement do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.CreatePreparedAnnouncementForMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_created"})
      middleware(Middleware.Tracker)
    end

    # -------- END OF NEW NEWSFLOW FEATURE ENDPOINTS --------
  end

  object :linkedin_media_statistics do
    field(:id, non_null(:id))
    field(:impression_count, non_null(:integer))
    field(:like_count, non_null(:integer))
    field(:comment_count, non_null(:integer))
    field(:share_count, non_null(:integer))
    field(:engagement, non_null(:float))
    field(:clicked_count, non_null(:integer))
  end

  object :interactions_queries do
    @desc "Get a paginated list of medias"
    connection field(:medias, node_type: :media) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.ListMedias.resolve/3)
      middleware(Middleware.Analytics, %{event: "medias_listed"})
    end

    connection field(:media_announcements, node_type: :media_announcement) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaAnnouncements.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcements_fetched"})
    end

    @desc "Get paginated media_announcements and prepared_announcements"
    connection field(:announcements_list, node_type: :announcement_list) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.AnnouncementsList.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "announcements_list_fetched"
      })
    end

    connection field(:media_updates, node_type: :media_update) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaUpdates.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_updates_fetched"})
    end

    @desc "Get paginated media questions(comments from investor users)"
    connection field(:media_questions_list, node_type: :media_comment) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaQuestionsList.cursor/3)
      middleware(Middleware.Analytics, %{event: "media_questions_list_fetched"})
    end

    @desc "Get paginated investor users who have interacted with an announcement / update"
    connection field(:media_interacted_investors, node_type: :media_interacted_investor) do
      arg(:options, :options_input)
      arg(:media_id, non_null(:id))
      arg(:media_type, non_null(:string))
      arg(:slug, :string)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaInteractedInvestors.cursor/3)
      middleware(Middleware.Analytics, %{event: "media_interacted_investors_fetched"})
    end

    @desc "Get list of non draft not linked prepared announcements with search"
    field :non_draft_not_linked_prepared_announcements,
          non_null(list_of(non_null(:prepared_announcement))) do
      arg(:search, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.NonDraftNotLinkedPreparedAnnouncements.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "non_draft_not_linked_prepared_announcements_fetched"
      })
    end

    field :interactive_media_stats, non_null(:interactive_media_stats) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.InteractiveMediaStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "interactive_media_stats_fetched"})
    end

    field :media_announcement, :media_announcement do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_fetched"})
    end

    field :media, :media do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.Media.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_fetched"})
    end

    field :prepared_announcement, :prepared_announcement do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.admin"})
      resolve(&Interactions.PreparedAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_fetched"})
    end

    field(:media_comments, non_null(list_of(non_null(:media_comment)))) do
      arg(:is_annotation, :boolean)
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaComments.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comments_fetched"})
    end

    field(:media_comments_company_author, non_null(list_of(non_null(:media_comment)))) do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaCommentsCompanyAuthor.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comments_company_author_fetched"})
    end

    field :media_survey_answers, non_null(list_of(non_null(:media_survey_answer))) do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaSurveyAnswers.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_survey_answers_fetched"})
    end

    field :media_update, :media_update do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaUpdate.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_fetched"})
    end

    field :oldest_media_announcement, :media_announcement do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.OldestMediaAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "oldest_media_announcement_fetched"})
    end

    field :oldest_media_announcement_date, :iso_naive_datetime do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.OldestMediaAnnouncementDate.resolve/3)
      middleware(Middleware.Analytics, %{event: "oldest_media_announcement_date_fetched"})
    end

    field :media_announcement_viewer_stats, :media_viewer_stats do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaAnnouncementViewerStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcements_viewer_stats_fetched"})
    end

    @desc "Viewer stats for announcements + updates"
    field :media_viewer_stats, :media_viewer_stats do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.MediaViewerStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcements_viewer_stats_fetched"})
    end

    @desc "All existing media tags on current company by media type"
    field :existing_media_tags, non_null(list_of(non_null(:media_tag))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "interactions_media_announcements.editor"})
      resolve(&Interactions.ExistingTags.resolve/3)
      middleware(Middleware.Analytics, %{event: "existing_tags_fetched"})
    end

    @desc "AI drafting answer from comments"
    field :ai_answer_comments, non_null(:string) do
      arg(:media_comment_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Interactions.AIAnswerComments.resolve/3)
      middleware(Middleware.Analytics, %{event: "ai_answer_comments"})
    end

    @desc "Get stats for a linkedin post"
    field :linkedin_media_statistics, :linkedin_media_statistics do
      arg(:post_id, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&Interactions.LinkedinMediaStatistics.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "linkedin_media_statistics_query"})
    end
  end
end
