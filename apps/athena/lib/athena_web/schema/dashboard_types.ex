defmodule AthenaWeb.Schema.DashboardTypes do
  @moduledoc """
  Dashboard GraphQL Schema

  For graphs, charts or insights
  """

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  object :company_stats do
    field(:id, non_null(:id))

    field(:as_of_date, :date_short)

    field(:average_holding_time, non_null(:float), description: "Number in days") do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.average_holding_time/3)
    end

    field(:company_profile_id, non_null(:id))

    field(:geographical_breakdown_chart, non_null(:geographical_breakdown_chart))

    field(:growth_ratio_past_ninety_days, non_null(:float),
      description: "Growth ratio for the past 90 days until today and not as_of_date"
    )

    field :profit_loss, non_null(:profit_loss) do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.profit_loss/3)
    end

    field :raising_potential, non_null(:raising_potential) do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.raising_potential/3)
    end

    field :shareholder_insights, non_null(:shareholder_insights) do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.shareholder_insights/3)
    end
  end

  object :geographical_breakdown_chart do
    field(:data, non_null(list_of(:geographical_breakdown_chart_data)))
  end

  object :geographical_breakdown_chart_data do
    field(:id, non_null(:id))

    field(:eligible, non_null(:boolean))
    field(:label, non_null(:string))
    field(:value, non_null(:integer))
  end

  object :profit_loss do
    field(:average_shareholder_profit_loss, non_null(:float))
    field(:shareholders_in_profit, non_null(:float))
  end

  object :raising_potential do
    field(:average_uptake, non_null(:float))
    field(:high, non_null(:float))

    field :low, non_null(:float) do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.low/3)
    end

    field(:mean, non_null(:float))
    field(:shareholder_participation, non_null(:float))

    field(:scenario_range, non_null(:scenario_range))
  end

  object :scenario_range do
    field(:all_bad, non_null(:scenario_range_data))
    field(:all_good, non_null(:scenario_range_data))
    field(:rocky_finish, non_null(:scenario_range_data))
  end

  object :scenario_range_data do
    field(:average_uptake, non_null(:float))
    field(:high, non_null(:float))

    field :low, non_null(:float) do
      resolve(&Resolvers.Dashboard.CompanyStatsFields.low/3)
    end

    field(:mean, non_null(:float))
    field(:shareholder_participation, non_null(:float))
  end

  object :shareholder_insights do
    field(:addresses, non_null(:integer))
    field(:eligibility, non_null(:float))
    field(:email, non_null(:integer))
    field(:mobile, non_null(:integer))
    field(:reachability, non_null(:float))
    field(:total, non_null(:integer))
  end

  ##############################################################################
  # Connections                                                                #
  ##############################################################################
  connection node_type: :shareholder_trading_activity do
    field(:total, non_null(:integer))
    field(:order, :order)

    edge do
    end
  end

  connection node_type: :trading_activity do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Dashboard.TradingActivity.total/3)
    end

    edge do
    end
  end

  ##############################################################################
  # Enums                                                                      #
  ##############################################################################
  enum :trading_activity_type do
    value(:new, as: "new")
    value(:returning, as: "returning")
    value(:churned, as: "churned")
    value(:upgraded, as: "upgraded")
    value(:downgraded, as: "downgraded")
  end

  ##############################################################################
  # Input Objects                                                              #
  ##############################################################################
  input_object :send_start_planning_spp_email_input do
    field(:first_name, non_null(:string))
    field(:phone, :string)
    field(:what_do_you_want_to_learn_about, non_null(:string))
    field(:additional_comments, :string)
  end

  input_object :shareholder_trading_activity_input do
    field(:trading_activity_type, :trading_activity_type)
    field(:start_date, non_null(:date))
    field(:end_date, non_null(:date))
  end

  ##############################################################################
  # Objects                                                                    #
  ##############################################################################
  object :broker_breakdown do
    field(:id, non_null(:id))

    field(:name, non_null(:string))
    field(:name_short, non_null(:string))
    field(:net_movements, non_null(:integer))
    field(:pids, non_null(list_of(non_null(:string))))
    field(:shareholders_count, non_null(:integer))
    field(:total_shares, non_null(:integer))
  end

  object :current_holding_distributions do
    field(:id, non_null(:id))

    field(:date, non_null(:date))
    field(:holding_distributions, non_null(list_of(non_null(:holding_distribution))))
  end

  object :current_holding_lengths do
    field(:id, non_null(:id))

    field(:date, non_null(:date))
    field(:holding_lengths, non_null(list_of(non_null(:holding_length))))
  end

  object :current_holding_sizes do
    field(:id, non_null(:id))

    field(:date, non_null(:date))
    field(:holding_sizes, non_null(list_of(non_null(:holding_size))))
  end

  object :current_shareholder_contactabilities do
    field(:id, non_null(:id))

    field(:date, non_null(:date))
    field(:emails_count, non_null(:integer))
    field(:phones_count, non_null(:integer))
    field(:total_shareholders, non_null(:integer))
  end

  object :current_shareholder_locations do
    field(:id, non_null(:id))

    field(:date, non_null(:date))
    field(:shareholder_locations, non_null(list_of(non_null(:shareholder_location))))
  end

  object :current_shareholder_profits do
    field(:id, non_null(:id))

    field(:average_shareholders_profit, non_null(:float))
    field(:date, non_null(:date))
    field(:shareholder_profits, non_null(list_of(non_null(:shareholder_profit))))
    field(:shareholders_in_loss, non_null(:integer))
    field(:shareholders_in_profit, non_null(:integer))
    field(:total_unrealised_gain, non_null(:float))
    field(:total_unrealised_loss, non_null(:float))
  end

  object :holding_distribution do
    field(:id, non_null(:id))

    field(:currency, :string)
    field(:lower_bound, :integer, description: "$ value, inclusive")
    field(:upper_bound, :integer, description: "$ value, exclusive")
    field(:value, non_null(:integer), description: "# of shareholders")
  end

  object :holding_insights do
    field(:id, non_null(:id))

    field(:average_holding_size, non_null(:float))
    field(:average_holding_value, non_null(:float))
    field(:date, non_null(:date))
    field(:holding_timeseries, non_null(list_of(non_null(:holding_timeseries))))
  end

  object :holding_length do
    field(:id, non_null(:id))

    field(:label, non_null(:string))
    field(:shareholders_count, non_null(:integer))
    field(:total_shares, non_null(:integer))
  end

  object :holding_size do
    field(:id, non_null(:id))

    field(:label, non_null(:string))
    field(:percentage, non_null(:float))
    field(:value, non_null(:integer))
  end

  object :holding_timeseries do
    field(:id, non_null(:id))

    field(:average_holding_time, non_null(:float), description: "Number in days")
    field(:date, non_null(:date))
    field(:shareholdings_count, non_null(:integer))
  end

  object :growth_ratio_timeseries do
    field :id, non_null(:id) do
      resolve(&Resolvers.Dashboard.GrowthRatioTimeseriesFields.id/3)
    end

    field(:date, :date_short)
    field(:new, :integer)
    field(:returning, :integer)
    field(:churned, :integer)

    field :growth_ratio, :float do
      resolve(&Resolvers.Dashboard.GrowthRatioTimeseriesFields.growth_ratio/3)
    end
  end

  object :shareholder_location do
    field(:id, non_null(:id))

    field(:location, non_null(:string))
    field(:shareholders_count, non_null(:integer))
    field(:total_shares, non_null(:integer))
  end

  object :shareholder_profit do
    field(:id, non_null(:id))

    field(:lower_bound, :float)
    field(:shareholders_count, non_null(:integer))
    field(:upper_bound, :float)
  end

  # Deprecated. Use :trading_activity instead
  object :shareholder_trading_activity do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:days_traded, non_null(:integer))
    field(:email, :string)
    field(:holdings, non_null(:integer))
    field(:movement, non_null(:integer))
    field(:shareholding_id, non_null(:id))
  end

  object :trading_activity do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:days_traded, non_null(:integer))
    field(:email, :string)
    field(:holdings, non_null(:integer))
    field(:movement, non_null(:integer))
    field(:shareholding_id, non_null(:id))

    field(:type, :string, description: "'new', 'returning', 'churned', 'upgraded', 'downgraded'")
    field(:movement_percent, non_null(:string))
    field(:holding_time, non_null(:string), description: "String like '1 yr 2 mth'")
    field(:initial_purchase_date, :string)

    field :shareholding, :shareholding do
      resolve(&Resolvers.Dashboard.TradingActivityFields.shareholding/3)
    end
  end

  object :trading_activity_stats do
    field(:new_count, non_null(:integer))
    field(:returning_count, non_null(:integer))
    field(:churned_count, non_null(:integer))
    field(:upgraded_count, non_null(:integer))
    field(:downgraded_count, non_null(:integer))
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :dashboard_queries do
    @desc "Deprecated. Use trading_activity instead"
    connection field(:shareholder_trading_activity, node_type: :shareholder_trading_activity) do
      arg(:shareholder_trading_activity_input, non_null(:shareholder_trading_activity_input))
      arg(:order, :order_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.ShareholderTradingActivity.cursor/3)
      middleware(Middleware.Analytics, %{event: "shareholder_trading_activity_fetched"})
    end

    connection field(:trading_activity, node_type: :trading_activity) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.TradingActivity.cursor/3)
      middleware(Middleware.Analytics, %{event: "trading_activity_fetched"})
    end

    @desc "Get the trading activity stats"
    field :trading_activity_stats, :trading_activity_stats do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.TradingActivity.stats/3)
      middleware(Middleware.Analytics, %{event: "trading_activity_stats_fetched"})
    end

    @desc "Get the broker breakdown"
    field :broker_breakdown, non_null(list_of(non_null(:broker_breakdown))) do
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.BrokerBreakdown.resolve/3)
      middleware(Middleware.Analytics, %{event: "broker_breakdown_fetched"})
    end

    @desc "Get the company stats for dashboard"
    field :current_company_stats, :company_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Dashboard.CurrentCompanyStats.get_current_company_stats/3)
      middleware(Middleware.Analytics, %{event: "current_company_stats_fetched"})
    end

    @desc "Get the current holding distributions"
    field :current_holding_distributions, :current_holding_distributions do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentHoldingDistributions.resolve/3)

      middleware(Middleware.Analytics, %{event: "current_holding_distributions_fetched"})
    end

    @desc "Get the current holding insights"
    field :current_holding_insights, :holding_insights do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentHoldingInsights.resolve/3)
      middleware(Middleware.Analytics, %{event: "current_holding_insights_fetched"})
    end

    @desc "Get the current holding lengths"
    field :current_holding_lengths, :current_holding_lengths do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentHoldingLengths.resolve/3)
      middleware(Middleware.Analytics, %{event: "current_holding_lengths_fetched"})
    end

    @desc "Get the current holding sizes"
    field :current_holding_sizes, :current_holding_sizes do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentHoldingSizes.resolve/3)
      middleware(Middleware.Analytics, %{event: "current_holding_sizes_fetched"})
    end

    @desc "Get the current shareholder contactabilities"
    field :current_shareholder_contactabilities, :current_shareholder_contactabilities do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentShareholderContactabilities.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "current_shareholder_contactabilities_fetched"
      })
    end

    @desc "Get the current shareholder locations"
    field :current_shareholder_locations, :current_shareholder_locations do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentShareholderLocations.resolve/3)

      middleware(Middleware.Analytics, %{event: "current_shareholder_locations_fetched"})
    end

    @desc "Get the current shareholder profits"
    field :current_shareholder_profits, :current_shareholder_profits do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.CurrentShareholderProfits.resolve/3)
      middleware(Middleware.Analytics, %{event: "current_shareholder_profits_fetched"})
    end

    @desc "Get the growth ratio timeseries"
    field :growth_ratio_timeseries, list_of(non_null(:growth_ratio_timeseries)) do
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.GrowthRatioTimeseries.resolve/3)
      middleware(Middleware.Analytics, %{event: "growth_ratio_timeseries_fetched"})
    end

    field :metabase_static_embed_url, :string do
      arg(:custom_report, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(AthenaWeb.Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.MetabaseStaticEmbedUrl.resolve/3)
    end
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  object :dashboard_mutations do
    @desc "Send a start planning your spp email to service team"
    field :send_start_planning_spp_email, :boolean do
      arg(:send_start_planning_spp_email_input, non_null(:send_start_planning_spp_email_input))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.SendStartPlanningSppEmail.send_start_planning_spp_email/3)
      middleware(Middleware.Analytics, %{event: "start_planning_spp_email_sent"})
      middleware(Middleware.Tracker)
    end

    @desc "Request a custom report"
    field :request_custom_report, non_null(:boolean) do
      arg(:message, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Dashboard.RequestCustomReport.resolve/3)
      middleware(Middleware.Analytics, %{event: "custom_report_requested"})
      middleware(Middleware.Tracker)
    end
  end
end
