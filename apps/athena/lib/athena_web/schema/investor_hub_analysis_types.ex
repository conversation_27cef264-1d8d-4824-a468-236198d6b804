defmodule AthenaWeb.Schema.InvestorHubAnalysisTypes do
  @moduledoc false
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias <PERSON><PERSON><PERSON>.Middleware

  object :current_company_investor_hub_analysis_stats do
    field(:company_profile_id, non_null(:id))

    field(:current_month_investor_hub_stats, non_null(:investor_hub_analysis_stats)) do
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.Fields.current_month_investor_hub_stats/3)
    end

    field(:previous_month_investor_hub_stats, non_null(:investor_hub_analysis_stats)) do
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.Fields.previous_month_investor_hub_stats/3)
    end

    field(:overall_investor_hub_stats, non_null(:investor_hub_analysis_stats)) do
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.Fields.overall_investor_hub_stats/3)
    end
  end

  object :investor_hub_analysis_stats do
    field(:title, non_null(:string))
    field(:views, non_null(:integer))
    field(:visitors, non_null(:integer))
    field(:signups, non_null(:integer))
  end

  object :media_stats do
    field(:comments, non_null(:integer))
    field(:reactions, non_null(:integer))
    field(:survey_responses, non_null(:integer))
    field(:views, non_null(:integer))
    field(:visitors, non_null(:integer))
  end

  object :media_survey_response do
    field(:answers, non_null(list_of(:media_survey_response_answers)))
    field(:question, non_null(:media_survey_question_type))
    field(:total_responses, non_null(:integer))
  end

  object :media_survey_response_answers do
    field(:answer, non_null(:media_survey_answer_type))
    field(:response_percentage_as_float, non_null(:float))
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :investor_hub_analysis_queries do
    @desc "Gets the investor hub analysis stats"
    field :current_company_investor_hub_analysis, :current_company_investor_hub_analysis_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.CurrentCompany.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "current_company_investor_hub_analysis_fetched"
      })
    end

    @desc "Gets the survey response stats for a media"
    field :media_survey_response_stats, list_of(:media_survey_response) do
      arg(:media_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.MediaSurveyResponseStats.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "media_survey_response_stats_fetched"})
    end

    @desc "Gets the investor hub overview stats"
    field :media_stats, :media_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.InvestorHubAnalysis.MediaStats.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "media_stats_fetched"})
    end
  end
end
