defmodule AthenaWeb.Schema.ShareholderInsightsTypes do
  @moduledoc """
  ShareholderInsights GraphQL Schema
  """
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  @desc "A HNW Investor is either a contact or shareholding, identified by `hnw_status` or `hnw_behaviour`"
  connection node_type: :hnw_investor do
    field :total, non_null(:integer) do
      arg(:hnw_type, :hnw_query_type)
      arg(:include_past_investors, :boolean)
      arg(:search_phrase, :string)
      resolve(&Resolvers.ShareholderInsights.HNWInvestorFields.total/3)
    end

    field :totals, non_null(:hnw_totals) do
      arg(:include_past_investors, :boolean)
      arg(:search_phrase, :string)
      resolve(&Resolvers.ShareholderInsights.HNWInvestorFields.totals/3)
    end

    edge do
    end
  end

  enum :hnw_investor_type do
    value(:contact, as: "contact")
    value(:shareholding, as: "shareholding")
  end

  enum(:hnw_query_type,
    values: Gaia.Contacts.Contact.hnw_statuses()
  )

  object :hnw_totals do
    field(:id, non_null(:id))

    field(:all, non_null(:integer))
    field(:nominated_without_cert, non_null(:integer))
    field(:nominated_cert_pending, non_null(:integer))
    field(:nominated_cert_verified, non_null(:integer))
    field(:identified_via_behaviour, non_null(:integer))
  end

  object :hnw_investor do
    field(:id, non_null(:id))

    field(:hnw_identified_at, non_null(:iso_naive_datetime))

    field(
      :hnw_status,
      :contact_hnw_statuses,
      do: resolve(&Resolvers.ShareholderInsights.HNWInvestorFields.hnw_status/3)
    )

    field(:hnw_behaviour, :hnw_behaviours)
    field(:email, :string)
    field(:name, :string)
    field(:account_names, list_of(:string))
    field(:share_count, :integer)
    field(:type, non_null(:hnw_investor_type))
  end

  object :hnw_identified_stats do
    field(:id, non_null(:id))

    field(:last_seven_days, :integer)
    field(:seven_to_fourteen_days_ago, :integer)
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :shareholder_insights_queries do
    # For the HNW tab
    @desc "Get high net worth investors"
    connection field(:hnw_investors, node_type: :hnw_investor) do
      arg(:hnw_type, :hnw_query_type)
      arg(:include_past_investors, :boolean)
      arg(:search_phrase, :string)
      arg(:order, :order_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.ShareholderInsights.HNWInvestors.resolve/3)
      middleware(Middleware.Analytics, %{event: "hnw_investors_fetched"})
    end

    @desc "Get how many high net worth investors were identified in the last 14 days"
    field :hnw_identified_numbers, :hnw_identified_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.ShareholderInsights.HNWIdentifiedNumbers.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "current_company_stats_fetched"})
    end
  end
end
