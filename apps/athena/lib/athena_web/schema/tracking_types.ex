defmodule AthenaWeb.Schema.TrackingTypes do
  @moduledoc """
  Tracking GraphQL Schema
  """

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers.Tracking

  connection node_type: :utm_link do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Tracking.TotalUtmLinks.resolve/3)
    end

    edge do
      field(:utm_link, non_null(:utm_link))
    end
  end

  connection node_type: :utm_investor_signup do
    field(:utm_link_id, non_null(:id))
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:utm_link_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Tracking.UtmLinkAnalyticsSignups.total/3)
    end

    edge do
      field(:node, non_null(:contact))
    end
  end

  connection node_type: :existing_hub_page do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Tracking.ExistingHubPages.total/3)
    end

    edge do
    end
  end

  enum(:hs_custom_event, values: Gaia.Hubspot.get_custom_events_keys())

  object :existing_hub_page do
    field(:id, non_null(:id))
    field(:name, non_null(:string))
    field(:type, non_null(:string))
    field(:url, non_null(:string))
    field(:timestamp, :naive_datetime)
  end

  object :utm_link do
    field(:id, non_null(:id))
    field(:hash, non_null(:string))
    field(:destination_url, non_null(:string))
    field(:utm_campaign, non_null(:string))
    field(:utm_medium, non_null(:string))
    field(:utm_source, non_null(:string))
    field(:utm_url, non_null(:string))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:is_user_generated, non_null(:boolean))
    field(:total_hub_sign_ups, non_null(:integer), do: resolve(&Tracking.UtmLinkFields.total_hub_sign_ups_count/3))
  end

  input_object :utm_link_input do
    field(:destination_url, non_null(:string))
    field(:utm_campaign, non_null(:string))
    field(:utm_medium, non_null(:string))
    field(:utm_source, non_null(:string))
    field(:utm_url, non_null(:string))
  end

  object :utm_link_analytics do
    field(:curr_period_analytics, non_null(:utm_link_stats_for_period))
    field(:prev_period_analytics, non_null(:utm_link_stats_for_period))
  end

  object :utm_link_stats_for_period do
    field(:id, non_null(:id))
    field(:total_hits, non_null(:integer))
    field(:total_hits_list, non_null(list_of(:daily_hits)))
    field(:total_unique_hits, non_null(:integer))
    field(:total_unique_hits_list, non_null(list_of(:daily_unique_hits)))
    field(:total_sign_ups, non_null(:integer))
    field(:total_sign_ups_list, non_null(list_of(:daily_user_signups)))
  end

  object :daily_hits do
    field(:date, non_null(:date))
    field(:total_hits, non_null(:integer))
  end

  object :daily_unique_hits do
    field(:date, non_null(:date))
    field(:unique_hits, non_null(:integer))
  end

  object :daily_user_signups do
    field(:date, non_null(:date))
    field(:total_signups, non_null(:integer))
  end

  object :email_reputation do
    field(:reputation, non_null(:integer))
  end

  object :email_reputation_data do
    field(:reputation, non_null(:integer))
    field(:bounces, non_null(:integer))
    field(:sends, non_null(:integer))
    field(:clicks, non_null(:integer))
    field(:complaints, non_null(:integer))
    field(:opens, non_null(:integer))
    field(:unsubscribes, non_null(:integer))
  end

  object :sent_email do
    field(:identifier, :integer)
    field(:subject, :string)
    field(:type, non_null(:string))
  end

  object :email_reputation_data_point do
    field(:date, non_null(:string))
    field(:reputation, non_null(:integer))
    field(:sends, non_null(:integer))
    field(:emails, non_null(list_of(:sent_email)))
  end

  object :email_stats do
    field(:identifier, :integer)
    field(:subject, :string)
    field(:type, non_null(:string))
    field(:method, non_null(:string))
    field(:sends, non_null(:integer))
    field(:opens, non_null(:integer))
    field(:clicks, non_null(:integer))
    field(:bounces, non_null(:integer))
    field(:complaints, non_null(:integer))
    field(:unsubscribes, non_null(:integer))
    field(:inserted_at, non_null(:date))
    field(:sent_at, non_null(:date))
  end

  object :utm_medium_and_sources do
    field(:medium, non_null(:string))
    field(:sources, non_null(list_of(:string)))
  end

  object :existing_utm_fields do
    field(:utm_campaigns, non_null(list_of(:string)))
    field(:utm_mediums_and_sources, non_null(list_of(:utm_medium_and_sources)))
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  object :tracking_mutations do
    @desc "Create utm_link"
    field :create_utm_link, :utm_link do
      arg(:utm_link, :utm_link_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.CreateUtmLink.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_created", hide_args: true})
      middleware(Middleware.Tracker, %{hide_args: true})
    end

    @desc "Update utm_link"
    field :update_utm_link, :utm_link do
      arg(:id, non_null(:id))
      arg(:updated_utm_attrs, :utm_link_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&AthenaWeb.Resolvers.Tracking.UpdateUtmLink.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_updated", hide_args: true})
      middleware(Middleware.Tracker, %{hide_args: true})
    end

    @desc "Delete utm_link"
    field :delete_utm_link, :boolean do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.DeleteUtmLink.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_deleted", hide_args: true})
      middleware(Middleware.Tracker, %{hide_args: true})
    end

    @desc "Send a custom event to Hubspot"
    field :send_hs_custom_event, :boolean do
      arg(:custom_event, non_null(:hs_custom_event))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.SendHubspotCustomEvent.resolve/3)
      middleware(Middleware.Analytics, %{event: "hs_custom_event_sent"})
      middleware(Middleware.Tracker)
    end
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :tracking_queries do
    @desc "Get a single utm_link"
    field :get_utm_link, :utm_link do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.GetUtmLink.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_fetched", hide_args: true})
    end

    connection field(:utm_links, node_type: :utm_link) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.UtmLinks.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_links_fetched"})
    end

    field :utm_link_analytics, :utm_link_analytics do
      arg(:id, non_null(:id))
      arg(:start_date, :naive_datetime)
      arg(:end_date, :naive_datetime)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.UtmLinkAnalytics.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_analytics_fetched", hide_args: true})
    end

    connection field(:utm_link_analytics_signups, node_type: :utm_investor_signup) do
      arg(:utm_link_id, non_null(:id))
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.UtmLinkAnalyticsSignups.resolve/3)
      middleware(Middleware.Analytics, %{event: "utm_link_analytics_signups_fetched", hide_args: true})
    end

    @desc "Get email reputation for a company"
    field :email_reputation, :email_reputation do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.EmailReputation.resolve/3)
      middleware(Middleware.Analytics, %{event: "email_reputation_fetched", hide_args: true})
    end

    @desc "Get all email reputation data for a company"
    field :email_reputation_data, :email_reputation_data do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.EmailReputationData.resolve/3)
      middleware(Middleware.Analytics, %{event: "email_reputation_data_fetched", hide_args: true})
    end

    @desc "Get company's email reputation data for a graph"
    field :email_reputation_over_time, non_null(list_of(non_null(:email_reputation_data_point))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.EmailReputationOverTime.resolve/3)
      middleware(Middleware.Analytics, %{event: "email_reputation_yearly_graph_fetched", hide_args: true})
    end

    @desc "Get latest emails for email reputation"
    field(:email_reputation_email_stats, non_null(list_of(non_null(:email_stats)))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})

      arg(:type, non_null(:string),
        description: "The type of email data. Options: 'latest' or 'worst'. Keeping it simple for now."
      )

      resolve(&Tracking.EmailReputationEmailStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "email_reputation_email_stats_fetched"})
    end

    @desc "Get existing utm_campaigns, utm_mediums, and utm_sources for a company"
    field(:existing_utm_fields, :existing_utm_fields) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.ExistingUtmFields.resolve/3)
      middleware(Middleware.Analytics, %{event: "existing_utm_campaigns_utm_mediums_and_utm_sources_fetched"})
    end

    @desc "Get existing hub pages for a company"
    connection field(:existing_hub_pages, node_type: :existing_hub_page) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorized"})
      resolve(&Tracking.ExistingHubPages.resolve/3)
      middleware(Middleware.Analytics, %{event: "existing_hub_pages_fetched"})
    end
  end
end
