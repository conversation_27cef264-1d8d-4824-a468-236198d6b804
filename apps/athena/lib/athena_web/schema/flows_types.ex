defmodule AthenaWeb.Schema.FlowsTypes do
  @moduledoc false

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias Gaia.Flows

  enum(:distribution_channel_type,
    values: Flows.DistributionSettings.get_distribution_channel_types()
  )

  enum(:distribution_recipient_list_type,
    values: Flows.DistributionSettings.get_distribution_recipient_list_types()
  )

  enum(:flow_type, values: [:announcement, :update, :new_shareholder_welcome])

  enum(:social_platform, values: [:linkedin, :twitter])

  input_object :upsert_distribution_settings_input do
    field(:included_announcement_types, list_of(non_null(:string)))
    field(:included_update_types, list_of(non_null(:media_update_type)))
    field(:recipient_list_type, list_of(non_null(:distribution_recipient_list_type)))

    field(:excluded_contacts, list_of(non_null(:id)),
      description: "List of contact id's that should be excluded from this distribution"
    )

    field(:used_default_types, :boolean, description: "Boolean value of whether the default suggested types were used")

    field(:social_post_template, :string)

    field(:shareholder_welcome_enabled, :boolean)

    field(:email_settings, :distribution_settings_email_input)
  end

  object :current_company_distribution_settings do
    field(:id, non_null(:id))
    field(:updated_at, :iso_naive_datetime)

    field(:email, :distribution_settings)
    field(:linkedin, :distribution_settings)
    field(:twitter, :distribution_settings)
  end

  object :automation_stats do
    field(:id, non_null(:id))

    field(:shareholder_welcome_email_sent_count, :integer,
      do: resolve(&AthenaWeb.Resolvers.Flows.Fields.AutomationStats.shareholder_welcome_email_sent_count/3)
    )
  end

  object :distributed_social do
    field(:id, non_null(:id))

    field(:linkedin_post_id, :string)
    field(:linkedin_posted_at, :iso_naive_datetime)
    field(:twitter_post_id, :string)
    field(:twitter_posted_at, :iso_naive_datetime)

    field(:linkedin_post_url, :string) do
      resolve(&AthenaWeb.Resolvers.Flows.Fields.DistributedSocial.linkedin_post_url/3)
    end

    field(:twitter_post_url, :string) do
      resolve(&AthenaWeb.Resolvers.Flows.Fields.DistributedSocial.twitter_post_url/3)
    end
  end

  object :distribution_settings do
    field(:id, :id)
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:channel, :distribution_channel_type)
    field(:excluded_contacts, list_of(non_null(:id)))
    field(:included_announcement_types, list_of(non_null(:string)))
    field(:included_update_types, list_of(non_null(:media_update_type)))
    field(:is_active, :boolean)
    field(:recipient_list_type, list_of(non_null(:distribution_recipient_list_type)))
    field(:social_post_template, :string)
    field(:used_default_types, :boolean)
    field(:shareholder_welcome_enabled, :boolean)

    field(:email_settings, :distribution_settings_email, do: resolve(dataloader(Gaia.Repo)))
  end

  object :distributions_settings_email do
    field(:id, non_null(:id))
    field(:from_name, :string)
    field(:subject, :string)
    field(:email_json, :string)
    field(:email_html, :string)
  end

  input_object :distribution_settings_create_input do
    field(:channel, non_null(:distribution_channel_type))
  end

  input_object :distribution_settings_update_input do
    field(:included_announcement_types, list_of(non_null(:string)))
    field(:included_update_types, list_of(non_null(:media_update_type)))
    field(:recipient_list_type, list_of(non_null(:distribution_recipient_list_type)))
    field(:social_post_template, :string)
    field(:shareholder_welcome_enabled, :boolean)

    field(:excluded_contacts, list_of(non_null(:id)),
      description: "List of contact id's that should be excluded from this distribution"
    )

    field(:used_default_types, :boolean, description: "Boolean value of whether the default suggested types were used")
  end

  object :active_distribution_flows do
    field(:id, non_null(:id))

    field(:announcements, non_null(:boolean))
    field(:updates, non_null(:boolean))
    field(:new_shareholder_welcome, non_null(:boolean))
  end

  object :distribution_settings_email do
    field(:id, non_null(:id))
    field(:email_html, non_null(:string))
    field(:email_json, non_null(:string))
    field(:from_name, :string)
    field(:subject, :string)
  end

  input_object :distribution_settings_email_input do
    field(:email_html, :string)
    field(:email_json, :string)
    field(:from_name, :string)
    field(:subject, :string)
  end

  # object :twitter_post_statisitics do
  #   field(:id, non_null(:id))
  #   field(:impression_count, non_null(:integer))
  #   field(:like_count, non_null(:integer))
  #   field(:reply_count, non_null(:integer))
  #   field(:retweet_count, non_null(:integer))
  # end

  object :linkedin_post_statistics do
    field(:id, non_null(:id))
    field(:impression_count, non_null(:integer))
    field(:like_count, non_null(:integer))
    field(:comment_count, non_null(:integer))
    field(:share_count, non_null(:integer))
  end

  object :flows_queries do
    field :current_company_distribution_settings,
          non_null(:current_company_distribution_settings) do
      arg(:flow_type, non_null(:flow_type))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.CurrentCompanyDistributionSettings.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "current_company_distribution_settings_fetched"
      })
    end

    field :distribution_settings_for_channel_for_flow_type, :distribution_settings do
      arg(:channel, :distribution_channel_type)
      arg(:flow_type, :flow_type)

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})

      resolve(&AthenaWeb.Resolvers.Flows.DistributionSettingsForChannelForFlowType.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "distribution_settings_for_channel_for_flow_type_query"
      })
    end

    field :active_distribution_flows, :active_distribution_flows do
      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.ActiveDistributionFlows.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "active_distribution_flows_query"
      })
    end

    @desc "Stats for automations, i.e. how many emails have been sent for each automation flow"
    field :automation_stats, :automation_stats do
      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})

      resolve(&AthenaWeb.Resolvers.Flows.AutomationStats.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "automation_stats_query"
      })
    end

    @desc "Get stats for a linkedin post"
    field :linkedin_post_statistics, :linkedin_post_statistics do
      arg(:post_id, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.LinkedinPostStatistics.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "linkedin_post_statistics_query"})
    end
  end

  object :flows_mutations do
    field :create_distribution_settings_for_flow_type, :distribution_settings do
      arg(:flow_type, :flow_type)
      arg(:distribution_settings, non_null(:distribution_settings_create_input))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.CreateDistributionSettingsForFlowType.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{event: "create_distribution_settings_mutation"})
    end

    field :update_distribution_settings, :distribution_settings do
      arg(:distribution_settings_id, :id)
      arg(:distribution_settings, non_null(:distribution_settings_update_input))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.UpdateDistributionSettings.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{event: "update_distribution_settings_mutation"})
    end

    field :create_distribution_settings_email, :distribution_settings_email do
      arg(:distribution_settings_id, :id)
      arg(:distribution_settings_email, non_null(:distribution_settings_email_input))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.CreateDistributionSettingsEmail.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "create_distribution_settings_email_mutation"
      })
    end

    field :update_distribution_settings_email, :distribution_settings_email do
      arg(:distribution_settings_email_id, :id)
      arg(:distribution_settings_email, non_null(:distribution_settings_email_input))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.UpdateDistributionSettingsEmail.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "update_distribution_settings_email_mutation"
      })
    end

    field :activate_distribution_settings_for_email, :distribution_settings do
      arg(:distribution_settings_id, :id)
      arg(:distribution_settings, non_null(:distribution_settings_update_input))
      arg(:distribution_settings_email, non_null(:distribution_settings_email_input))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.ActivateDistributionSettingsForEmail.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "activate_distribution_settings_mutation"
      })
    end

    @desc "Activate all distribution settings for current company"
    field :activate_current_company_distribution_settings,
          non_null(:current_company_distribution_settings) do
      arg(:flow_type, non_null(:flow_type))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})

      resolve(&AthenaWeb.Resolvers.Flows.ActivateCurrentCompanyDistributionSettings.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "current_company_distribution_settings_activated"
      })

      middleware(AthenaWeb.Middleware.Tracker)
    end

    @desc "Deactivate all distribution settings for current company"
    field :deactivate_current_company_distribution_settings,
          non_null(:current_company_distribution_settings) do
      arg(:flow_type, non_null(:flow_type))

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})

      resolve(&AthenaWeb.Resolvers.Flows.DeactivateCurrentCompanyDistributionSettings.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{
        event: "current_company_distribution_settings_deactivated"
      })

      middleware(AthenaWeb.Middleware.Tracker)
    end

    # Deprecated 11/12/2024 - use `:generate_social_thumbnail_signed_url` instead
    field :generate_linkedin_thumbnail_signed_url, non_null(:string) do
      arg(:mime_type, non_null(:string))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.Flows.GenerateSocialThumbnailSignedUrl.resolve/3)
      middleware(Middleware.Analytics, %{event: "linkedin_thumbnail_signed_url_generated"})
    end

    field :generate_social_thumbnail_signed_url, non_null(:string) do
      arg(:mime_type, non_null(:string))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.Flows.GenerateSocialThumbnailSignedUrl.resolve/3)
      middleware(Middleware.Analytics, %{event: "social_thumbnail_signed_url_generated"})
    end

    field :send_manual_social_post, :distributed_social do
      arg(:media_id, non_null(:id))
      arg(:social_platform, non_null(:social_platform))
      arg(:text, non_null(:string))
      arg(:linkedin_thumbail_url, :string)
      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})
      resolve(&AthenaWeb.Resolvers.Flows.SendManualSocialPost.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "social_post_sent_manually"})
      middleware(AthenaWeb.Middleware.Tracker)
    end

    field :upsert_distribution_settings, non_null(:current_company_distribution_settings) do
      arg(:flow_type, non_null(:flow_type))

      arg(:email, :upsert_distribution_settings_input)
      arg(:linkedin, :upsert_distribution_settings_input)
      arg(:twitter, :upsert_distribution_settings_input)

      middleware(Middleware.BlockCloudIP)

      middleware(AthenaWeb.Middleware.Permission, %{permission: "comms_emails.admin"})

      resolve(&AthenaWeb.Resolvers.Flows.UpsertDistributionSettings.resolve/3)

      middleware(AthenaWeb.Middleware.Analytics, %{event: "distribution_settings_upserted"})

      middleware(AthenaWeb.Middleware.Tracker)
    end
  end
end
