defmodule AthenaWeb.Schema.RefinitivTypes do
  @moduledoc false

  use <PERSON><PERSON><PERSON>, :type

  alias <PERSON><PERSON><PERSON>.Middleware

  object :token do
    field(:value, non_null(:string))
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :refinitiv_queries do
    @desc "Get API token"
    field :token, :token do
      middleware(Middleware.BlockCloudIP)
      resolve(&AthenaWeb.Resolvers.Refinitiv.Token.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "refinitiv_token_fetched"})
    end
  end
end
