defmodule <PERSON><PERSON><PERSON>.Schema.SppEstimatesTypes do
  @moduledoc """
  SppOutcomes GraphQL Schema
  """

  use AthenaW<PERSON>, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias <PERSON><PERSON><PERSON>.Middleware
  alias <PERSON>Web.Resolvers

  object :spp_estimate do
    field(:low_estimate, non_null(:float))
    field(:med_estimate, non_null(:float))
    field(:high_estimate, non_null(:float))
    field(:part_rate, non_null(:float))
    field(:avg_part, non_null(:float))

    field(:top_part, list_of(:integer))
    field(:prior_part, :integer)
    field(:updated_at, non_null(:iso_naive_datetime))
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :spp_estimates_queries do
    @desc "Get SPP outcomes"
    field :spp_estimate, :spp_estimate do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.SppEstimate.resolve/3)
      middleware(Middleware.Analytics, %{event: "spp_estimate_fetched", hide_args: true})
    end
  end
end
