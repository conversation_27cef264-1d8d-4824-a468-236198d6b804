defmodule AthenaWeb.Schema.RegistersTypes do
  @moduledoc """
  Registers GraphQL Schema
  """
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic
  use Helper.Pipe

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  connection node_type: :shareholding do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Registers.Total.total/3)
    end

    edge do
      field(:shareholding, non_null(:shareholding))
    end
  end

  connection node_type: :shareholding_or_beneficial_owner_account do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Registers.ShareholdingsAndBeneficialOwnerAccounts.total/3)
    end

    edge do
      field(:shareholding_or_beneficial_owner_account, non_null(:shareholding_or_beneficial_owner_account))
    end
  end

  object :shareholding_or_beneficial_owner_account do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:account_type, non_null(:string))
    field(:share_count, :integer)
    field(:contact, :contact_partial)
  end

  object :contact_partial do
    field(:id, :id)
    field(:first_name, :string)
    field(:last_name, :string)
    field(:email, :string)
    field(:phone_number, :string)
  end

  object :daily_holding do
    field(:id, non_null(:id))

    field(:date, non_null(:date_short))

    field(:balance, non_null(:integer))
  end

  object :share_movement do
    field(:id, non_null(:id))
    field(:account_name, :string)
    field(:closing_balance, non_null(:integer))
    field(:movement, non_null(:integer))
    field(:opening_balance, non_null(:integer))
    field(:settled_at, non_null(:date_short))
    field(:movement_type, :string)
    field(:transaction_price, :float)
    field(:estimated_price, :float)
  end

  enum(:sophisticated_investor_values, values: [:potential, :unverified, :verified])
  enum(:hnw_behaviours, values: Gaia.Registers.Shareholding.hnw_behaviours())

  object :shareholding do
    field(:id, non_null(:id))

    field(:updated_at, :naive_datetime)

    field(:account_name, non_null(:string))
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_city, :string)
    field(:address_state, :string)
    field(:address_country, :string)
    field(:address_postcode, :string)
    field(:email, :string)
    field(:phone_number, :string)

    field(:biggest_movement, :integer)
    field(:broker_pid, :string)
    field(:current_holding_start_date, :date)
    field(:currency, :string)
    field(:estimated_profit_loss, :float)
    field(:estimated_total_purchase_value, :float)
    field(:estimated_total_sale_value, :float)
    field(:initial_purchase_date, :date)
    field(:hnw_identified_at, :iso_naive_datetime)
    field(:hnw_behaviour, :hnw_behaviours)
    field(:movement_count, :integer)
    field(:share_count, :integer)

    field :broker_name_short, :string do
      resolve(&Resolvers.Registers.ShareholdingFields.broker_name_short/3)
    end

    field :holder_id_masked, :string do
      resolve(&Resolvers.Registers.ShareholdingFields.holder_id_masked/3)
    end

    field(:contact, :contact, do: resolve(dataloader(Gaia.Repo)))

    field :latest_share_movement, :share_movement do
      resolve(&Resolvers.Registers.ShareholdingFields.latest_share_movement/3)
    end

    field :share_count_rank, :integer do
      resolve(&Resolvers.Registers.ShareholdingFields.share_count_rank/3)
    end

    field :sophisticated_investor_status, :sophisticated_investor_values do
      resolve(&Resolvers.Registers.ShareholdingFields.sophisticated_investor_status/3)
    end

    field :has_participated_in_spp, :boolean do
      resolve(&Resolvers.Registers.ShareholdingFields.has_participated_in_spp/3)
    end

    field :has_participated_in_placement, :boolean do
      resolve(&Resolvers.Registers.ShareholdingFields.has_participated_in_placement/3)
    end

    field :has_email_recipient, :boolean do
      arg(:email_id, non_null(:id))
      resolve(&Resolvers.Registers.ShareholdingFields.has_email_recipient/3)
    end
  end

  object :shareholding_summary do
    field(:id, non_null(:id))
    field(:announcements, non_null(list_of(:media_announcement)))
    field(:daily_holdings, non_null(list_of(:daily_holding)))
    field(:share_movements, non_null(list_of(:share_movement)))
    field(:timeseries, non_null(list_of(:timeseries)))
    field(:beneficial_owner_holdings, non_null(list_of(:beneficial_owner_holding)))
  end

  enum :shareholder_activity_type do
    value(:new, as: "new")
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :registers_queries do
    @desc "Get current company paginated shareholdings"
    connection field(:shareholdings, node_type: :shareholding) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return:
          {:ok,
           %{
             edges: [],
             page_info: %{
               has_next_page: false,
               has_previous_page: false,
               start_cursor: nil,
               end_cursor: nil
             }
           }}
      })

      resolve(&Resolvers.Registers.Shareholdings.cursor/3)
      middleware(Middleware.Analytics, %{event: "shareholdings_fetched"})
    end

    @desc "Get current company shareholdings and beneficial owner accounts"
    connection field(:shareholdings_and_beneficial_owner_accounts, node_type: :shareholding_or_beneficial_owner_account) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return:
          {:ok,
           %{
             edges: [],
             page_info: %{
               has_next_page: false,
               has_previous_page: false,
               start_cursor: nil,
               end_cursor: nil
             }
           }}
      })

      resolve(&Resolvers.Registers.ShareholdingsAndBeneficialOwnerAccounts.cursor/3)

      middleware(Middleware.Analytics, %{
        event: "shareholdings_and_beneficial_owner_accounts_fetched"
      })
    end

    field :campaign_channel_shareholdings, non_null(list_of(non_null(:shareholding))) do
      arg(:channel, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Registers.CampaignChannelShareholders.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "campaign_channel_shareholdings_fetched"
      })
    end

    field :campaign_channel_shareholdings_by_activity_type,
          non_null(list_of(non_null(:shareholding))) do
      arg(:channel, non_null(:string))
      arg(:shareholder_activity_type, non_null(:shareholder_activity_type))
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Registers.CampaignChannelShareholdersByActivityType.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "campaign_channel_shareholdings_by_activity_type_fetched"
      })
    end

    field :shareholding, :shareholding do
      arg(:id, non_null(:id))

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Registers.Shareholding.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholding_fetched"})
    end

    field :shareholding_summary, :shareholding_summary do
      arg(:id, non_null(:id))
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))
      arg(:movement_sort_order, :sort_order, default_value: :asc)

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Registers.ShareholdingSummary.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholding_summary_fetched"})
    end
  end
end
