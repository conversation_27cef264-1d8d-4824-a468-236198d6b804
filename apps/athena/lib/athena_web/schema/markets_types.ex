defmodule AthenaWeb.Schema.MarketsTypes do
  @moduledoc """
    Markets Type
  """
  use AthenaWeb, :type

  alias Athena<PERSON>eb.Resolvers

  object :ticker do
    field(:id, non_null(:id))
    field(:listing_key, non_null(:string))
    field(:market_key, non_null(:string))
    field(:refinitiv_identification_code, :string)

    field :market_listing_key, non_null(:string) do
      resolve(&Resolvers.Markets.TickerFields.market_listing_key/3)
    end
  end

  object :secondary_ticker do
    field(:id, non_null(:id))

    field(:listing_key, non_null(:string))
    field(:market_key, non_null(:string))
  end

  object :timeseries do
    field :id, non_null(:id) do
      resolve(&Resolvers.Markets.TimeseriesFields.id/3)
    end

    field(:date, non_null(:date_short))

    field(:close, :float)
    field(:open, :float)
    field(:high, :float)
    field(:low, :float)
    field(:currency, :string)
    field(:volume, :float, description: "Could be decimals if timeseries is adjusted")
  end
end
