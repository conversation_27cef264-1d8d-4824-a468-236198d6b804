defmodule AthenaWeb.Schema.WebsitesTypes do
  @moduledoc false
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  object :website_queries do
    @desc "Get the currently editing website for the current company (not the published version)"
    field :current_website, :website do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.CurrentWebsite.resolve/3)
      middleware(Middleware.Analytics, %{event: "current_website_fetched"})
    end

    @desc "Get a single page by slug"
    field :page, :page do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:slug, non_null(:string))
      resolve(&Resolvers.Websites.Page.resolve/3)
      middleware(Middleware.Analytics, %{event: "website_page_fetched"})
    end

    @desc "Get a single block by ID"
    field :block, :block do
      middleware(Middleware.BlockCloudIP)
      arg(:id, non_null(:id))
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.Block.resolve/3)
      middleware(Middleware.Analytics, %{event: "website_block_fetched"})
    end

    @desc "Get the navigation menu items for the current draft website"
    field :nav_menu_items, list_of(non_null(:nav_menu_item)) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.NavMenuItems.resolve/3)
      middleware(Middleware.Analytics, %{event: "nav_menu_items_fetched"})
    end

    @desc "Get hub stats for the hub builder index page"
    field :hub_stats, :hub_stats do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.HubStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "hub_stats_fetched"})
    end
  end

  object :website_mutations do
    @desc "Publish a website for the currently logged in users company profile"
    field :publish_website, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.PublishWebsite.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "website_published"})
    end

    @desc "Update a website for the currently logged in users company profile"
    field :update_website, non_null(:website) do
      arg(:theme_colour_primary, :string)
      arg(:theme_colour_primary_text, :string)
      arg(:theme_colour_accent, :string)
      arg(:theme_colour_accent_text, :string)
      arg(:theme_font_title, :string)
      arg(:theme_font_body, :string)
      arg(:logo_cloudinary_url, :string)
      arg(:logo_square_cloudinary_url, :string)
      arg(:is_full_website_replacement, :boolean)
      arg(:is_dark_mode, :boolean)
      arg(:is_email_popup_modal_enabled, :boolean)
      arg(:features_on_sign_up_page, :map)
      arg(:email_popup_modal_cloudinary_url, :string)
      arg(:google_analytics_id, :string)
      arg(:google_tag_manager_id, :string)
      arg(:title, :string)
      arg(:head_tag_content, :string)
      arg(:is_custom_footer_enabled, :boolean)
      arg(:footer_logo_cloudinary_url, :string)
      arg(:description, :map)
      arg(:address, :string)
      arg(:telephone, :string)
      arg(:email, :string)
      arg(:facebook_url, :string)
      arg(:linkedin_url, :string)
      arg(:twitter_url, :string)
      arg(:instagram_url, :string)
      arg(:is_sitemap_enabled, :boolean)
      arg(:sitemap_link_groups, list_of(:map))
      arg(:is_footer_links_enabled, :boolean)
      arg(:footer_links, list_of(:map))
      arg(:badges, list_of(:map))
      arg(:is_newsletter_subscription_enabled, :boolean)
      arg(:newsletter_subscription, :map)
      arg(:footer_background_color, :string)
      arg(:footer_text_color, :string)
      arg(:footer_button_background_color, :string)
      arg(:footer_button_text_color, :string)
      arg(:show_webinar_banner, :boolean)

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      resolve(&Resolvers.Websites.UpdateWebsite.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "website_updated"})
    end

    @desc "Create a new page"
    field :create_page, non_null(:page) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:title, non_null(:string))
      arg(:slug, non_null(:string))
      arg(:preset, :string)
      arg(:source_page_slug, :string)
      resolve(&Resolvers.Websites.CreatePage.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "page_created"})
    end

    @desc "Update a page"
    field :update_page, non_null(:page) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      arg(:title, non_null(:string))
      arg(:slug, non_null(:string))
      arg(:meta_description, :string)
      arg(:social_image_cloudinary_public_id, :string)
      resolve(&Resolvers.Websites.UpdatePage.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "page_updated"})
    end

    @desc "Delete a page"
    field :delete_page, non_null(:page) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Websites.DeletePage.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "page_deleted"})
    end

    @desc "Create a new block"
    field :create_block, non_null(:block) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:page_id, non_null(:id))
      arg(:type, non_null(:string))
      arg(:position, non_null(:integer))
      arg(:content, :map)
      resolve(&Resolvers.Websites.CreateBlock.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "block_created"})
    end

    @desc "Update a block"
    field :update_block, non_null(:block) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      arg(:content, non_null(:map))
      resolve(&Resolvers.Websites.UpdateBlock.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "block_updated"})
    end

    @desc "Delete a block"
    field :delete_block, non_null(:block) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Websites.DeleteBlock.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "block_deleted"})
    end

    @desc "Sort blocks for a given page"
    field :sort_blocks, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})

      arg(:page_id, non_null(:id))
      arg(:block_ids, list_of(:id))

      resolve(&AthenaWeb.Resolvers.Websites.SortBlocks.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "blocks_sorted"})
    end

    @desc "Create a new navigation menu item"
    field :create_nav_menu_item, non_null(:nav_menu_item) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:label, non_null(:string))
      arg(:url, :string)
      arg(:position, non_null(:integer))
      arg(:open_in_new_tab, :boolean)
      arg(:page_id, :id)
      arg(:parent_id, :id)
      resolve(&Resolvers.Websites.CreateNavMenuItem.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "nav_menu_item_created"})
    end

    @desc "Update a navigation menu item"
    field :update_nav_menu_item, non_null(:nav_menu_item) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      arg(:label, :string)
      arg(:url, :string)
      arg(:open_in_new_tab, :boolean)
      arg(:page_id, :id)
      arg(:parent_id, :id)
      resolve(&Resolvers.Websites.UpdateNavMenuItem.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "nav_menu_item_updated"})
    end

    @desc "Delete a navigation menu item"
    field :delete_nav_menu_item, non_null(:nav_menu_item) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Websites.DeleteNavMenuItem.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "nav_menu_item_deleted"})
    end

    @desc "Sort navigation menu items for a given parent"
    field :sort_nav_menu_items, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})

      arg(:parent_id, :id)
      arg(:nav_menu_item_ids, list_of(:id))

      resolve(&AthenaWeb.Resolvers.Websites.SortNavMenuItems.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "nav_menu_items_sorted"})
    end

    @desc "Sort navigation menu items when some are children of others"
    field :sort_nav_menu_items_with_children, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "websites.admin"})

      arg(:nav_menu_items, list_of(:nav_menu_item_order_input))

      resolve(&AthenaWeb.Resolvers.Websites.SortNavMenuItemsWithChildren.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "nav_menu_items_sorted"})
    end
  end

  object :website do
    field(:id, non_null(:id))
    field(:published_at, :iso_naive_datetime)
    field(:company_profile_id, :id)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:theme_colour_primary, :string)
    field(:theme_colour_primary_text, :string)
    field(:theme_colour_accent, :string)
    field(:theme_colour_accent_text, :string)
    field(:theme_font_title, :string)
    field(:theme_font_body, :string)
    field(:logo_cloudinary_url, :string)
    field(:logo_square_cloudinary_url, :string)
    field(:preview_secret, :string)
    field(:is_full_website_replacement, :boolean)
    field(:is_dark_mode, :boolean)
    field(:is_email_popup_modal_enabled, :boolean)
    field(:features_on_sign_up_page, :map)
    field(:email_popup_modal_cloudinary_url, :string)
    field(:google_analytics_id, :string)
    field(:google_tag_manager_id, :string)
    field(:title, :string)
    field(:head_tag_content, :string)
    field(:sign_up_page_terms_and_conditions, :string)
    field(:cookie_banner, :string)
    field(:show_webinar_banner, :boolean)

    # Website footer fields
    field(:is_custom_footer_enabled, :boolean)
    field(:footer_logo_cloudinary_url, :string)
    field(:description, :map)
    field(:address, :string)
    field(:telephone, :string)
    field(:email, :string)
    field(:facebook_url, :string)
    field(:linkedin_url, :string)
    field(:twitter_url, :string)
    field(:instagram_url, :string)
    field(:is_sitemap_enabled, :boolean)
    field(:sitemap_link_groups, list_of(:map))
    field(:is_footer_links_enabled, :boolean)
    field(:footer_links, list_of(:map))
    field(:badges, list_of(non_null(:map)))
    field(:is_newsletter_subscription_enabled, :boolean)
    field(:newsletter_subscription, :map)
    field(:footer_background_color, :string)
    field(:footer_text_color, :string)
    field(:footer_button_background_color, :string)
    field(:footer_button_text_color, :string)

    field(:company_profile, :company_profile, resolve: dataloader(Gaia.Repo))
    field(:published_by_company_user, :company_user, resolve: dataloader(Gaia.Repo))
    field(:pages, list_of(:page), resolve: dataloader(Gaia.Repo))

    field :nav_menu_items, list_of(non_null(:nav_menu_item)) do
      resolve(fn website, _, _ ->
        nav_menu_items =
          Gaia.Websites.NavMenuItem
          |> Gaia.Websites.NavMenuItem.query_parent_items(website.id)
          |> Gaia.Repo.all()

        {:ok, nav_menu_items}
      end)
    end
  end

  object :page do
    field(:id, non_null(:id))
    field(:title, :string)
    field(:slug, :string)
    field(:published_at, :iso_naive_datetime)
    field(:invalidated, :boolean)
    field(:website_id, :id)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))
    field(:is_draft, :boolean)
    field(:meta_description, :string)
    field(:social_image_cloudinary_public_id, :string)
    field(:webinar_id, :id)

    field(:last_edited_by_company_user, :company_user, resolve: dataloader(Gaia.Repo))
    field(:website, :website, resolve: dataloader(Gaia.Repo))
    field(:blocks, list_of(:block), resolve: dataloader(Gaia.Repo))
    field(:webinar, :webinar, resolve: dataloader(Gaia.Repo))
  end

  object :block do
    field(:id, non_null(:id))
    field(:type, :string)
    field(:position, :integer)
    field(:content, :map)
    field(:website_page_id, :id)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:website_page, :page, resolve: dataloader(Gaia.Repo))
    field(:last_edited_by_company_user, :company_user, resolve: dataloader(Gaia.Repo))
  end

  object :nav_menu_item do
    field(:id, non_null(:id))
    field(:label, :string)
    field(:url, :string)
    field(:position, :integer)
    field(:open_in_new_tab, :boolean)
    field(:website_id, :id)
    field(:parent_id, :id)
    field(:page_id, :id)
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:website, :website, resolve: dataloader(Gaia.Repo))
    field(:parent, :nav_menu_item, resolve: dataloader(Gaia.Repo))
    field(:page, :page, resolve: dataloader(Gaia.Repo))
    field(:children, list_of(:nav_menu_item), resolve: dataloader(Gaia.Repo))
  end

  input_object :nav_menu_item_order_input do
    field(:id, non_null(:id))
    field(:parent_id, :id)
  end

  object :hub_stats do
    field(:sign_ups_last_thirty_days, :integer)
    field(:unique_visitors_last_thirty_days, :integer)
    field(:pending_qualified_investors, :integer)
  end
end
