defmodule AthenaWeb.Schema.RaisesTypes do
  @moduledoc """
  Raises GraphQL Schema
  """
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic
  use Gettext, backend: AthenaWeb.Gettext

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias Athena<PERSON>eb.Resolvers

  object :spp_estimation_range do
    field(:h_quartile, :float)
    field(:l_quartile, :float)
    field(:min, :float)
    field(:max, :float)
    field(:med, :float)
    # Potential Value will be -1, 0, 1
    field(:performance, :integer)
  end

  object :past_placement do
    field(:id, non_null(:id))
    field(:amount_raised, :float)

    @desc """
    If tranche is T1/T2, amount_raised_total = T1 amount_raised + T2 amount_raised
    Otherwise, amount_raised_total = amount_raised
    """
    field :amount_raised_total, :float do
      resolve(&Resolvers.Raises.PastPlacementFields.amount_raised_total/3)
    end

    field(:issue_price, :float)

    field(:movement_since, :float) do
      resolve(&Resolvers.Raises.PastPlacementFields.movement_since/3)
    end

    field(:offer_announced_at, :iso_naive_datetime)
    field(:outcome_announced_at, :iso_naive_datetime)
    field(:settled_at, :date)

    @desc """
    If tranche is T1, return tranche_two_settled_at
    Otherwise, tranche_two_settled_at is nil
    """
    field(:tranche_two_settled_at, :date) do
      resolve(&Resolvers.Raises.PastPlacementFields.tranche_two_settled_at/3)
    end

    field :shares_allocated, :integer do
      resolve(&Resolvers.Raises.PastPlacementFields.shares_allocated/3)
    end

    field(:shares_issued, :integer)
    field(:trading_halt_price, :float)
    field(:tranche, :past_placement_tranche)
  end

  enum(:past_placement_tranche, values: Gaia.Raises.PastPlacement.get_tranche_values())

  connection node_type: :past_placement_participant do
    field(:options, :options)

    field(:has_invalidated_participants, non_null(:boolean)) do
      arg(:past_placement_id, non_null(:id))
      resolve(&Resolvers.Raises.Fields.PastPlacementParticipants.has_invalidated_participants/3)
    end

    field(:total, non_null(:integer)) do
      arg(:options, :options_input)
      arg(:past_placement_id, non_null(:id))

      resolve(&Resolvers.Raises.Fields.PastPlacementParticipants.total/3)
    end

    edge do
      field(:past_placement_participant, non_null(:past_placement_participant))
    end
  end

  object :past_placement_participant do
    field(:id, non_null(:id))

    field(:allocation_shares, :integer)
    field(:settlement_shares, :integer)
    field(:one_week_after_settlement_shares, :integer)
    field(:one_month_after_settlement_shares, :integer)
    field(:three_months_after_settlement_shares, :integer)

    field(:tranche_type, non_null(:past_placement_participant_tranche_type))

    field(:invalidated, non_null(:boolean))

    field(:shareholding, non_null(:shareholding), do: resolve(dataloader(Gaia.Repo)))
  end

  enum(:past_placement_participant_tranche_type,
    values: Gaia.Raises.PastPlacements.Participant.get_tranche_type_values()
  )

  object :past_placement_aftermarket_stats do
    field(:churner_count, non_null(:integer))
    field(:downgrader_count, non_null(:integer))
    field(:upgrader_count, non_null(:integer))
  end

  enum(:past_placement_aftermarket_time_range,
    values: [:one_week, :one_month, :three_months]
  )

  connection node_type: :shareholder_offer do
    field(:options, :options)

    field(:total, non_null(:integer)) do
      arg(:options, :options_input)
      resolve(&Resolvers.Raises.Fields.ShareholderOffers.total/3)
    end

    edge do
      field(:shareholder_offer, non_null(:shareholder_offer))
    end
  end

  object :shareholder_offer do
    field(:id, non_null(:id))
    field(:title, non_null(:string))
    field(:type, non_null(:shareholder_offer_type))
    field(:is_live, non_null(:boolean))
    field(:published_at, :iso_naive_datetime)
    field(:scheduled_at, :iso_naive_datetime)
    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))
    field(:last_edited_by_user, non_null(:company_user), do: resolve(dataloader(Gaia.Repo)))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:total_view_count, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_view_count/3)
    end

    field(:total_view_count_from_hub_users, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_view_count_from_hub_users/3)
    end

    field(:total_unique_visitors_count, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_unique_visitors_count/3)
    end

    field(:total_unique_visitors_count_from_hub_users, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_unique_visitors_count_from_hub_users/3)
    end

    field(:sign_ups_during_offer_period, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.sign_ups_during_offer_period/3)
    end

    field(:total_investor_presentation_downloads_count, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_investor_presentation_downloads_count/3)
    end

    field(:total_offer_booklet_downloads_count, non_null(:integer)) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.total_offer_booklet_downloads_count/3)
    end

    field(
      :shareholder_offer_engagement,
      non_null(list_of(non_null(:shareholder_offer_engagement)))
    ) do
      resolve(&Resolvers.Raises.Fields.ShareholderOffer.engagement/3)
    end

    field(:company_shareholder_offer_page, :shareholder_offer_page, do: resolve(dataloader(Gaia.Repo)))

    field(:private_viewers, non_null(list_of(non_null(:private_viewer))),
      do: resolve(&AthenaWeb.Resolvers.Raises.ShareholderOffer.resolve_private_viewers/3)
    )
  end

  enum(:shareholder_offer_type, values: Gaia.Raises.ShareholderOffer.get_type_values())

  input_object :shareholder_offer_input do
    field(:title, non_null(:string))
    field(:type, :shareholder_offer_type)
    field(:is_live, :boolean)
  end

  object :shareholder_offer_engagement do
    field(:date, non_null(:date))
    field(:total_view_count, non_null(:integer))
    field(:total_unique_visitors_count, non_null(:integer))
  end

  object :private_viewer do
    field(:id, non_null(:id))
    field(:email, non_null(:string))
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  object :raises_mutations do
    @desc """
    Generate GCS signed URL to upload placement participant list.
    """
    field :generate_past_placement_participant_list_signed_url, non_null(:string) do
      arg(:mime_type, non_null(:string))
      arg(:past_placement_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin"
      })

      resolve(&Resolvers.Raises.GeneratePastPlacementParticipantListSignedUrl.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "past_placement_participant_list_signed_url_generated"
      })
    end

    @desc "Invalidate past placement participant"
    field :invalidate_past_placement_participant, :past_placement_participant do
      arg(:participant_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.Mutations.InvalidatePastPlacementParticipant.resolve/3)
      middleware(Middleware.Analytics, %{event: "past_placement_participant_invalidated"})
      middleware(Middleware.Tracker)
    end

    @desc """
    1. Insert any missing participants
    2. Invalidate any incorrect participants
    """
    field :upsert_past_placement_participant_list,
          non_null(list_of(non_null(:past_placement_participant))) do
      arg(:past_placement_id, non_null(:id))
      arg(:participant_list, non_null(:upload))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin"
      })

      resolve(&Resolvers.Raises.UpsertPastPlacementParticipantList.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "past_placement_participant_list_upserted",
        hide_args: true
      })
    end

    @desc "Validate past placement participant"
    field :validate_past_placement_participant, :past_placement_participant do
      arg(:participant_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.Mutations.ValidatePastPlacementParticipant.resolve/3)
      middleware(Middleware.Analytics, %{event: "past_placement_participant_validated"})
      middleware(Middleware.Tracker)
    end

    @desc "Create shareholder offer"
    field :create_shareholder_offer, :shareholder_offer do
      arg(:shareholder_offer, non_null(:shareholder_offer_input))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Raises.CreateShareholderOffer.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholder_offer_created"})
      middleware(Middleware.Tracker)
    end

    @desc "Delete shareholder offer"
    field :delete_shareholder_offer, :shareholder_offer do
      arg(:shareholder_offer_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Raises.DeleteShareholderOffer.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholder_offer_deleted"})
      middleware(Middleware.Tracker)
    end

    @desc "Update shareholder offer status"
    field :update_shareholder_offer_status, :shareholder_offer do
      arg(:shareholder_offer_id, non_null(:id))
      arg(:is_live, non_null(:boolean))
      arg(:scheduled_at, :iso_naive_datetime)

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Raises.UpdateShareholderOfferStatus.resolve/3)
      middleware(Middleware.Analytics, %{event: "update_shareholder_offer_status_updated"})
      middleware(Middleware.Tracker)
    end

    @desc "Update shareholder offer title"
    field :update_shareholder_offer_title, :shareholder_offer do
      arg(:shareholder_offer_id, non_null(:id))
      arg(:title, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.UpdateShareholderOfferTitle.resolve/3)
      middleware(Middleware.Analytics, %{event: "update_shareholder_offer_title_updated"})
      middleware(Middleware.Tracker)
    end

    @desc "Create shareholder offer private viewer"
    field :create_shareholder_offer_private_viewer, :private_viewer do
      arg(:email, non_null(:string))
      arg(:shareholder_offer_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&AthenaWeb.Resolvers.Raises.CreateShareholderOfferPrivateViewer.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholder_offer_private_viewer_created"})
      middleware(Middleware.Tracker)
    end

    @desc "Delete shareholder offer private viewer"
    field :delete_shareholder_offer_private_viewer, :boolean do
      arg(:id, non_null(:id))

      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&AthenaWeb.Resolvers.Raises.DeleteShareholderOfferPrivateViewer.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholder_offer_private_viewer_deleted"})
      middleware(Middleware.Tracker)
    end
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :raises_queries do
    @desc """
    Gets a past placement by ID
    """
    field(:past_placement, non_null(:past_placement)) do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.PastPlacement.resolve/3)
      middleware(Middleware.Analytics, %{event: "past_placement_fetched"})
    end

    @desc """
    Gets a list of past placements that have been announced since 01 July 2020
    """
    field(:past_placements, non_null(list_of(non_null(:past_placement)))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin", error_return: {:ok, []}})
      resolve(&Resolvers.Raises.PastPlacements.resolve/3)
      middleware(Middleware.Analytics, %{event: "past_placements_fetched"})
    end

    @desc """
    Gets aftermarket stats by past placement
    """
    field(:past_placement_aftermarket_stats, non_null(:past_placement_aftermarket_stats)) do
      arg(:past_placement_id, non_null(:id))
      arg(:time_range, non_null(:past_placement_aftermarket_time_range))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.PastPlacementAftermarketStats.resolve/3)
      middleware(Middleware.Analytics, %{event: "past_placement_aftermarket_stats_fetched"})
    end

    @desc "Get all past placement participants by past_placement_id for CSV download"
    field(
      :all_past_placement_participants,
      non_null(list_of(non_null(:past_placement_participant)))
    ) do
      arg(:options, :options_input)
      arg(:past_placement_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Raises.Queries.AllPastPlacementParticipants.resolve/3)
      middleware(Middleware.Analytics, %{event: "all_past_placement_participants_fetched"})
    end

    @desc "Get paginated past placement participants by past_placement_id"
    connection field(:past_placement_participants, node_type: :past_placement_participant) do
      arg(:options, :options_input)
      arg(:past_placement_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&Resolvers.Raises.Queries.PastPlacementParticipantsCursor.cursor/3)
      middleware(Middleware.Analytics, %{event: "past_placement_participants_fetched"})
    end

    @desc "Get company's paginated shareholder offers"
    connection field(:shareholder_offers, node_type: :shareholder_offer) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.ShareholderOffers.cursor/3)

      middleware(Middleware.Analytics, %{event: "shareholder_offers_fetched"})
    end

    @desc "Get a shareholder offer by ID"
    field(:shareholder_offer, non_null(:shareholder_offer)) do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&Resolvers.Raises.ShareholderOffer.resolve/3)
      middleware(Middleware.Analytics, %{event: "shareholder_offer_fetched"})
    end

    field(
      :shareholder_offer_engagement,
      non_null(list_of(non_null(:shareholder_offer_engagement)))
    ) do
    end
  end
end
