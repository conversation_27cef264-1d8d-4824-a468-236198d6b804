defmodule AthenaWeb.Schema.ContactsTypes do
  @moduledoc """
  Contacts GraphQL Schema
  """
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers

  connection node_type: :amplify_investor do
    field :total, non_null(:integer) do
      arg(:search_phrase, :string)
      arg(:search_tags, list_of(non_null(:string)))
      resolve(&Resolvers.Contacts.AmplifyInvestorFields.total/3)
    end

    edge do
    end
  end

  connection node_type: :contact do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.ContactFields.total/3)
    end

    edge do
    end
  end

  connection node_type: :contact_activity do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:contact_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.ContactActivities.total/3)
    end

    edge do
    end
  end

  connection node_type: :contact_activity_month do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:contact_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.ContactActivitiesV2.total/3)
    end

    edge do
    end
  end

  connection node_type: :static_list do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.StaticLists.total/3)
    end

    edge do
    end
  end

  connection node_type: :dynamic_list do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.DynamicLists.total/3)
    end

    edge do
    end
  end

  input_object :contact_input do
    field(:address_city, :string)
    field(:address_country, :string)
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_postcode, :string)
    field(:address_state, :string)
    field(:email, :string)
    field(:first_name, :string)
    field(:invalidated, :boolean)
    field(:last_name, :string)
    field(:phone_number, :string)
    field(:company, :string)
    field(:occupation, :string)
    field(:age_range, :string)
    field(:sunrice_grower_number, :string)
  end

  enum(:contact_note_type, values: Gaia.Contacts.Note.get_contacts_note_types())

  input_object :contact_note_input do
    field(:content, :string)
    field(:invalidated, :boolean)
    field(:occured_at, :iso_naive_datetime)
    field(:type, :contact_note_type)
    field(:title, :string)
  end

  input_object :dynamic_list_input do
    field(:description, :string)
    field(:filters, list_of(non_null(:filter_input)))
    field(:name, non_null(:string))
  end

  input_object :static_list_input do
    field(:description, :string)
    field(:name, non_null(:string))
    field(:background_color, :string)
    field(:text_color, :string)
    field(:contact_ids, list_of(non_null(:id)))
  end

  # TAGS TODO: Remove once static lists are fully implemented
  input_object :tag_input do
    field(:invalidated, :boolean)
    field(:name, :string)
    field(:id, :id)
  end

  enum(:lead_status_update_option,
    values: [:investor_lead, :nominated_shareholder]
  )

  object :amplify_investor do
    field(:id, non_null(:id))

    field(:contact, :contact)
    field(:shareholding, :shareholding)
    field(:type, non_null(:string))
  end

  enum(:contact_source_type, values: Gaia.Contacts.Contact.contact_source_type() ++ [:other])
  enum(:contact_hnw_statuses, values: Gaia.Contacts.Contact.hnw_statuses())

  enum(:shareholder_status,
    values: [:investor_lead, :nominated_shareholder, :shareholder, :past_shareholder]
  )

  object :check_dynamic_list_safe_to_delete_response do
    field(:id, non_null(:id))

    field(:distribution_settings, non_null(list_of(non_null(:distribution_settings))))
    field(:draft_emails, non_null(list_of(non_null(:email))))
  end

  object :check_static_list_safe_to_delete_response do
    field(:id, non_null(:id))

    field(:distribution_settings, non_null(list_of(non_null(:distribution_settings))))
    field(:draft_emails, non_null(list_of(non_null(:email))))
  end

  object :contact do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:iso_naive_datetime))

    field(:address_city, :string)
    field(:address_country, :string)
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_postcode, :string)
    field(:address_state, :string)

    field(:contact_source, :contact_source_type) do
      resolve(fn parent, _, %{context: %{current_company_profile_user: %{permissions: permissions}}} ->
        if not Gaia.Companies.Permission.has_permission?("registers_shareholdings.admin", permissions) and
             parent.contact_source in [:registry_import, :beneficial_owners_import] do
          {:ok, :other}
        else
          {:ok, parent.contact_source}
        end
      end)
    end

    field(:email, :string)
    field(:first_name, :string)
    field(:hnw_identified_at, :iso_naive_datetime)
    field(:hnw_status, :contact_hnw_statuses)
    field(:is_nominated_shareholder, :boolean)
    field(:nominated_shareholder_identified_at, :iso_naive_datetime)
    field(:invalidated, non_null(:boolean))
    field(:last_name, :string)
    field(:phone_number, :string)
    field(:sunrice_grower_number, :string)
    field(:company, :string)
    field(:occupation, :string)
    field(:age_range, :string)

    field(:comms_unsubscribes, non_null(list_of(non_null(:contact_unsubscribe))), do: resolve(dataloader(Gaia.Repo)))

    field(:global_unsubscribe, :contact_global_unsubscribe, do: resolve(dataloader(Gaia.Repo)))

    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))

    field :has_email_recipient, :boolean do
      arg(:email_id, :id)
      resolve(&Resolvers.Contacts.ContactFields.has_email_recipient/3)
    end

    field(:suppression, :contact_suppression, do: resolve(dataloader(Gaia.Repo)))

    field :total_shareholding_rank, :integer do
      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return: {:ok, nil}
      })

      resolve(&AthenaWeb.Resolvers.Contacts.ContactFields.contact_total_shareholding_rank/3)
    end

    field :email_engagement_status, :string do
      resolve(&AthenaWeb.Resolvers.Contacts.ContactFields.email_engagement_status/3)
    end

    field :shareholder_status, :shareholder_status do
      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return: {:ok, nil}
      })

      resolve(&AthenaWeb.Resolvers.Contacts.ContactFields.shareholder_status/3)
    end

    field(:investor, :investor_user, do: resolve(dataloader(Gaia.Repo)))

    field(:shareholdings_without_preloads, non_null(list_of(non_null(:shareholding)))) do
      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return: {:ok, []}
      })

      resolve(dataloader(Gaia.Repo))
    end

    field(:shareholdings, non_null(list_of(non_null(:shareholding)))) do
      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return: {:ok, []}
      })

      resolve(
        dataloader(
          Gaia.Repo,
          :shareholdings,
          callback: fn shareholdings, _, _ ->
            {:ok,
             Gaia.Repo.preload(shareholdings, [
               :placement_demand_shareholding,
               :past_placement_participants
             ])}
          end
        )
      )
    end

    field(:tags, non_null(list_of(non_null(:tag))), do: resolve(dataloader(Gaia.Repo)))

    field(:static_lists, non_null(list_of(non_null(:static_list))), do: resolve(dataloader(Gaia.Repo)))

    # Contact creator
    field(:creator_name, :string)
    field(:creator_user, :company_user, do: resolve(dataloader(Gaia.Repo)))

    field(:imported_at, :naive_datetime)

    field(:beneficial_owner_accounts, non_null(list_of(non_null(:beneficial_owner_account)))) do
      middleware(Middleware.Permission, %{
        permission: "registers_shareholdings.admin",
        error_return: {:ok, []}
      })

      resolve(
        dataloader(
          Gaia.Repo,
          :beneficial_owner_accounts,
          callback: fn beneficial_owner_accounts, _, _ ->
            {:ok, Gaia.Repo.preload(beneficial_owner_accounts, [:beneficial_owner_holdings])}
          end
        )
      )
    end
  end

  object :contact_activity_month do
    field(:month, non_null(:iso_naive_datetime))
    field(:categories, list_of(:contact_activity_type_group))
  end

  object :contact_activity_type_group do
    field(:category, :string)
    field(:activities, list_of(:contact_activity_v2))
  end

  object :contact_activity_v2 do
    field(:id, non_null(:id))

    field(:metadata, non_null(:map))
    field(:timestamp, non_null(:string))
    field(:type, non_null(:string))
    field(:company_user, :map)
  end

  object :contact_activity do
    field(:id, non_null(:id))

    field(:metadata, non_null(:map))
    field(:timestamp, non_null(:iso_naive_datetime))
    field(:type, non_null(:string))
    field(:company_user, :map)
  end

  object :contact_note do
    field(:id, non_null(:id))

    field(:content, :string)
    field(:invalidated, non_null(:boolean))
    field(:occured_at, non_null(:iso_naive_datetime))
    field(:title, non_null(:string))
    field(:type, non_null(:contact_note_type))
  end

  object :contact_shareholding_summary do
    field(:id, non_null(:id))

    field(:announcements, non_null(list_of(:media_announcement)))
    field(:daily_holdings, non_null(list_of(:daily_holding)))
    field(:share_movements, non_null(list_of(:share_movement)))
    field(:timeseries, non_null(list_of(:timeseries)))
    field(:beneficial_owner_holdings, non_null(list_of(:beneficial_owner_aggregate_holding)))
    field(:beneficial_owner_holdings_by_account, non_null(list_of(:beneficial_owner_aggregate_holding)))
  end

  object :contact_beneficial_owner_holdings_summary do
    field(:id, non_null(:id))
    field(:beneficial_owner_holdings, non_null(list_of(:beneficial_owner_aggregate_holding)))
  end

  object :dynamic_list do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:description, :string)
    field(:estimated_contacts_size, :integer)
    field(:estimated_contactable_contacts_size, :integer)
    field(:filters, non_null(list_of(non_null(:filter))))
    field(:last_updated_at, :iso_naive_datetime)
    field(:name, non_null(:string))

    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))

    field(:last_updated_by_profile_user, :company_profile_user, do: resolve(dataloader(Gaia.Repo)))

    field(:last_used_on_email, :email, do: resolve(dataloader(Gaia.Repo)))
  end

  object :simple_static_list do
    field(:id, non_null(:id))
    field(:name, non_null(:string))

    field(:background_color, :string)
    field(:text_color, :string)
  end

  object :static_list do
    import_fields(:simple_static_list)

    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:description, :string)

    field(:total_members, non_null(:integer)) do
      resolve(&AthenaWeb.Resolvers.Contacts.StaticList.resolve_total_members/3)
    end

    field(:total_contactable_members, non_null(:integer)) do
      resolve(&AthenaWeb.Resolvers.Contacts.StaticList.resolve_total_contactable_members/3)
    end

    field(:members_contact_ids, non_null(list_of(non_null(:id)))) do
      resolve(&AthenaWeb.Resolvers.Contacts.StaticList.resolve_members_contact_ids/3)
    end

    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))

    field(:last_updated_by_profile_user, :company_profile_user, do: resolve(dataloader(Gaia.Repo)))

    field(:last_used_on_email, :email, do: resolve(dataloader(Gaia.Repo)))
  end

  object :static_list_member do
    field(:id, non_null(:id))

    field(:static_list, non_null(:static_list), do: resolve(dataloader(Gaia.Repo)))
    field(:contact, non_null(:contact), do: resolve(dataloader(Gaia.Repo)))
  end

  object :tag do
    field(:id, non_null(:id))

    field(:invalidated, non_null(:boolean))
    field(:name, non_null(:string))
  end

  object :bulk_import do
    field(:id, non_null(:id))

    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:contacts_count, non_null(:integer))

    field(:uploader_profile_user, non_null(:company_profile_user), do: resolve(dataloader(Gaia.Repo)))
  end

  ##############################################################################
  # Mutations                                                                  #
  ##############################################################################

  object :contacts_mutations do
    @desc "Create a contact"
    field :create_contact, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact, non_null(:contact_input))
      resolve(&Resolvers.Contacts.CreateContact.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_created"})
    end

    @desc "Create a note on a contact"
    field :create_contact_note, non_null(:contact_note) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact_id, non_null(:id))
      arg(:contact_note, non_null(:contact_note_input))
      resolve(&Resolvers.Contacts.CreateContactNote.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_note_created"})
    end

    @desc "Create a tag on a contact"
    field :create_tag, non_null(:tag) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact_id, non_null(:id))
      arg(:tag, non_null(:tag_input))
      resolve(&Resolvers.Contacts.CreateTag.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "tag_created"})
    end

    @desc "Link contact with investor hub user"
    field :link_contact_with_investor, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:investor_user_id, non_null(:id))
      resolve(&Resolvers.Contacts.LinkContactWithInvestor.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_linked_with_investor"})
    end

    @desc "Link contact with shareholdings"
    field :link_contact_with_shareholdings, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      arg(:id, non_null(:id))
      arg(:shareholding_ids, non_null(list_of(non_null(:id))))
      resolve(&Resolvers.Contacts.LinkContactWithShareholdings.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_linked_with_shareholdings"})
    end

    @desc "Link contact with beneficial owner accounts"
    field :link_contact_with_beneficial_owner_accounts, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      arg(:id, non_null(:id))
      arg(:beneficial_owner_account_ids, non_null(list_of(non_null(:id))))
      resolve(&Resolvers.Contacts.LinkContactWithBeneficialOwnerAccounts.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_linked_with_beneficial_owner_accounts"})
    end

    @desc "Unlink contact with investor hub user"
    field :unlink_contact_with_investor, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:investor_user_id, non_null(:id))
      resolve(&Resolvers.Contacts.UnlinkContactWithInvestor.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_unlinked_with_investor"})
    end

    @desc "Unlink contact with shareholding"
    field :unlink_contact_with_shareholding, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      arg(:id, non_null(:id))
      arg(:shareholding_id, non_null(:id))
      resolve(&Resolvers.Contacts.UnlinkContactWithShareholding.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_unlinked_with_shareholding"})
    end

    @desc "Update contact"
    field :update_contact, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:contact, non_null(:contact_input))
      resolve(&Resolvers.Contacts.UpdateContact.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_updated"})
    end

    @desc "Update note on a contact"
    field :update_contact_note, non_null(:contact_note) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:contact_note, non_null(:contact_note_input))
      resolve(&Resolvers.Contacts.UpdateContactNote.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "contact_note_updated"})
    end

    @desc "Update tag on a contact"
    field :update_tag, non_null(:tag) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:tag, non_null(:tag_input))
      resolve(&Resolvers.Contacts.UpdateTag.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "tag_updated"})
    end

    field :update_contact_lead_status, non_null(:contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      arg(:lead_status, non_null(:lead_status_update_option))
      resolve(&Resolvers.Contacts.UpdateLeadStatus.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "lead_status_updated"})
    end

    @desc "Upsert custom contacts"
    field :upsert_custom_contacts, non_null(:map) do
      arg(:custom_contacts, non_null(list_of(non_null(:contact_input))))
      arg(:audience_tags, list_of(non_null(:string)))
      arg(:is_global_unsubscribe, non_null(:boolean))
      arg(:apply_subscription_to_new_contact_only, non_null(:boolean))
      arg(:unsubscribe_scopes, list_of(non_null(:string)))
      arg(:client_answer_list_source, :string)
      arg(:client_answer_last_usage, :string)

      middleware(Middleware.BlockCloudIP)

      resolve(&Resolvers.Contacts.UpsertCustomContacts.resolve/3)
      middleware(Middleware.Permission, %{permission: "authorised"})
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "custom_contacts_upserted"})
    end

    field :create_dynamic_list, non_null(:dynamic_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:dynamic_list, non_null(:dynamic_list_input))
      resolve(&Resolvers.Contacts.CreateDynamicList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "dynamic_list_created"})
    end

    field :delete_dynamic_list, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.DeleteDynamicList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "dynamic_list_deleted"})
    end

    field :update_dynamic_list, non_null(:dynamic_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      arg(:dynamic_list, non_null(:dynamic_list_input))
      resolve(&Resolvers.Contacts.UpdateDynamicList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "dynamic_list_updated"})
    end

    field :create_static_list, non_null(:static_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:static_list, non_null(:static_list_input))
      resolve(&Resolvers.Contacts.CreateStaticList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "static_list_created"})
    end

    field :update_static_list, non_null(:static_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      arg(:static_list, non_null(:static_list_input))
      resolve(&Resolvers.Contacts.UpdateStaticList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "static_list_updated"})
    end

    field :delete_static_list, non_null(:boolean) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.DeleteStaticList.resolve/3)
      middleware(Middleware.Tracker)
      middleware(Middleware.Analytics, %{event: "static_list_deleted"})
    end

    field(:create_static_list_member, non_null(:static_list_member)) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:static_list_id, non_null(:id))
      arg(:contact_id, non_null(:id))
      resolve(&Resolvers.Contacts.CreateStaticListMember.resolve/3)
      middleware(Middleware.Analytics, %{event: "static_list_member_created"})
    end

    field(:delete_static_list_member, non_null(:boolean)) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:static_list_id, non_null(:id))
      arg(:contact_id, non_null(:id))
      resolve(&Resolvers.Contacts.DeleteStaticListMember.resolve/3)
      middleware(Middleware.Analytics, %{event: "static_list_member_deleted"})
    end
  end

  ##############################################################################
  # Queries                                                                    #
  ##############################################################################

  object :contacts_queries do
    # For the Investor search Amplify tab
    @desc "Get amplify investors based on the search phrase"
    connection field(:amplify_investors, node_type: :amplify_investor) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:search_phrase, :string)
      arg(:search_tags, list_of(non_null(:string)))
      resolve(&Resolvers.Contacts.AmplifyInvestors.resolve/3)
      middleware(Middleware.Analytics, %{event: "amplify_investors_fetched"})
    end

    @desc "Get contact activities by contact id"
    connection field(:contact_activities, node_type: :contact_activity) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.ContactActivities.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_activities_fetched"})
    end

    @desc "Get contact activities by contact id (V2)"
    connection field(:contact_activities_v2, node_type: :contact_activity_month) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact_id, non_null(:id))
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.ContactActivitiesV2.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_activities_fetched"})
    end

    @desc "Get contact latest engagement activity"
    field :contact_latest_engagement_activity, :contact_activity do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:contact_id, non_null(:id))
      resolve(&Resolvers.Contacts.ContactLatestEngagementActivityResolver.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_latest_engagement_activity_fetched"})
    end

    @desc "Get contacts"
    connection field(:contacts, node_type: :contact) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.Contacts.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_fetched"})
    end

    @desc "Get the full list of only contact ids for a filter combination"
    field :all_contact_ids, non_null(list_of(non_null(:id))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:filters, list_of(:filter_input))
      resolve(&Resolvers.Contacts.AllContactIds.resolve/3)
      middleware(Middleware.Analytics, %{event: "all_contact_ids_fetched"})
    end

    @desc "Get contact by id"
    field :contact, :contact do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.Contact.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_fetched"})
    end

    @desc "Get contact shareholding summary"
    field :contact_shareholding_summary, :contact_shareholding_summary do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      arg(:contact_id, non_null(:id))
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))
      arg(:sort_order, :sort_order, default_value: :asc)
      resolve(&Resolvers.Contacts.ContactShareholdingSummary.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_shareholding_summary_fetched"})
    end

    @desc "Get beneficial owner holdings by contact id"
    field :contact_beneficial_owner_holdings_summary, :contact_beneficial_owner_holdings_summary do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      arg(:contact_id, non_null(:id))
      arg(:account_id, non_null(:id))
      arg(:start_date, non_null(:date))
      arg(:end_date, non_null(:date))
      resolve(&Resolvers.Contacts.ContactBeneficialOwnerHoldingsSummary.resolve/3)
      middleware(Middleware.Analytics, %{event: "contact_beneficial_owner_holdings_summary_fetched"})
    end

    @desc "All existing tags on current company"
    field :existing_tags, non_null(list_of(non_null(:tag))) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Contacts.ExistingTags.resolve/3)
      middleware(Middleware.Analytics, %{event: "existing_tags_fetched"})
    end

    @desc "Get pending bulk imports for the current company"
    field :pending_bulk_imports, non_null(list_of(non_null(:bulk_import))) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      resolve(&Resolvers.Contacts.BulkImportContact.resolve/3)
      middleware(Middleware.Analytics, %{event: "pending_bulk_imports_fetched"})
    end

    connection field(:dynamic_lists, node_type: :dynamic_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.DynamicLists.resolve/3)
      middleware(Middleware.Analytics, %{event: "dynamic_lists_fetched"})
    end

    field(:dynamic_list, :dynamic_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.DynamicList.resolve/3)
      middleware(Middleware.Analytics, %{event: "dynamic_list_fetched"})
    end

    field(:check_dynamic_list_name_taken, non_null(:boolean)) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:name, non_null(:string))
      resolve(&Resolvers.Contacts.CheckDynamicListNameTaken.resolve/3)
      middleware(Middleware.Analytics, %{event: "check_dynamic_list_name_taken_fetched"})
    end

    field(:check_dynamic_list_safe_to_delete, :check_dynamic_list_safe_to_delete_response) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.CheckDynamicListSafeToDelete.resolve/3)
      middleware(Middleware.Analytics, %{event: "check_dynamic_list_safe_to_delete_fetched"})
    end

    connection field(:static_lists, node_type: :static_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:options, :options_input)
      resolve(&Resolvers.Contacts.StaticLists.resolve/3)
      middleware(Middleware.Analytics, %{event: "static_lists_fetched"})
    end

    field(:static_list, :static_list) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.StaticList.resolve/3)
      middleware(Middleware.Analytics, %{event: "static_list_fetched"})
    end

    field(:check_static_list_name_taken, non_null(:boolean)) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:name, non_null(:string))
      resolve(&Resolvers.Contacts.CheckStaticListNameTaken.resolve/3)
      middleware(Middleware.Analytics, %{event: "check_static_list_name_taken_fetched"})
    end

    field(:check_static_list_safe_to_delete, :check_static_list_safe_to_delete_response) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      arg(:id, non_null(:id))
      resolve(&Resolvers.Contacts.CheckStaticListSafeToDelete.resolve/3)
      middleware(Middleware.Analytics, %{event: "check_static_list_safe_to_delete_fetched"})
    end

    field(:static_list_suggested_names, list_of(non_null(:string))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "contacts_dynamic_lists.admin"})
      resolve(&Resolvers.Contacts.StaticListSuggestedNames.resolve/3)
      middleware(Middleware.Analytics, %{event: "static_list_suggested_names_fetched"})
    end

    field(:most_recently_used_static_lists, list_of(non_null(:static_list))) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Contacts.MostRecentlyUsedStaticLists.resolve/3)
      middleware(Middleware.Analytics, %{event: "most_used_static_lists_fetched"})
    end

    @desc "All existing static list tags on current company"
    field :existing_static_lists, non_null(list_of(non_null(:simple_static_list))) do
      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "authorised"})
      resolve(&Resolvers.Contacts.ExistingStaticLists.resolve/3)
      middleware(Middleware.Analytics, %{event: "existing_tags_fetched"})
    end
  end
end
