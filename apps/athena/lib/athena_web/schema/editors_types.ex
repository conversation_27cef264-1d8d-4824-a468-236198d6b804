defmodule AthenaWeb.Schema.EditorsTypes do
  @moduledoc false

  use AthenaWeb, :type

  alias <PERSON><PERSON>eb.Middleware

  object :editors_mutations do
    field :generate_asset_signed_url, :string do
      arg(:mime_type, non_null(:string))
      middleware(Middleware.BlockCloudIP)
      middleware(AthenaWeb.Middleware.Permission, %{permission: "authorised"})
      resolve(&AthenaWeb.Resolvers.Editors.GenerateAssetSignedUrl.resolve/3)
      middleware(AthenaWeb.Middleware.Analytics, %{event: "asset_signed_url_generated"})
    end
  end
end
