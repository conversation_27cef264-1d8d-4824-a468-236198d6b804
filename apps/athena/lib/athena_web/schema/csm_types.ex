defmodule AthenaWeb.Schema.CsmTypes do
  @moduledoc false
  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  alias Athena<PERSON>eb.Middleware
  alias <PERSON>Web.Resolvers

  object :csm_mutations do
    @desc "Register interest in new feature"
    field :register_interest_in_feature, non_null(:boolean) do
      arg(:feature_id, non_null(:string))

      middleware(Middleware.BlockCloudIP)
      resolve(&Resolvers.Webinars.RegisterInterestInFeature.resolve/3)
      middleware(Middleware.Analytics, %{event: "registered_interest_in_feature"})
      middleware(Middleware.Tracker)
    end
  end
end
