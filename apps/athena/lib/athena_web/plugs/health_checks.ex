defmodule AthenaWeb.HealthChecks do
  @moduledoc "Add liveness, readiness and uptime probes to an endpoint."

  @behaviour Plug

  def init(config), do: config

  def call(%Plug.Conn{request_path: "/readiness_check"} = conn, _opts) do
    conn
    |> Plug.Conn.send_resp(200, "OK")
    |> Plug.Conn.halt()
  end

  def call(%Plug.Conn{request_path: "/liveness_check"} = conn, _opts) do
    conn
    |> Plug.Conn.send_resp(200, "OK")
    |> Plug.Conn.halt()
  end

  def call(conn, _opts), do: conn
end
