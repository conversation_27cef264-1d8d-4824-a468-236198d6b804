defmodule AthenaWeb.Plugs.LogEmailBasicAuth do
  @moduledoc """
  Plug for log email basic auth

  Adding authentication to protect endpoints
  """
  def log_email_basic_auth(conn, _opts) do
    %{username: username, password: password} =
      Application.get_env(:athena, :log_email_basic_auth, %{username: "", password: ""})

    Plug.BasicAuth.basic_auth(conn, username: username, password: password)
  end
end
