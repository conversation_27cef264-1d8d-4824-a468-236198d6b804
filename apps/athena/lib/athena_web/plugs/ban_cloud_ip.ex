defmodule AthenaWeb.BanCloudIp do
  @moduledoc "BanCloudIp Plug"

  import Phoenix.Controller
  import Plug.Conn

  alias Gaia.CloudIps
  alias Gaia.CloudIps.CloudIp

  require Logger

  def init(opts), do: opts

  def call(conn, _) do
    cloud_ip = conn.remote_ip |> Tuple.to_list() |> Enum.join(".")
    user_agent = conn |> get_req_header("user-agent") |> Enum.at(0)

    user_agent
    |> String.contains?(CloudIps.allowed_user_agent_keywords())
    |> case do
      true ->
        conn

      false ->
        try do
          iptrie = :persistent_term.get({CloudIpFetcher, :cloud_trie})

          lookup_cloud_ip_from_iptrie = Iptrie.lookup(iptrie, cloud_ip)

          lookup_cloud_ip_from_ban_list =
            CloudIps.get_cloud_ip_by_ip_address_and_request_path(%{ip_address: cloud_ip, request_path: conn.request_path})

          maybe_allow_user_agent(
            %{
              lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie,
              lookup_cloud_ip_from_ban_list: lookup_cloud_ip_from_ban_list,
              cloud_ip: cloud_ip,
              request_path: conn.request_path,
              user_agent: user_agent
            },
            conn
          )
        rescue
          e ->
            Logger.info("Iptrie lookup error: #{inspect(e)}", %{"cloud_ip" => cloud_ip})
            conn
        end
    end
  end

  defp maybe_allow_user_agent(%{lookup_cloud_ip_from_ban_list: %CloudIp{user_agent: user_agent}} = args, conn)
       when not is_nil(user_agent) do
    user_agent
    |> String.contains?(CloudIps.allowed_user_agent_keywords())
    |> case do
      true -> conn
      false -> ban_cloud_ip(args, conn)
    end
  end

  defp maybe_allow_user_agent(args, conn) do
    ban_cloud_ip(args, conn)
  end

  defp deliver_slack_notification(%{cloud_ip: cloud_ip, request_path: request_path, blocked_reason: blocked_reason}, conn) do
    Slack.Message.send(
      %{
        attachments: [
          %Slack.Message{
            title: "Cloud IP Fetched - Athena",
            title_link: "https://www.ipqualityscore.com/free-ip-lookup-proxy-vpn-test/lookup/#{cloud_ip}",
            text: "IP Address: #{cloud_ip}\nRequest Path: #{request_path}\nReason: #{blocked_reason}"
          }
        ]
      },
      :cloud_ip_fetched_notifier_webhook_url
    )

    conn
    |> put_status(403)
    |> json(%{error: %{status: 403, message: "Access denied!"}})
    |> halt()
  end

  defp ban_cloud_ip(%{lookup_cloud_ip_from_iptrie: nil, lookup_cloud_ip_from_ban_list: _}, conn) do
    conn
  end

  defp ban_cloud_ip(
         %{
           lookup_cloud_ip_from_iptrie: _lookup_cloud_ip_from_iptrie,
           lookup_cloud_ip_from_ban_list: %CloudIp{is_allowed: true}
         },
         conn
       ) do
    conn
  end

  defp ban_cloud_ip(
         %{
           lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie,
           lookup_cloud_ip_from_ban_list: %CloudIp{allowed_by: allowd_by}
         } = params,
         conn
       )
       when not is_nil(allowd_by) do
    utc_now = NaiveDateTime.utc_now(:second)

    utc_now
    |> Timex.compare(allowd_by)
    |> case do
      1 ->
        deliver_slack_notification(
          %{
            cloud_ip: Map.get(params, :cloud_ip),
            request_path: Map.get(params, :request_path),
            blocked_reason: elem(lookup_cloud_ip_from_iptrie, 1)
          },
          conn
        )

      _ ->
        conn
    end
  end

  defp ban_cloud_ip(
         %{lookup_cloud_ip_from_iptrie: lookup_cloud_ip_from_iptrie, lookup_cloud_ip_from_ban_list: _} = params,
         conn
       ) do
    CloudIps.create_cloud_ip(%{
      ip_address: Map.get(params, :cloud_ip),
      is_allowed: false,
      source: :athena,
      user_agent: Map.get(params, :user_agent),
      category: elem(lookup_cloud_ip_from_iptrie, 1),
      request_path: Map.get(params, :request_path)
    })

    deliver_slack_notification(
      %{
        cloud_ip: Map.get(params, :cloud_ip),
        request_path: Map.get(params, :request_path),
        blocked_reason: elem(lookup_cloud_ip_from_iptrie, 1)
      },
      conn
    )
  end
end
