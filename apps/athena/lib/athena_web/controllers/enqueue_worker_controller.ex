defmodule AthenaWeb.EnqueueWorkerController do
  @moduledoc """
  A controller used by Athena E2E tests to enqueue Oban Worker jobs by specifying the worker in POST params.
  This controller is only available in the "dev" and "staging" environments.
  """
  use AthenaWeb, :controller

  alias Gaia.Workers.Playwright.CleanTestCompanyUsers

  def enqueue(conn, %{"worker" => "CleanTestCompanyUsers"}) do
    # We don't want this to run immediately as tests requiring authentication
    # will be relying on these test users still existing in the database.
    case CleanTestCompanyUsers.enqueue(%{}, schedule_in: {15, :minutes}) do
      {:ok, %Oban.Job{}} ->
        send_resp(conn, 201, [])

      {:error, _error} ->
        send_resp(conn, 400, [])
    end
  end

  def enqueue(conn, _params) do
    send_resp(conn, 400, [])
  end
end
