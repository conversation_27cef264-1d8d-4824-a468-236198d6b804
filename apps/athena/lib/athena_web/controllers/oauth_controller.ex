defmodule AthenaWeb.OAuthController do
  @moduledoc """
  OAuth controller responsible for handling connecting to OAuth provider
  Supports LinkedIn and Twitter
  """
  use <PERSON><PERSON><PERSON>, :controller

  import Gaia.Socials.Helper

  alias Gaia.Companies
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection
  alias Gaia.Socials
  alias Socials.LinkedIn
  alias Socials.Twitter

  require Helper.Error.Custom.ErrorHandler

  #########################################################################
  # Request                                                               #
  #########################################################################

  def request(%{assigns: %{current_company_profile_user: %ProfileUser{}}} = conn, %{
        "provider" => "linkedin",
        "redirect" => redirect,
        "ticker" => ticker
      }) do
    state = Ecto.UUID.generate()

    # Put the generated state on session to prevent CSRF
    conn
    |> put_session(:linkedin_oauth_state, state)
    |> redirect(external: LinkedIn.get_authorization_url(redirect, ticker, state))
  end

  def request(%{assigns: %{current_company_profile_user: %ProfileUser{id: company_profile_user_id}}} = conn, %{
        "provider" => "twitter",
        "redirect" => redirect,
        "ticker" => ticker
      }) do
    case Twitter.request_token(%{redirect: redirect, ticker: ticker}) do
      {:ok,
       %{
         "oauth_callback_confirmed" => _,
         "oauth_token" => oauth_token,
         "oauth_token_secret" => _
       }} ->
        redirect(conn, external: Twitter.get_authorize_url(oauth_token))

      error ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error on AthenaWeb.OAuthController.request for Twitter",
          %{
            company_profile_user_id: company_profile_user_id
          },
          error
        )

        send_error(conn, redirect, "Cannot connect with Twitter")
    end
  end

  def request(conn, %{"redirect" => redirect}) do
    send_error(conn, redirect, "Cannot connect")
  end

  # Show a generic error message as fallback
  def request(conn, _) do
    conn
    |> put_status(500)
    |> text("error")
  end

  #########################################################################
  # Callback                                                              #
  #########################################################################

  def callback(
        %{
          assigns: %{
            current_company_profile_user: %ProfileUser{id: company_profile_user_id, profile: %{id: company_profile_id}}
          }
        } = conn,
        %{"code" => code, "provider" => "linkedin", "redirect" => redirect, "state" => state, "ticker" => ticker}
      ) do
    # state is used to prevent CSRF attacks
    with {:check_state, true} <-
           {:check_state, state === get_session(conn, :linkedin_oauth_state)},
         {:ok,
          %{
            "access_token" => access_token,
            "expires_in" => expires_in,
            "refresh_token" => refresh_token,
            "refresh_token_expires_in" => refresh_token_expires_in
          }} <- LinkedIn.access_token(code, redirect, ticker),
         {:ok, %SocialConnection{}} <-
           Companies.create_or_update_social_connection(%{
             company_profile_id: company_profile_id,
             linkedin_access_token: access_token,
             linkedin_access_token_expires_at: Timex.shift(NaiveDateTime.utc_now(:second), seconds: expires_in),
             linkedin_refresh_token: refresh_token,
             linkedin_refresh_token_expires_at:
               Timex.shift(NaiveDateTime.utc_now(:second), seconds: refresh_token_expires_in)
           }) do
      send_success_linkedin(conn, redirect)
    else
      error ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error on AthenaWeb.OAuthController.callback for LinkedIn",
          %{
            company_profile_user_id: company_profile_user_id
          },
          error
        )

        send_error(conn, redirect, "Cannot connect with LinkedIn")
    end
  end

  # When user cancels LinkedIn OAuth workflow
  def callback(%{assigns: %{current_company_profile_user: %ProfileUser{}}} = conn, %{
        "error" => _error,
        "error_description" => _,
        "provider" => "linkedin",
        "redirect" => redirect,
        "state" => _,
        "ticker" => _
      }) do
    # LinkedIn returns two possible values for error: user_cancelled_login or user_cancelled_authorize
    # Redirect user to their original page when they cancelled OAuth flow
    redirect(conn, external: redirect)
  end

  def callback(
        %{
          assigns: %{
            current_company_profile_user: %ProfileUser{id: company_profile_user_id, profile: %{id: company_profile_id}}
          }
        } = conn,
        %{
          "oauth_token" => token,
          "oauth_verifier" => verifier,
          "provider" => "twitter",
          "redirect" => redirect,
          "ticker" => _
        }
      ) do
    with {:ok,
          %{
            "oauth_token" => oauth_token,
            "oauth_token_secret" => oauth_token_secret,
            "screen_name" => username,
            "user_id" => user_id
          }} <-
           Twitter.access_token(token, verifier),
         {:ok, %SocialConnection{}} <-
           Companies.create_or_update_social_connection(%{
             company_profile_id: company_profile_id,
             twitter_oauth_token: oauth_token,
             twitter_oauth_token_secret: oauth_token_secret,
             twitter_username: username,
             twitter_user_id: user_id
           }) do
      send_success_twitter(conn, redirect)
    else
      error ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error on AthenaWeb.OAuthController.callback for Twitter",
          %{
            company_profile_user_id: company_profile_user_id
          },
          error
        )

        send_error(conn, redirect, "Cannot connect with Twitter")
    end
  end

  # When user cancels Twitter OAuth workflow
  def callback(%{assigns: %{current_company_profile_user: %ProfileUser{}}} = conn, %{
        "denied" => _,
        "provider" => "twitter",
        "redirect" => redirect,
        "ticker" => _
      }) do
    # Redirect user to their original page when they cancelled OAuth flow
    redirect(conn, external: redirect)
  end

  def callback(conn, %{"redirect" => redirect}) do
    send_error(conn, redirect, "Cannot connect")
  end

  # Show a generic error message as fallback
  def callback(conn, _) do
    conn
    |> put_status(500)
    |> text("error")
  end

  #########################################################################
  # Helper                                                                #
  #########################################################################

  defp send_error(conn, redirect, message) do
    send_response(conn, redirect, %{status: "error", message: message})
  end

  defp send_success_linkedin(conn, redirect) do
    send_response(conn, redirect, %{status: "success_linkedin"})
  end

  defp send_success_twitter(conn, redirect) do
    send_response(conn, redirect, %{status: "success_twitter"})
  end

  defp send_response(conn, redirect, query) do
    redirect_url =
      redirect
      |> URI.parse()
      |> URI.append_query(encode_query(query))
      |> URI.to_string()

    redirect(conn, external: redirect_url)
  end
end
