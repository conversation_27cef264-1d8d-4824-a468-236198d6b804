defmodule AthenaWeb.PlaywrightController do
  @moduledoc """
  A controller used by Athena E2E tests to fetch helper data - notably test user emails.
  This controller is only available in the "development" and "staging" environments.
  """
  use AthenaWeb, :controller

  alias AthenaWeb.Resolvers.Companies.InviteAndCreateCompanyProfileUsers
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def invite_test_company_user(conn, %{"email" => email}) do
    with true <- String.contains?(email, Companies.test_company_user_email_host()),
         %Profile{id: company_profile_id} <- Companies.get_test_company_profile(),
         {:ok, [%{success: %ProfileUser{job_title: "Playwright Drone", status: :pending}}]} <-
           InviteAndCreateCompanyProfileUsers.invite_and_create_company_profile_user_exec(
             [%{email: email, job_title: "Playwright Drone"}],
             company_profile_id,
             []
           ) do
      send_resp(conn, 201, [])
    else
      _ ->
        send_resp(conn, 400, [])
    end
  end

  def get_test_company_user_email(conn, _params) do
    case Companies.get_one_test_company_user_email() do
      nil ->
        send_resp(conn, 404, "Not found")

      investor_email when is_binary(investor_email) ->
        json(conn, %{email: investor_email})
    end
  end
end
