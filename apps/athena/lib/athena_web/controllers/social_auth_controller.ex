defmodule AthenaWeb.SocialAuthController do
  @moduledoc """
    Social Auth Controller
  """
  use AthenaWeb, :controller

  plug <PERSON>eb<PERSON><PERSON> when action in [:callback]

  def request(conn, %{"provider" => provider, "redirect" => redirect_url, "current" => current_url}) do
    with {:ok, [providers: providers]} <- Application.fetch_env(:ueberauth, Ueberauth),
         config when not is_nil(config) <- Keyword.get(providers, String.to_atom(provider)) do
      conn
      |> put_session(:redirect_url, redirect_url)
      |> put_session(:current_url, current_url)
      |> Ueberauth.run_request(provider, config)
    else
      # TODO: Improve error rendering.
      _ ->
        url = add_query_param(current_url, "error", "internal_server_error")

        redirect(conn, external: url)
    end
  end

  def request(conn, _) do
    conn
    |> put_status(404)
    |> json(%{error: %{status: 404, message: "Internal Server Error"}})
  end

  @doc """
  Callback for social auth
  CAUTION: Microsoft is not working in here because it can only accept locahost with out .com
  """
  def callback(
        %Plug.Conn{
          assigns: %{
            ueberauth_auth: %Ueberauth.Auth{
              info: %Ueberauth.Auth.Info{email: email, first_name: first_name, last_name: last_name},
              provider: provider,
              extra: extra
            }
          }
        } = conn,
        _params
      ) do
    redirect_url = get_session(conn, :redirect_url)
    current_url = get_session(conn, :current_url)

    # Verify email ownership for Microsoft provider
    verified_email = verify_email(provider, email, extra)

    handle_auth_result(conn, verified_email, first_name, last_name, redirect_url, current_url)
  end

  def callback(conn, _params) do
    current_url = get_session(conn, :current_url)
    url = add_query_param(current_url, "error", "internal_server_error")

    redirect(conn, external: url)
  end

  # Verify email based on provider
  defp verify_email(:microsoft, email, extra) do
    # Extract the UPN (User Principal Name) which is the actual login identity
    raw_info = extra.raw_info
    user_data = raw_info[:user] || %{}
    upn = user_data["userPrincipalName"]
    email_verified = user_data["verified_email"] || false

    # Only use the email if it matches the domain in the UPN
    # or if the email is verified
    if (upn && String.downcase(upn) == String.downcase(email)) || email_verified do
      email
    else
      require Logger

      Logger.error("Email verification failed for Microsoft login", %{
        email: email,
        upn: upn,
        email_verified: email_verified
      })

      nil
    end
  end

  defp verify_email(_provider, email, _extra), do: email

  # Handle authentication result based on verified email
  defp handle_auth_result(conn, nil, _first_name, _last_name, _redirect_url, current_url) do
    url = add_query_param(current_url, "error", "email_verification_failed")
    redirect(conn, external: url)
  end

  defp handle_auth_result(conn, email, first_name, last_name, redirect_url, current_url) do
    case Gaia.Companies.get_user_by_email(email) do
      %Gaia.Companies.User{} = user ->
        maybe_active_company_user(user, first_name, last_name)
        AthenaWeb.CompanyUserAuth.log_in_company_user(conn, user, %{redirect_url: redirect_url, sso?: true})

      nil ->
        url = add_query_param(current_url, "error", "user_not_found")
        redirect(conn, external: url)

      _ ->
        url = add_query_param(current_url, "error", "internal_server_error")
        redirect(conn, external: url)
    end
  end

  defp maybe_active_company_user(user, first_name, last_name) do
    with %Gaia.Companies.User{
           first_name: nil,
           last_name: nil,
           company_profile_users: [%Gaia.Companies.ProfileUser{} = profile_user | _]
         } <- Gaia.Repo.preload(user, :company_profile_users),
         true <- Gaia.Companies.is_user_sso_only?(user) do
      Gaia.Companies.update_user_and_profile_user_information(
        user,
        profile_user,
        %{
          first_name: first_name,
          last_name: last_name,
          information_confirmed_at: NaiveDateTime.utc_now(:second),
          confirmed_at: NaiveDateTime.utc_now(:second)
        },
        %{status: :active, activated_at: NaiveDateTime.utc_now(:second)}
      )
    else
      _ -> :skip
    end
  end

  defp add_query_param(url, key, value) do
    url |> URI.parse() |> Map.put(:query, URI.encode_query(%{key => value})) |> URI.to_string()
  end
end
