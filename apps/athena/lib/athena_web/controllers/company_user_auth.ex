defmodule AthenaWeb.CompanyUserAuth do
  @moduledoc false
  import Phoenix.Controller
  # TODO: Add moduledoc
  import Plug.Conn

  alias Gaia.Companies

  # Make the remember me cookie valid for 60 days.
  # If you want bump or reduce this value, also change
  # the token expiry itself in Companies.UserToken.
  @max_age 60 * 60 * 24 * 60
  @remember_me_cookie "_athena_company_user_remember_me"
  @remember_me_options [sign: true, max_age: @max_age, same_site: "Lax"]

  @socket_token_cookie "_athena_company_user_socket_token"
  @socket_token_options [http_only: false]

  # TODO: Deprecated.
  @company_ticker_cookie "_athena_current_company_market_listing_key"

  @doc """
  Logs the company user in.

  It renews the session ID and clears the whole session
  to avoid fixation attacks. See the renew_session
  function to customize this behaviour.
  """
  def log_in_company_user(conn, company_user, params \\ %{})
  # Pattern match when login from a simulate token
  def log_in_company_user(conn, company_user, %{
        simulating_admin_user_id: simulating_admin_user_id,
        market_listing_key: market_listing_key
      }) do
    token = Companies.generate_simulating_user_token(company_user, simulating_admin_user_id)
    athena_url = "#{Application.get_env(:helper, :athena_url)}/#{market_listing_key}"

    conn
    |> renew_session()
    |> put_session(:company_user_token, token)
    |> put_session(:simulate, true)
    |> put_session(:simulating_admin_user_id, simulating_admin_user_id)
    # TODO: Deprecated.
    |> put_resp_cookie(@company_ticker_cookie, market_listing_key, domain: get_cookie_domain())
    |> write_socket_token_cookie(token)
    |> redirect(external: athena_url)
  end

  def log_in_company_user(conn, company_user, %{sso?: true, redirect_url: redirect_url}) do
    token = Companies.generate_user_session_token(company_user)

    conn
    |> renew_session()
    |> put_session(:company_user_token, token)
    |> write_socket_token_cookie(token)
    |> redirect(external: redirect_url)
  end

  def log_in_company_user(conn, company_user, params) do
    token = Companies.generate_user_session_token(company_user)

    conn
    |> renew_session()
    |> put_session(:company_user_token, token)
    |> maybe_write_remember_me_cookie(token, params)
    |> write_socket_token_cookie(token)
    |> json(%{success: true})
  end

  defp maybe_write_remember_me_cookie(conn, token, %{"remember_me" => true}) do
    put_resp_cookie(
      conn,
      @remember_me_cookie,
      token,
      @remember_me_options ++ [domain: get_cookie_domain()]
    )
  end

  defp maybe_write_remember_me_cookie(conn, _token, _params) do
    conn
  end

  defp write_socket_token_cookie(conn, token) do
    encoded_token = Base.url_encode64(token, padding: false)

    put_resp_cookie(
      conn,
      @socket_token_cookie,
      encoded_token,
      @socket_token_options ++ [domain: get_cookie_domain()]
    )
  end

  # This function renews the session ID and erases the whole
  # session to avoid fixation attacks. If there is any data
  # in the session you may want to preserve after log in/log out,
  # you must explicitly fetch the session data before clearing
  # and then immediately set it after clearing, for example:
  #
  #     defp renew_session(conn) do
  #       preferred_locale = get_session(conn, :preferred_locale)
  #
  #       conn
  #       |> configure_session(renew: true)
  #       |> clear_session()
  #       |> put_session(:preferred_locale, preferred_locale)
  #     end
  #
  defp renew_session(conn) do
    conn
    |> configure_session(renew: true)
    |> clear_session()
  end

  @doc """
  Logs the company user out.

  It clears all session data for safety. See renew_session.
  """
  def log_out_company_user(conn) do
    company_user_token = get_session(conn, :company_user_token)
    company_user_token && Companies.delete_user_session_token(company_user_token)

    conn
    |> renew_session()
    |> delete_resp_cookie(@remember_me_cookie)
    |> delete_resp_cookie(@socket_token_cookie)
    |> json(%{success: true})
  end

  @doc """
  Authenticates the company user by looking into the session
  and remember me token.
  """
  def authenticate(conn, _opts) do
    {company_user_token, conn} = ensure_company_user_token(conn)
    current_company_ticker = get_ticker_from_conn(conn)
    simulating_admin_user_id = get_session(conn, :simulating_admin_user_id)

    conn
    |> maybe_assign_company_profile_user_or_company_user(
      current_company_ticker,
      company_user_token
    )
    |> assign(:simulating_admin_user_id, simulating_admin_user_id)
  end

  defp maybe_assign_company_profile_user_or_company_user(conn, market_listing_key, token)
       when not is_nil(market_listing_key) and not is_nil(token) do
    case Gaia.Companies.get_profile_user_context_by_market_listing_key_and_user_token(market_listing_key, token) do
      %Gaia.Companies.ProfileUser{} = profile_user ->
        assign(
          conn,
          :current_company_profile_user,
          profile_user
        )

      nil ->
        maybe_assign_company_profile_user_or_company_user(conn, nil, token)
    end
  end

  # This is a callback scenario when `company_ticker_cookie` is lost.
  # Always refer to `current_company_profile_user` to determine authentication state.
  defp maybe_assign_company_profile_user_or_company_user(conn, _ticker, token) when not is_nil(token) do
    assign(
      conn,
      :current_company_user,
      Gaia.Companies.get_user_by_session_token(token)
    )
  end

  defp maybe_assign_company_profile_user_or_company_user(conn, _ticker, _token), do: conn

  defp ensure_company_user_token(conn) do
    if company_user_token = get_session(conn, :company_user_token) do
      {company_user_token, conn}
    else
      conn = fetch_cookies(conn, signed: [@remember_me_cookie])

      if company_user_token = conn.cookies[@remember_me_cookie] do
        {company_user_token,
         conn
         |> put_session(:company_user_token, company_user_token)
         |> write_socket_token_cookie(company_user_token)}
      else
        {nil, conn}
      end
    end
  end

  # TODO: Deprecated.
  defp get_cookie_domain do
    (Application.get_env(:helper, :runtime_env) == "development" && ".localhost.com") ||
      ".investorhub.com"
  end

  defp get_ticker_from_conn(%Plug.Conn{params: %{"market_listing_key" => market_listing_key}}), do: market_listing_key

  # TODO: Deprecate this in the future, this before UK expansion
  defp get_ticker_from_conn(%Plug.Conn{params: %{"ticker" => market_listing_key}}), do: market_listing_key

  # TODO: Deprecated.
  defp get_ticker_from_conn(%Plug.Conn{req_cookies: req_cookies}) do
    Map.get(req_cookies, @company_ticker_cookie, nil)
  end
end
