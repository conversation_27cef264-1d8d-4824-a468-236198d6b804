defmodule AthenaWeb.CompanyUserSessionController do
  use <PERSON><PERSON><PERSON>, :controller

  alias Athena<PERSON>eb.CompanyUserAuth
  alias <PERSON><PERSON>.Companies

  def simulate(conn, %{"market_listing_key" => market_listing_key, "token" => token}) do
    case Gaia.Admins.get_admin_user_and_company_user_by_simulate_token(token) do
      %Gaia.Admins.UserSimulateToken{
        admin_user: %Gaia.Admins.User{id: admin_user_id},
        company_user: %Gaia.Companies.User{id: user_id} = company_user
      } = user_simulate_token ->
        Gaia.Admins.delete_user_simulate_token(user_simulate_token)

        maybe_send_simulation_notifier(company_user, market_listing_key)

        Analytics.track("athena", "admin_simulate_company_user", user_id, %{
          admin_user_id: admin_user_id,
          company_user_id: company_user.id,
          market_listing_key: market_listing_key
        })

        CompanyUserAuth.log_in_company_user(conn, company_user, %{
          simulating_admin_user_id: admin_user_id,
          market_listing_key: market_listing_key
        })

      _ ->
        simulate_return_error(conn)
    end
  end

  def simulate(conn, _params), do: simulate_return_error(conn)

  defp maybe_send_simulation_notifier(%{email: email} = company_user, market_listing_key) do
    if String.ends_with?(email, "@fresh.xyz") || String.ends_with?(email, "@freshxyz.com") ||
         String.ends_with?(email, "@freshamplify.com") ||
         String.ends_with?(email, "@investorhub.com") do
      :skip
    else
      company_user
      |> Gaia.Repo.preload(company_profiles: :ticker)
      |> EmailTransactional.Company.account_simulated(market_listing_key)
      |> EmailTransactional.Mailer.deliver()
    end
  end

  defp simulate_return_error(conn),
    do: conn |> put_status(401) |> json(%{error: %{status: 401, message: "Invalid simulate token"}})

  def create(conn, %{"company_user" => company_user_params}) do
    %{"email" => email, "password" => password} = company_user_params

    with %Companies.User{} = company_user <- Companies.get_user_by_email_and_password(email, password),
         false <- Companies.is_user_sso_only?(company_user) do
      CompanyUserAuth.log_in_company_user(conn, company_user, company_user_params)
    else
      true ->
        conn
        |> put_status(401)
        |> json(%{
          error: %{
            status: 401,
            message: "Passwords are disabled for this domain, please use Google or Microsoft SSO instead."
          }
        })

      _ ->
        conn
        |> put_status(401)
        |> json(%{error: %{status: 401, message: "Invalid email or password"}})
    end
  end

  def delete(conn, _params) do
    CompanyUserAuth.log_out_company_user(conn)
  end

  def simulate_status(conn, _params), do: json(conn, %{simulate: get_session(conn, :simulate) || false})
end
