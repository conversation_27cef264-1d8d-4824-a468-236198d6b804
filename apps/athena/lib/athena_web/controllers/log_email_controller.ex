defmodule AthenaWeb.LogEmailController do
  @moduledoc """
  Email conversations outside platform that clients want to track into athena CRM

  - Clients can bcc an email to our special email address that will log the email conversation into athena CRM
  - The special email address is managed by Cloudmailin and will trigger a HTTP POST request to this endpoint when it receives an email
  """
  use AthenaWeb, :controller

  alias <PERSON>aia.Companies
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Contacts.EmailLog
  alias Gaia.Repo

  @date_format "{WDshort}, {0D} {Mshort} {YYYY} {h24}:{m}:{s} {Z}"

  def webhook(conn, %{"headers" => headers, "plain" => plain} = params) do
    env = :helper |> Application.get_env(:runtime_env, "") |> String.upcase()

    date = headers |> Map.get("date") |> Timex.parse!(@date_format)
    from_email = headers |> Map.get("from") |> parse_email_address()
    message_id = get_message_id(params)
    to = Map.get(headers, "to")

    {company_profile_id, contact_id} = get_company_profile_id_and_contact_id(from_email, to)

    %{message_id: message_id}
    |> Contacts.get_email_log_by()
    |> case do
      nil ->
        Contacts.create_email_log(%{
          from: from_email,
          message_id: message_id,
          metadata: params,
          subject: Map.get(headers, "subject"),
          text_content: plain,
          to: to,
          company_profile_id: company_profile_id,
          contact_id: contact_id
        })

      %EmailLog{metadata: %{"headers" => %{"date" => existing_date}}} = email_log ->
        # Only update if the new content is newer than existing ones
        existing_date
        |> Timex.parse!(@date_format)
        |> Timex.before?(date)
        |> if do
          Contacts.update_email_log(email_log, %{metadata: params, text_content: plain})
        else
          {:skip, email_log}
        end
    end
    |> case do
      {:skip, _} ->
        :ok

      {:ok, %EmailLog{id: email_log_id, contact_id: nil}} ->
        Slack.Message.send(
          %{text: "[#{env}] - WARNING - could not attribute email log id #{email_log_id}"},
          :alert_auto_log_email_url
        )

      {:ok, %EmailLog{} = email_log} ->
        %EmailLog{
          company_profile: %Profile{hubspot_company_id: hubspot_company_id, id: company_profile_id, name: company_name}
        } = Repo.preload(email_log, :company_profile)

        Gaia.Hubspot.send_custom_event(hubspot_company_id, :crm_activity_completed, from_email)

        Slack.Message.send(
          %{text: "[#{env}] - SUCCESS - Congrats! email attributed for #{company_name} (id: #{company_profile_id})"},
          :alert_auto_log_email_url
        )
    end

    send_resp(conn, 200, "OK")
  end

  def get_company_profile_id_and_contact_id(from_email, to) do
    # Very simple attribution rules for current version
    # The number of email addresses inside `to` can only be 1
    # The sender domain needs to be from our clients and have its dns verified
    with false <- String.contains?(to, ","),
         to_email = parse_email_address(to),
         from_domain =
           ~r/@(.*)/
           |> Regex.run(from_email, capture: :all_but_first)
           |> List.first(),
         %CustomDomain{company_profile_id: company_profile_id} = custom_domain <-
           Companies.get_custom_domain_by(%{root_domain: from_domain}),
         true <- Companies.can_send_emails_from_custom_domain?(custom_domain),
         %Contact{id: contact_id} <- Contacts.get_contact_by(%{company_profile_id: company_profile_id, email: to_email}) do
      {company_profile_id, contact_id}
    else
      _ ->
        {nil, nil}
    end
  end

  # The field references will exists if replying to an email
  # Prioritise the original email's message_id so we can group emails together
  defp get_message_id(%{"headers" => %{"references" => references}}) do
    references
    |> String.split(" ")
    |> List.first()
  end

  defp get_message_id(%{"headers" => %{"message_id" => message_id}}) do
    message_id
  end

  @doc """
  Input could be "Firstname Lastname <<EMAIL>>" or <EMAIL>
  """
  def parse_email_address(input) do
    input
    |> String.contains?("<")
    |> case do
      true ->
        ~r/<(.*)>/
        |> Regex.run(input, capture: :all_but_first)
        |> List.first()

      false ->
        input
    end
    |> String.trim()
    |> String.downcase()
  end
end
