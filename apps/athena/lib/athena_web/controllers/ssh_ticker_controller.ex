defmodule AthenaWeb.SshTickerController do
  @moduledoc """
    A controller used for return SSH current price request by clients
    It is a hacky solution we should review at some point
  """

  use AthenaWeb, :controller

  plug :auth

  defp auth(conn, _opts) do
    username = Application.fetch_env!(:helper, :ssh_username)
    password = Application.fetch_env!(:helper, :ssh_password)
    Plug.BasicAuth.basic_auth(conn, username: username, password: password)
  end

  def webhook(conn, _params) do
    %{
      cf_last: cf_last
    } = Refinitiv.get_quote("SSH")

    send_resp(conn, 200, "#{cf_last}")
  end
end
