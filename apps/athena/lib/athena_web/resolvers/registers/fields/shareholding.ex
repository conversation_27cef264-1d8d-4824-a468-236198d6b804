defmodule AthenaWeb.Resolvers.Registers.ShareholdingFields do
  @moduledoc """
  Resolvers for object shareholding
  """

  use Helper.Pipe

  alias Gaia.Brokers
  alias Gaia.Registers

  def broker_name_short(%{broker_pid: broker_pid}, _, _) when is_binary(broker_pid) do
    broker_pid
    |> Brokers.get_broker_by_pid()
    |> Map.get(:name_short)
    |> {:ok, __}
  end

  def broker_name_short(_, _, _), do: {:ok, nil}

  def holder_id_masked(%{holder_id: holder_id}, _, _) when is_binary(holder_id),
    do: {:ok, "*******#{String.slice(holder_id, -4..-1)}"}

  def holder_id_masked(_, _, _), do: {:ok, nil}

  def latest_share_movement(%{id: shareholding_id}, _, _) do
    shareholding = Registers.get_latest_share_movement(shareholding_id)

    {:ok, shareholding}
  end

  def share_count_rank(%{id: shareholding_id, company_profile_id: company_profile_id}, _, _) do
    rank = Registers.get_shareholding_share_count_rank(shareholding_id, company_profile_id)

    {:ok, rank}
  end

  def sophisticated_investor_status(
        %{placement_demand_shareholding: %Gaia.Raises.PlacementDemands.Shareholding{} = p_d_s},
        _,
        _
      ) do
    # TODO: connect investor hub users and shareholdings
    # to show :unverified and :verified sophs as well as :potential
    if p_d_s.holding_size_threshold_met ||
         p_d_s.property_value_threshold_met ||
         p_d_s.shares_moved_through_wholesale_broker ||
         p_d_s.single_trade_volume_threshold_met ||
         p_d_s.soph_in_fresh_equities do
      {:ok, :potential}
    else
      {:ok, nil}
    end
  end

  def sophisticated_investor_status(_, _, _), do: {:ok, nil}

  def has_participated_in_spp(%{id: shareholding_id, company_profile_id: company_profile_id}, _, _) do
    participated = Registers.has_shareholding_participated_in_spp(shareholding_id, company_profile_id)

    {:ok, participated}
  end

  def has_participated_in_placement(
        %{past_placement_participants: [%Gaia.Raises.PastPlacements.Participant{} | _]},
        _,
        _
      ),
      do: {:ok, true}

  def has_participated_in_placement(%{past_placement_participants: []}, _, _), do: {:ok, false}

  def has_participated_in_placement(_, _, _), do: {:ok, nil}

  def has_email_recipient(%{id: shareholding_id}, %{email_id: email_id}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile_id: company_profile_id}}
      }) do
    args = %{email_id: email_id, company_profile_id: company_profile_id}

    Absinthe.Resolution.Helpers.batch(
      {Gaia.Comms, :batch_get_shareholder_email_recipients, args},
      shareholding_id,
      fn batch_results ->
        batch_results |> Enum.any?(&(&1.shareholder_id === shareholding_id)) |> {:ok, __}
      end
    )
  end
end
