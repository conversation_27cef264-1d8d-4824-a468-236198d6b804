defmodule AthenaWeb.Resolvers.Registers.Total do
  @moduledoc """
    Shareholdings Field Resolvers
  """

  import Ecto.Query, warn: false

  alias Gaia.Registers
  alias Gaia.Repo

  def total(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    length =
      args
      |> Map.get(:options, %{})
      |> Registers.shareholdings_query_by_company_profile_id(id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end

  def total(_parents, _args, _context), do: {:error, "It looks like you don't have permission to do this."}
end
