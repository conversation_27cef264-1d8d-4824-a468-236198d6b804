defmodule AthenaWeb.Resolvers.Registers.CampaignChannelShareholdersByActivityType do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  def resolve(
        _,
        %{
          channel: channel,
          shareholder_activity_type: shareholder_activity_type,
          start_date: start_date,
          end_date: end_date
        },
        %{
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{id: company_profile_id}
            }
          }
        }
      ) do
    %{
      channel: channel,
      company_profile_id: company_profile_id,
      shareholder_activity_type: shareholder_activity_type,
      start_date: start_date,
      end_date: end_date
    }
    |> Gaia.Registers.get_shareholdings_for_campaign_channel_by_activity_type()
    |> case do
      [_ | _] = shareholdings ->
        {:ok, shareholdings}

      [] ->
        {:ok, []}

      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Unable to get shareholdings for campaign channel by activity type"),
           error: error
         }}
    end
  end
end
