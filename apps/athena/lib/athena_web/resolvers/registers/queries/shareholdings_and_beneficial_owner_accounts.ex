defmodule AthenaWeb.Resolvers.Registers.ShareholdingsAndBeneficialOwnerAccounts do
  @moduledoc """
  Registers Shareholdings and Beneficial Owner Accounts Query Resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.RegistersAndOwners
  alias Gaia.Repo

  def cursor(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    options = Map.get(args, :options, %{})

    connection_result =
      options
      |> RegistersAndOwners.shareholdings_and_beneficial_owner_accounts_query_by_company_profile_id(id)
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        updated_edges =
          Enum.map(connection.edges, fn %{node: node} = edge ->
            updated_node =
              case Map.get(node, :contact) do
                %{} = contact ->
                  atomized_contact =
                    for {k, v} <- contact, into: %{} do
                      {String.to_existing_atom(k), v}
                    end

                  %{node | contact: atomized_contact}

                _ ->
                  node
              end

            %{edge | node: updated_node}
          end)

        {:ok,
         %{
           edges: updated_edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  def total(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    length =
      args
      |> Map.get(:options, %{})
      |> RegistersAndOwners.shareholdings_and_beneficial_owner_accounts_query_by_company_profile_id(id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end
end
