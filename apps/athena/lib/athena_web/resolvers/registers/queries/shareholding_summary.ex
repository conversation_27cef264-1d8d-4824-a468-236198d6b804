defmodule AthenaWeb.Resolvers.Registers.ShareholdingSummary do
  @moduledoc """
  Registers ShareholdingSummary Query Resolvers
  """

  use Helper.Pipe

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Markets.Ticker
  alias Gaia.Registers
  alias Gaia.Repo

  def resolve(
        _,
        %{
          id: shareholding_id,
          start_date: %Date{} = start_date,
          end_date: %Date{} = end_date,
          movement_sort_order: sort_order
        },
        %{
          context: %{
            current_company_profile_user: %ProfileUser{
              profile: %Profile{
                id: company_profile_id,
                ticker: %Ticker{listing_key: listing_key, market_key: market_key} = ticker,
                timezone: timezone
              }
            }
          }
        }
      ) do
    announcements =
      %{
        filters: [
          %{key: "listing_key", value: String.upcase(listing_key)},
          %{key: "market_key", value: String.upcase("#{market_key}")},
          %{
            key: "posted_at_greater_than",
            value:
              start_date
              |> Timex.to_datetime(timezone)
              |> Timex.to_naive_datetime()
          },
          %{
            key: "posted_at_less_than",
            value:
              end_date
              |> Timex.to_datetime(timezone)
              |> Timex.end_of_day()
              |> Timex.to_naive_datetime()
          }
        ],
        orders: [
          %{key: "posted_at", value: "asc"},
          %{key: "id", value: "asc"}
        ]
      }
      |> Interactions.media_announcements_query()
      |> Repo.all()

    daily_holdings =
      Registers.get_daily_holdings_by_shareholding_id(
        shareholding_id,
        company_profile_id,
        start_date,
        end_date
      )

    share_movements =
      Registers.get_share_movements_by_shareholding_id(
        shareholding_id,
        company_profile_id,
        start_date,
        end_date,
        movement_sort_order: sort_order
      )

    beneficial_owner_holdings =
      Gaia.BeneficialOwners.beneficial_owner_latest_holdings_per_account_by_shareholding_id(
        shareholding_id,
        company_profile_id
      )

    {:ok,
     %{
       id: "#{shareholding_id}-#{start_date}-#{end_date}",
       announcements: announcements,
       daily_holdings: daily_holdings,
       share_movements: share_movements,
       timeseries: get_timeseries(ticker, start_date, end_date),
       beneficial_owner_holdings: beneficial_owner_holdings
     }}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end

  defp get_timeseries(%Ticker{} = ticker, %Date{} = start_date, %Date{} = end_date) do
    case Gaia.MarketData.get_timeseries(ticker, start_date, end_date) do
      {:ok, ts} -> Gaia.MarketData.YahooFinance.combine_refinitiv_timeseries_with_chix(ts, ticker, start_date, end_date)
      _ -> []
    end
  end
end
