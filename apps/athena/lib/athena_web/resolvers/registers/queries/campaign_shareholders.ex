defmodule AthenaWeb.Resolvers.Registers.CampaignChannelShareholders do
  @moduledoc false

  def resolve(_, %{channel: channel}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }),
      do:
        {:ok,
         Gaia.Registers.get_shareholdings_for_campaign_channel(%{
           channel: channel,
           company_profile_id: company_profile_id
         })}
end
