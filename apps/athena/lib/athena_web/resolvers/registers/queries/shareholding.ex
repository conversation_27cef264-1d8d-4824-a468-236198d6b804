defmodule AthenaWeb.Resolvers.Registers.Shareholding do
  @moduledoc """
  Registers Shareholding Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Registers
  alias Gaia.Repo

  def resolve(_, %{id: shareholding_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    %{id: shareholding_id, company_profile_id: company_profile_id}
    |> Registers.get_shareholding_by()
    |> Repo.preload([:placement_demand_shareholding, :past_placement_participants])
    |> {:ok, __}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
