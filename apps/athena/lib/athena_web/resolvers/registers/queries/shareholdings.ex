defmodule AthenaWeb.Resolvers.Registers.Shareholdings do
  @moduledoc """
  Registers Shareholdings Query Resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Registers
  alias Gaia.Repo

  def cursor(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    options = Map.get(args, :options, %{})

    connection_result =
      options
      |> Registers.shareholdings_query_by_company_profile_id(id)
      |> Connection.from_query(&get_with_preload(&1, options), args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  defp get_with_preload(query, options) do
    preload_placements =
      options
      |> Map.get(:filters, %{})
      |> Enum.any?(fn filter -> filter[:key] == "traits" && filter[:value] == "placement" end)

    if preload_placements do
      query |> Repo.all() |> Repo.preload(:past_placement_participants)
    else
      Repo.all(query)
    end
  end
end
