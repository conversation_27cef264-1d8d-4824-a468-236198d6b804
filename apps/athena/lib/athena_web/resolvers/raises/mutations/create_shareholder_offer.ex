defmodule AthenaWeb.Resolvers.Raises.CreateShareholderOffer do
  @moduledoc """
  CreateShareholderOffer Resolver
  """

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer

  def resolve(_, %{shareholder_offer: shareholder_offer_input}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: company_profile_id},
            user: %User{id: company_user_id}
          }
        }
      }) do
    shareholder_offer_input
    |> Map.merge(%{
      company_profile_id: company_profile_id,
      last_edited_by_user_id: company_user_id
    })
    |> Raises.create_shareholder_offer()
    |> case do
      {:ok, %ShareholderOffer{} = created_shareholder_offer} ->
        {:ok, created_shareholder_offer}

      _error ->
        {:error, "Could not create shareholder offer"}
    end
  end
end
