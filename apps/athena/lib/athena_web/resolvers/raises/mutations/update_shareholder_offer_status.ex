defmodule AthenaWeb.Resolvers.Raises.UpdateShareholderOfferStatus do
  @moduledoc """
  UpdateShareholderOfferStatus Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer

  @gettext_context "Athena - UpdateShareholderOfferStatus mutation"

  def resolve(_, %{shareholder_offer_id: shareholder_offer_id} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: company_user_id}
          }
        }
      }) do
    with %ShareholderOffer{
           company_profile_id: company_profile_id,
           published_at: current_published_at
         } = shareholder_offer <-
           Raises.get_shareholder_offer(shareholder_offer_id),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         new_published_at = args |> Map.get(:is_live) |> get_published_at(current_published_at),
         :ok <- maybe_cancel_scheduled_shareholder_offer(shareholder_offer, args),
         :ok <- maybe_schedule_shareholder_offer(shareholder_offer, args),
         {:ok, %ShareholderOffer{} = updated_shareholder_offer} <-
           args
           |> Map.delete(:shareholder_offer_id)
           |> Map.merge(%{
             last_edited_by_user_id: company_user_id,
             published_at: new_published_at
           })
           |> Raises.update_shareholder_offer_status(shareholder_offer) do
      {:ok, updated_shareholder_offer}
    else
      {:check_ownership, false} ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not update shareholder offer status"
             )
         }}
    end
  end

  def maybe_cancel_scheduled_shareholder_offer(%ShareholderOffer{id: shareholder_offer_id, scheduled_at: scheduled_at}, %{
        is_live: false
      })
      when not is_nil(scheduled_at) do
    Raises.cancel_scheduled_shareholder_offer(shareholder_offer_id)
  end

  def maybe_cancel_scheduled_shareholder_offer(_shareholder_offer, _args), do: :ok

  def maybe_schedule_shareholder_offer(_shareholder_offer, %{is_live: false}) do
    :ok
  end

  def maybe_schedule_shareholder_offer(shareholder_offer, args) do
    args
    |> Map.get(:scheduled_at)
    |> Raises.maybe_schedule_shareholder_offer(shareholder_offer)
  end

  defp get_published_at(true, current_published_at) when is_nil(current_published_at) do
    NaiveDateTime.utc_now(:second)
  end

  defp get_published_at(_, current_published_at) do
    current_published_at
  end
end
