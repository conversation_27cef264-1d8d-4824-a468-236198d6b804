defmodule AthenaWeb.Resolvers.Raises.Mutations.ValidatePastPlacementParticipant do
  @moduledoc """
  ValidatePastPlacementParticipant Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Raises.PastPlacement
  alias Gaia.Raises.PastPlacements
  alias PastPlacements.Participant

  @gettext_context "Company user validate past placement participant mutation"

  def resolve(_, %{participant_id: participant_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with %Participant{past_placement: %PastPlacement{company_profile_id: company_profile_id}} = existing_participant <-
           participant_id
           |> PastPlacements.get_invalid_participant_by_id()
           |> Gaia.Repo.preload(:past_placement),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %Participant{} = updated_participant} <-
           PastPlacements.update_participant(existing_participant, %{invalidated: false}) do
      {:ok, updated_participant}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not validate past placement participant"
             )
         }}
    end
  end
end
