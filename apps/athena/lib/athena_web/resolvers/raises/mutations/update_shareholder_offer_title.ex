defmodule AthenaWeb.Resolvers.Raises.UpdateShareholderOfferTitle do
  @moduledoc """
  UpdateShareholderOfferTitle Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer

  @gettext_context "Athena - UpdateShareholderOfferTitle mutation"

  def resolve(_, %{shareholder_offer_id: shareholder_offer_id, title: title}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: company_user_id}
          }
        }
      }) do
    with %ShareholderOffer{
           company_profile_id: company_profile_id
         } = shareholder_offer <-
           Raises.get_shareholder_offer(shareholder_offer_id),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %ShareholderOffer{} = updated_shareholder_offer} <-
           Raises.update_shareholder_offer(shareholder_offer, %{title: title, last_edited_by_user_id: company_user_id}) do
      {:ok, updated_shareholder_offer}
    else
      {:check_ownership, false} ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not update shareholder offer title"
             )
         }}
    end
  end
end
