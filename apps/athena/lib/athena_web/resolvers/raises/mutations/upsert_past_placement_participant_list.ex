defmodule AthenaWeb.Resolvers.Raises.UpsertPastPlacementParticipantList do
  @moduledoc """
  UpsertPastPlacementParticipantList Resolver

  Expecting the number of participants of a past placement to be well under 100,
  so handling uploaded file content in one go.
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.PastPlacements
  alias Gaia.Repo

  @gettext_context "Upsert past placement participant list mutation resolver"

  def resolve(_, %{past_placement_id: past_placement_id, participant_list: participant_list}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: pp_company_profile_id} <-
           Gaia.Raises.get_past_placement(past_placement_id),
         {:check_ownership, true} <-
           {:check_ownership, pp_company_profile_id == company_profile_id},
         :ok <- validate_file_format(participant_list),
         [%{} | _] = uploaded_participants <- parse_file(participant_list),
         existing_participants = PastPlacements.list_existing_participants_by_past_placement_id(past_placement_id),
         {:ok, inserted_participants} <-
           insert_missing_participants(%{
             company_profile_id: company_profile_id,
             existing_participants: existing_participants,
             past_placement_id: past_placement_id,
             uploaded_participants: uploaded_participants
           }),
         {:ok, invalidated_participants} <-
           invalidate_incorrect_participants(%{
             existing_participants: existing_participants,
             uploaded_participants: uploaded_participants
           }) do
      {:ok, inserted_participants ++ invalidated_participants}
    else
      [] ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "No participant found in the uploaded list. Please re-upload or contact InvestorHub for support."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately the participant list uploaded was not processed successfully. Please try again later or contact InvestorHub for support."
             )
         }}
    end
  end

  def validate_file_format(%Plug.Upload{
        content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        path: path
      }) do
    case XlsxReader.open(path) do
      {:ok,
       %XlsxReader.Package{
         workbook: %XlsxReader.Workbook{
           sheets: [%XlsxReader.Sheet{name: "Past placement participant list"}]
         }
       }} ->
        :ok

      _ ->
        :error
    end
  end

  def validate_file_format(_), do: :error

  def parse_file(%Plug.Upload{
        content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        path: path
      }) do
    {:ok, package} = XlsxReader.open(path)
    {:ok, rows} = XlsxReader.sheet(package, "Past placement participant list")

    header_row_index = Enum.find_index(rows, &Enum.member?(&1, "HIN/SRN"))
    header_row = Enum.at(rows, header_row_index)

    rows
    |> Enum.drop(1)
    |> Enum.map(&format_row(&1))
    |> Enum.map(&map_row_to_participant(&1, header_row))
  end

  def format_row(row) do
    Enum.map(row, fn x ->
      cond do
        x == :expect_chars -> nil
        x == "" -> nil
        is_binary(x) -> String.trim(x)
        true -> x
      end
    end)
  end

  def map_row_to_participant(row, header_row) do
    Map.new(["HIN/SRN", "Shares allocated", "Settlement date"], fn header ->
      column_index = Enum.find_index(header_row, &(&1 == header))
      cell_value = Enum.at(row, column_index)

      case header do
        "HIN/SRN" -> {:holder_id, cell_value}
        "Shares allocated" -> {:allocation_shares, get_shares_allocated(cell_value)}
        "Settlement date" -> {:settled_at, cell_value |> Timex.parse!("{0D}/{0M}/{YYYY}") |> Timex.to_date()}
      end
    end)
  end

  def get_shares_allocated(shares_allocated) when is_binary(shares_allocated), do: string_to_integer(shares_allocated)

  def get_shares_allocated(shares_allocated) when is_float(shares_allocated), do: round(shares_allocated)

  def get_shares_allocated(shares_allocated) when is_integer(shares_allocated), do: shares_allocated

  def get_shares_allocated(_), do: 0

  def string_to_integer(string) do
    String.to_integer(string)
  rescue
    _ ->
      float_string_to_integer(string)
  end

  def float_string_to_integer(string) do
    string
    |> String.to_float()
    |> round()
  rescue
    _ ->
      0
  end

  def insert_missing_participants(%{
        company_profile_id: company_profile_id,
        existing_participants: existing_participants,
        past_placement_id: past_placement_id,
        uploaded_participants: uploaded_participants
      }) do
    movement_types = list_movement_types(company_profile_id)

    # Each participant now has only holder_id, allocation_shares, settled_at
    missing_participants = list_missing_participants(existing_participants, uploaded_participants)

    missing_participants_holder_ids =
      PastPlacements.list_missing_participants_holder_ids(%{
        company_profile_id: company_profile_id,
        missing_participants: missing_participants
      })

    settled_at = existing_participants |> Enum.at(0) |> Map.get(:settled_at)

    settlement_shares_query =
      PastPlacements.build_missing_participants_settlement_shares_query(%{
        holder_ids: missing_participants_holder_ids,
        movement_types: movement_types,
        settled_at: settled_at,
        company_profile_id: company_profile_id
      })

    one_week_after_settlement_shares_query =
      PastPlacements.build_participants_post_settlement_shares_query(%{
        settled_at: settled_at,
        settlement_shares_query: settlement_shares_query,
        time_period: "1 week",
        company_profile_id: company_profile_id
      })

    one_month_after_settlement_shares_query =
      PastPlacements.build_participants_post_settlement_shares_query(%{
        settled_at: settled_at,
        settlement_shares_query: settlement_shares_query,
        time_period: "1 month",
        company_profile_id: company_profile_id
      })

    three_months_after_settlement_shares_query =
      PastPlacements.build_participants_post_settlement_shares_query(%{
        settled_at: settled_at,
        settlement_shares_query: settlement_shares_query,
        time_period: "3 months",
        company_profile_id: company_profile_id
      })

    Gaia.Registers.Shareholding
    |> join(
      :inner,
      [shareholding],
      settlement in subquery(settlement_shares_query),
      on: shareholding.id == settlement.shareholding_id
    )
    |> join(
      :left,
      [shareholding, settlement],
      one_week_after_settlement in subquery(one_week_after_settlement_shares_query),
      on: shareholding.id == one_week_after_settlement.shareholding_id
    )
    |> join(
      :left,
      [shareholding, settlement, _],
      one_month_after_settlement in subquery(one_month_after_settlement_shares_query),
      on: shareholding.id == one_month_after_settlement.shareholding_id
    )
    |> join(
      :left,
      [shareholding, settlement, ...],
      three_months_after_settlement in subquery(three_months_after_settlement_shares_query),
      on: shareholding.id == three_months_after_settlement.shareholding_id
    )
    |> join(
      :left,
      [shareholding, settlement, ...],
      past_placement in Gaia.Raises.PastPlacement,
      on: past_placement.company_profile_id == shareholding.company_profile_id
    )
    |> where([..., past_placement], past_placement.id == ^past_placement_id)
    |> select(
      [
        shareholding,
        settlement,
        one_week_after_settlement,
        one_month_after_settlement,
        three_months_after_settlement,
        past_placement
      ],
      %{
        company_profile_id: past_placement.company_profile_id,
        holder_id: shareholding.holder_id,
        settlement_shares: settlement.shares,
        past_placement_id: past_placement.id,
        shareholding_id: shareholding.id,
        tranche_type: past_placement.tranche,
        one_week_after_settlement_shares: one_week_after_settlement.shares,
        one_month_after_settlement_shares: one_month_after_settlement.shares,
        three_months_after_settlement_shares: three_months_after_settlement.shares
      }
    )
    |> Repo.all(timeout: 300_000)
    |> Enum.map(
      &(&1
        |> Map.put(:allocation_shares, get_allocation_shares(&1, missing_participants))
        |> Map.delete(:holder_id))
    )
    |> PastPlacements.upsert_participants()
    |> case do
      {:ok, %{insert_all: {_count, participants}}} -> {:ok, participants}
      _ -> {:error, "Could not insert missing participants"}
    end
  end

  def get_allocation_shares(%{holder_id: holder_id}, missing_participants) do
    missing_participants
    |> Enum.find(
      %{},
      &(String.at(&1.holder_id, 0) == String.at(holder_id, 0) and
          String.slice(&1.holder_id, -5..-1) == String.slice(holder_id, -5..-1))
    )
    |> Map.get(:allocation_shares, 0)
  end

  def list_movement_types(company_profile_id) do
    company_profile_id
    |> Companies.get_profile()
    |> Repo.preload(:ticker)
    |> Raises.get_placement_movement_types()
  end

  def list_missing_participants(existing_participants, uploaded_participants) do
    case existing_participants do
      [] -> uploaded_participants
      _ -> Enum.filter(uploaded_participants, &(not is_existing_participant(&1, existing_participants)))
    end
  end

  def invalidate_incorrect_participants(%{
        existing_participants: [_ | _] = existing_participants,
        uploaded_participants: uploaded_participants
      }) do
    incorrect_participant_ids =
      existing_participants
      |> Enum.filter(&is_incorrect_participant(&1, uploaded_participants))
      |> Enum.map(& &1.participant_id)

    incorrect_participant_ids
    |> PastPlacements.invalidate_participants()
    |> case do
      {:ok, %{update_all: {_count, participants}}} -> {:ok, participants}
      _ -> {:error, "Could not invalidate incorrect participants"}
    end
  end

  # No existing participants to invalidate
  def invalidate_incorrect_participants(%{existing_participants: []}), do: {:ok, []}

  def is_incorrect_participant(%{holder_id: holder_id, settled_at: settled_at}, uploaded_participants) do
    not Enum.any?(
      uploaded_participants,
      &(&1.settled_at == settled_at and
          String.at(&1.holder_id, 0) == String.at(holder_id, 0) and
          String.slice(&1.holder_id, -5..-1) == String.slice(holder_id, -5..-1))
    )
  end

  def is_existing_participant(
        %{holder_id: holder_id, settled_at: settled_at} = _uploaded_participant,
        existing_participants
      ) do
    Enum.any?(
      existing_participants,
      &(&1.settled_at == settled_at and
          String.at(&1.holder_id, 0) == String.at(holder_id, 0) and
          String.slice(&1.holder_id, -5..-1) == String.slice(holder_id, -5..-1))
    )
  end
end
