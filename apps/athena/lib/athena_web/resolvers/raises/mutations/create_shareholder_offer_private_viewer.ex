defmodule AthenaWeb.Resolvers.Raises.CreateShareholderOfferPrivateViewer do
  @moduledoc """
  Add a private viewer to a shareholder offer
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Raises.ShareholderOffers.PrivateViewer

  @gettext_context "CreateShareholderOfferPrivateViewer mutation resolver"

  def resolve(_, %{email: email, shareholder_offer_id: shareholder_offer_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with {:ok, %ShareholderOffer{} = shareholder_offer} <- get_shareholder_offer(shareholder_offer_id),
         :ok <- check_permission(current_company_profile_id, shareholder_offer),
         {:ok, %PrivateViewer{} = private_viewer} <- create_private_viewer(email, shareholder_offer) do
      {:ok, private_viewer}
    else
      {:error, msg} = error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: msg
         }}
    end
  end

  defp create_private_viewer(email, %ShareholderOffer{} = shareholder_offer) do
    case Raises.create_private_viewer(%{
           email: email,
           shareholder_offer_id: shareholder_offer.id
         }) do
      {:ok, private_viewer} ->
        {:ok, private_viewer}

      {:error, changeset} ->
        {:error, get_error_msg(changeset)}
    end
  end

  defp get_shareholder_offer(shareholder_offer_id) do
    case Raises.get_shareholder_offer(shareholder_offer_id) do
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Shareholder offer not found"
         )}

      shareholder_offer ->
        {:ok, shareholder_offer}
    end
  end

  defp check_permission(current_company_profile_id, %ShareholderOffer{} = shareholder_offer) do
    if shareholder_offer.company_profile_id == current_company_profile_id do
      :ok
    else
      {:error,
       dpgettext(
         "errors",
         @gettext_context,
         "You do not have permission to add a private viewer to this shareholder offer"
       )}
    end
  end

  defp get_error_msg(%{errors: errors}) do
    Enum.find_value(errors, fn
      {:email, _} ->
        dpgettext(
          "errors",
          @gettext_context,
          "Email is invalid"
        )

      {:shareholder_offer_id, {"has already been taken", _}} ->
        dpgettext(
          "errors",
          @gettext_context,
          "Email has already been added for this offer"
        )

      _ ->
        dpgettext(
          "errors",
          @gettext_context,
          "Private viewer could not be created"
        )
    end)
  end
end
