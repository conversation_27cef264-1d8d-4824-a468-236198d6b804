defmodule AthenaWeb.Resolvers.Raises.GeneratePastPlacementParticipantListSignedUrl do
  @moduledoc false

  def resolve(_, %{mime_type: mime_type, past_placement_id: past_placement_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: company_profile_id} = past_placement <-
           Gaia.Raises.get_past_placement(past_placement_id),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{company_profile_id}/raises/past_placements/#{past_placement_id}/participants/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type],
             verb: "PUT"
           ),
         {:ok, %URI{host: host, path: path, scheme: scheme}} <- URI.new(url),
         {:ok, _} <-
           Gaia.Raises.update_past_placement(past_placement, %{
             participant_list_url: "#{scheme}://#{host}#{path}"
           }) do
      {:ok, url}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Unable to generate signed url."
         }}
    end
  end
end
