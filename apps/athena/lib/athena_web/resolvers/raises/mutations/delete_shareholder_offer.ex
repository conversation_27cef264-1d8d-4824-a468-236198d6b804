defmodule AthenaWeb.Resolvers.Raises.DeleteShareholderOffer do
  @moduledoc """
  DeleteShareholderOffer Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer

  @gettext_context "Athena - DeleteShareholderOffer mutation"

  def resolve(_, %{shareholder_offer_id: shareholder_offer_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %ShareholderOffer{company_profile_id: company_profile_id} = shareholder_offer <-
           Raises.get_shareholder_offer(shareholder_offer_id),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %ShareholderOffer{} = deleted_shareholder_offer} <-
           Raises.delete_shareholder_offer(shareholder_offer) do
      {:ok, deleted_shareholder_offer}
    else
      {:check_ownership, false} ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not delete shareholder offer"
             )
         }}
    end
  end
end
