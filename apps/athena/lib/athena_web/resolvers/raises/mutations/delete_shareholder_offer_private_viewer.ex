defmodule AthenaWeb.Resolvers.Raises.DeleteShareholderOfferPrivateViewer do
  @moduledoc """
  Add a private viewer to a shareholder offer
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Raises.ShareholderOffers.PrivateViewer

  @gettext_context "DeleteShareholderOfferPrivateViewer mutation resolver"

  def resolve(_, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with {:ok, %PrivateViewer{} = private_viewer} <- get_private_viewer(id),
         :ok <- check_permission(current_company_profile_id, private_viewer.shareholder_offer),
         {:ok, _} <- delete_private_viewer(private_viewer) do
      {:ok, true}
    else
      {:error, msg} = error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: msg
         }}
    end
  end

  defp get_private_viewer(id) do
    case Raises.get_private_viewer(id) do
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Private viewer not found"
         )}

      private_viewer ->
        {:ok, Gaia.Repo.preload(private_viewer, [:shareholder_offer])}
    end
  end

  defp check_permission(current_company_profile_id, %ShareholderOffer{} = shareholder_offer) do
    if shareholder_offer.company_profile_id == current_company_profile_id do
      :ok
    else
      {:error,
       dpgettext(
         "errors",
         @gettext_context,
         "You do not have permission to add a private viewer to this shareholder offer"
       )}
    end
  end

  defp delete_private_viewer(%PrivateViewer{} = private_viewer) do
    case Raises.delete_private_viewer(private_viewer) do
      {:ok, deleted_private_viewer} ->
        {:ok, deleted_private_viewer}

      {:error, _changeset} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Private viewer could not be deleted."
         )}
    end
  end
end
