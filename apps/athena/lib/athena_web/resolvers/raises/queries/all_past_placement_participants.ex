defmodule AthenaWeb.Resolvers.Raises.Queries.AllPastPlacementParticipants do
  @moduledoc """
  AllPastPlacementParticipants Resolvers
  """
  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Raises.PastPlacements
  alias Gaia.Repo

  @gettext_context "All past placement participants query"

  def resolve(_parents, %{past_placement_id: past_placement_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_profile_id}
          }
        }
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: profile_id} <-
           Gaia.Raises.get_past_placement(past_placement_id),
         true <- profile_id == current_profile_id do
      options = Map.get(args, :options, %{filters: [], orders: []})

      {:ok,
       options
       |> PastPlacements.participants_query_by_past_placement_id(past_placement_id)
       |> Repo.all(with_invalidated: check_is_with_invalidated(options))}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
             )
         }}
    end
  end

  defp check_is_with_invalidated(%{filters: filters}) when filters != [] do
    filters
    |> Enum.filter(&(&1.key == "invalidated"))
    |> Enum.at(0)
    |> case do
      nil -> false
      filter -> Map.get(filter, :value, false)
    end
  end

  defp check_is_with_invalidated(_), do: false
end
