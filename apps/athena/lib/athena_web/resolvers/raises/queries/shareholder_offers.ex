defmodule AthenaWeb.Resolvers.Raises.ShareholderOffers do
  @moduledoc """
    ShareholderOffers query resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Repo

  def cursor(_parents, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    options = Map.get(args, :options, %{filters: [], orders: []})

    connection_result =
      options
      |> Raises.shareholder_offers_query_by_company_profile_id(current_company_profile_id)
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
