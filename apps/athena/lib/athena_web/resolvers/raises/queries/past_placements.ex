defmodule AthenaWeb.Resolvers.Raises.PastPlacements do
  @moduledoc """
  Lists past placements of current company.
  """

  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }),
      do: {:ok, Gaia.Raises.list_past_placements_by_company_profile_id(company_profile_id)}
end
