defmodule AthenaWeb.Resolvers.Raises.SppHistorical do
  @moduledoc """
    Historical SPP fetcher
    Fetches data from raises_spp_historical
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker
  alias Gaia.Raises
  alias Gaia.Registers
  alias Gaia.Tracking

  def resolve(_parent, %{sector_ticker: sector_ticker, year: year}, _context) do
    {:ok, Raises.list_filtered_raises_spp_historical(%{ann_type: "close", sector_ticker: sector_ticker, year: year})}
  end

  def resolve_current_company(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{ticker: %Ticker{listing_key: listing_key}}}
        }
      }) do
    {:ok, Raises.get_raises_spp_historical_close_by_ticker(listing_key)}
  end

  def regress(_parent, %{sector_ticker: sector_ticker, year: year}, _context) do
    {:ok, Raises.regress_filtered_raises_spp_historical(%{ann_type: "close", sector_ticker: sector_ticker, year: year})}
  end

  def get_raise_spp_shareholder_stats(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok, Registers.get_raise_spp_shareholder_stats(company_profile_id)}
  end

  def get_raise_spp_reachability_stats(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok, Tracking.get_raise_spp_reachability_stats(company_profile_id)}
  end

  def belongs_to_current_company(%{company_profile_id: company_profile_id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_user_company_profile_id}}}
      }) do
    {:ok, company_profile_id == current_user_company_profile_id}
  end
end
