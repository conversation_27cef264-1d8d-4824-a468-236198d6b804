defmodule AthenaWeb.Resolvers.Raises.ShareholderOffer do
  @moduledoc """
  ShareholderOffer Query
  """
  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Raises.ShareholderOffers.PrivateViewer

  @gettext_context "Shareholder offer query"

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_profile_id}}}
      }) do
    id
    |> Raises.get_shareholder_offer()
    |> case do
      %ShareholderOffer{company_profile_id: profile_id} = shareholder_offer
      when profile_id == current_profile_id ->
        {:ok, shareholder_offer}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately the shareholder offer could not be found. Please try again later or contact InvestorHub for support."
             )
         }}
    end
  end

  def resolve_private_viewers(%ShareholderOffer{id: offer_id}, _args, _context) do
    from(pv in PrivateViewer,
      where: pv.shareholder_offer_id == ^offer_id,
      order_by: [asc: pv.id]
    )
    |> Gaia.Repo.all()
    |> case do
      private_viewers when is_list(private_viewers) ->
        {:ok, private_viewers}

      error ->
        {:error, error}
    end
  end
end
