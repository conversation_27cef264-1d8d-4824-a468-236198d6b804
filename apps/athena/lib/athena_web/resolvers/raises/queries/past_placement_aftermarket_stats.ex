defmodule AthenaWeb.Resolvers.Raises.PastPlacementAftermarketStats do
  @moduledoc """
  PastPlacementAftermarketStats Resolvers
  """
  use Gettext, backend: AthenaWeb.Gettext

  @gettext_context "Past placement aftermarket stats"

  def resolve(_parents, %{time_range: time_range, past_placement_id: past_placement_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_profile_id}
          }
        }
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: profile_id} <-
           Gaia.Raises.get_past_placement(past_placement_id),
         true <- profile_id == current_profile_id do
      {:ok,
       Gaia.Raises.PastPlacements.get_past_placement_aftermarket_stats(%{
         time_range: time_range,
         past_placement_id: past_placement_id
       })}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
             )
         }}
    end
  end
end
