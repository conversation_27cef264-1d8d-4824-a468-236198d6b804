defmodule AthenaWeb.Resolvers.Raises.PastPlacement do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  @gettext_context "Past placement query"

  def resolve(_parent, %{id: id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_profile_id}
          }
        }
      }) do
    case Gaia.Raises.get_past_placement(id) do
      %Gaia.Raises.PastPlacement{company_profile_id: profile_id} = past_placement
      when profile_id == current_profile_id ->
        {:ok, past_placement}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately the past placement could not be found. Please try again later or contact InvestorHub for support."
             )
         }}
    end
  end
end
