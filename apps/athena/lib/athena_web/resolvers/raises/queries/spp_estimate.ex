defmodule AthenaWeb.Resolvers.Raises.SppEstimate do
  @moduledoc """
    Fetch pre-loaded SPP estimate
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok, Raises.get_spp_estimate_by_company_profile_id(company_profile_id)}
  end
end
