defmodule AthenaWeb.Resolvers.Raises.PastPlacementFields do
  @moduledoc false

  def shares_allocated(%Gaia.Raises.PastPlacement{id: id}, _query, _context) do
    Absinthe.Resolution.Helpers.batch(
      {Gaia.Raises, :batch_get_past_placement_shares_allocated},
      id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.id == id))
        |> case do
          nil -> {:ok, nil}
          %{shares_allocated: shares_allocated} -> {:ok, shares_allocated}
        end
      end
    )
  end

  def amount_raised_total(%Gaia.Raises.PastPlacement{amount_raised: amount_raised, tranche: :none}, _query, _context),
    do: {:ok, amount_raised}

  def amount_raised_total(%Gaia.Raises.PastPlacement{amount_raised: amount_raised} = past_placement, _query, _context) do
    past_placement
    |> Gaia.Repo.preload(:tranche_linked_past_placement)
    |> case do
      %Gaia.Raises.PastPlacement{
        tranche_linked_past_placement: %{amount_raised: tranche_amount_raised}
      } ->
        {:ok, (amount_raised || 0) + (tranche_amount_raised || 0)}

      _ ->
        {:ok, amount_raised}
    end
  end

  def tranche_two_settled_at(%Gaia.Raises.PastPlacement{tranche: :t1} = past_placement, _query, _context) do
    past_placement
    |> Gaia.Repo.preload(:tranche_linked_past_placement)
    |> case do
      %Gaia.Raises.PastPlacement{settled_at: tranche_two_settled_at} -> {:ok, tranche_two_settled_at}
      _ -> {:ok, nil}
    end
  end

  def tranche_two_settled_at(%Gaia.Raises.PastPlacement{}, _query, _context), do: {:ok, nil}

  def movement_since(
        %Gaia.Raises.PastPlacement{company_profile_id: company_profile_id, issue_price: issue_price},
        _query,
        _context
      )
      when is_number(issue_price) and issue_price > 0 do
    %{close: latest_share_price} =
      Gaia.Markets.get_latest_timeseries_non_adjusted_by_company_profile_id(company_profile_id)

    {:ok, latest_share_price / issue_price - 1}
  end

  def movement_since(_past_placement, _query, _context), do: {:ok, 0.0}
end
