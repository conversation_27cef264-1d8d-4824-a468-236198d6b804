defmodule AthenaWeb.Resolvers.Raises.Fields.ShareholderOffers do
  @moduledoc """
  Shareholder offers fields
  """

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Repo

  def total(_parent, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    options = Map.get(args, :options, %{filters: [], orders: []})

    length =
      options
      |> Raises.shareholder_offers_query_by_company_profile_id(current_company_profile_id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end
end
