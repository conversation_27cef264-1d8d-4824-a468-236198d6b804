defmodule AthenaWeb.Resolvers.Raises.Fields.PastPlacementParticipants do
  @moduledoc """
  Past Placement Participant Fields
  """

  import Ecto.Query, warn: false

  alias Gaia.Raises.PastPlacements
  alias Gaia.Repo

  def total(_parent, %{past_placement_id: past_placement_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: company_profile_id} <-
           Gaia.Raises.get_past_placement(past_placement_id),
         true <- company_profile_id == current_company_profile_id do
      options = Map.get(args, :options, %{filters: [], orders: []})

      length =
        options
        |> PastPlacements.participants_query_by_past_placement_id(past_placement_id)
        |> Repo.aggregate(:count, with_invalidated: check_is_with_invalidated(options))

      {:ok, length}
    else
      _ ->
        {:error, "You are unauthorised."}
    end
  end

  def has_invalidated_participants(_parent, %{past_placement_id: past_placement_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with %Gaia.Raises.PastPlacement{company_profile_id: company_profile_id} <-
           Gaia.Raises.get_past_placement(past_placement_id),
         true <- company_profile_id == current_company_profile_id do
      {
        :ok,
        PastPlacements.check_has_invalidated_participants_by_past_placement_id(past_placement_id)
      }
    else
      _ ->
        {:error, "You are unauthorised."}
    end
  end

  defp check_is_with_invalidated(%{filters: filters}) when filters != [] do
    filters
    |> Enum.filter(&(&1.key == "invalidated"))
    |> Enum.at(0)
    |> case do
      nil -> false
      filter -> Map.get(filter, :value, false)
    end
  end

  defp check_is_with_invalidated(_), do: false
end
