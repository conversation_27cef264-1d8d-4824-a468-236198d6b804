defmodule AthenaWeb.Resolvers.Raises.Fields.ShareholderOffer do
  @moduledoc """
  Shareholder offer fields
  """

  import Ecto.Query, warn: false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Tracking

  def total_view_count(%ShareholderOffer{id: id, published_at: published_at} = shareholder_offer, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_shareholder_offer_views(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_view_count(_, _, _), do: {:ok, 0}

  def total_view_count_from_hub_users(%ShareholderOffer{id: id, published_at: published_at} = shareholder_offer, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_shareholder_offer_views_from_hub_users(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_view_count_from_hub_users(_, _, _), do: {:ok, 0}

  def total_unique_visitors_count(%ShareholderOffer{id: id, published_at: published_at} = shareholder_offer, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_unique_shareholder_offer_views(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_unique_visitors_count(_, _, _), do: {:ok, 0}

  def total_unique_visitors_count_from_hub_users(
        %ShareholderOffer{id: id, published_at: published_at} = shareholder_offer,
        _args,
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}}
      )
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_unique_shareholder_offer_views_from_hub_users(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_unique_visitors_count_from_hub_users(_, _, _), do: {:ok, 0}

  def sign_ups_during_offer_period(%ShareholderOffer{published_at: published_at} = shareholder_offer, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_sign_ups_during_offer_period(%{
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def sign_ups_during_offer_period(_, _, _), do: {:ok, 0}

  def total_investor_presentation_downloads_count(
        %ShareholderOffer{id: id, published_at: published_at} = shareholder_offer,
        _args,
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}}
      )
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_shareholder_offer_investor_presentation_downloads(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_investor_presentation_downloads_count(_, _, _), do: {:ok, 0}

  def total_offer_booklet_downloads_count(
        %ShareholderOffer{id: id, published_at: published_at} = shareholder_offer,
        _args,
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}}
      )
      when not is_nil(published_at) do
    {:ok,
     Tracking.count_shareholder_offer_offer_booklet_downloads(%{
       id: id,
       published_at: published_at,
       finished_at: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def total_offer_booklet_downloads_count(_, _, _), do: {:ok, 0}

  def engagement(%ShareholderOffer{id: id, published_at: published_at} = shareholder_offer, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when not is_nil(published_at) do
    {:ok,
     Raises.get_shareholder_offer_engagement(%{
       id: id,
       start_date: published_at,
       end_date: get_finished_at(shareholder_offer),
       company_profile_id: current_company_profile_id
     })}
  end

  def engagement(_, _, _), do: {:ok, []}

  defp get_finished_at(%ShareholderOffer{is_live: true}) do
    NaiveDateTime.utc_now(:second)
  end

  defp get_finished_at(%ShareholderOffer{updated_at: updated_at}) do
    updated_at
  end
end
