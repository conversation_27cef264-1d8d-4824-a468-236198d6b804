defmodule AthenaWeb.Resolvers.Contacts.AmplifyInvestorFields do
  @moduledoc """
  Resolvers for object amplify_investor
  """

  use Helper.Pipe

  alias Gaia.Contacts
  alias Gaia.Repo

  def total(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    company_profile_id
    |> Contacts.amplify_investors_query(args)
    |> Repo.aggregate(:count)
    |> {:ok, __}
  end
end
