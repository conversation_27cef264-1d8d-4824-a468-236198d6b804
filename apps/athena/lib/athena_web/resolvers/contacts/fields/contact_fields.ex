defmodule AthenaWeb.Resolvers.Contacts.ContactFields do
  @moduledoc """
  Resolvers for object contact
  """

  use Helper.Pipe

  import Helper.Guard, only: [is_id?: 1]

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Investors.User
  alias Gaia.Repo

  def has_email_recipient(%{id: contact_id}, %{email_id: email_id}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile_id: company_profile_id}}
      })
      when is_id?(email_id) do
    args = %{email_id: email_id, company_profile_id: company_profile_id}

    Absinthe.Resolution.Helpers.batch(
      {Gaia.Comms, :batch_get_contact_email_recipients, args},
      contact_id,
      fn batch_results ->
        batch_results |> Enum.any?(&(&1.contact_id === contact_id)) |> {:ok, __}
      end
    )
  end

  def has_email_recipient(_parent, _args, _res), do: {:ok, false}

  def total(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = Map.get(args, :options, %{})

    company_profile_id
    |> Contacts.contacts_query(options)
    |> Repo.aggregate(:count)
    |> {:ok, __}
  end

  def total(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving total contacts"
     }}
  end

  def nominated_shareholder_status(%Contact{investor: %User{is_self_nominated_shareholder: true}}),
    do: {:ok, :nominated_shareholder}

  def nominated_shareholder_status(%Contact{is_nominated_shareholder: true}), do: {:ok, :nominated_shareholder}

  def nominated_shareholder_status(_), do: {:ok, :investor_lead}

  def shareholder_status(%Contact{investor: %Ecto.Association.NotLoaded{}} = contact, args, context) do
    shareholder_status(Gaia.Repo.preload(contact, :investor), args, context)
  end

  def shareholder_status(%Contact{} = contact, _, _) do
    contact
    |> Gaia.Repo.preload([:shareholdings])
    |> Map.get(:shareholdings, [])
    |> case do
      [] ->
        nominated_shareholder_status(contact)

      shareholdings ->
        shareholdings
        |> Enum.any?(&(&1.share_count > 0))
        |> case do
          true -> {:ok, :shareholder}
          false -> {:ok, :past_shareholder}
        end
    end
  end

  def contact_total_shareholding_rank(%Contact{id: id}, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    case Gaia.Contacts.get_contact_total_shareholding_rank(id, company_profile_id) do
      nil -> {:ok, 0}
      rank -> {:ok, rank}
    end
  end

  def email_engagement_status(%Contact{id: id}, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    case Gaia.Contacts.email_engagement_status_for_contact(id, company_profile_id) do
      nil -> {:ok, "inactive"}
      status -> {:ok, status}
    end
  end
end
