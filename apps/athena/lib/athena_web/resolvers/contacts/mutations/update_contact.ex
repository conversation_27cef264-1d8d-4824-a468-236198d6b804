defmodule AthenaWeb.Resolvers.Contacts.UpdateContact do
  @moduledoc """
  UpdateContact Mutation Resolvers
  """

  use Helper.Pipe

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact

  def resolve(_, %{id: contact_id, contact: contact_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{} = contact <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:ok, %Contact{} = updated_contact} <- Contacts.update_contact(contact, contact_input) do
      {:ok, updated_contact}
    else
      nil ->
        {:error, "Contact not found"}

      {:error,
       %Ecto.Changeset{
         changes: %{email: _},
         errors: [
           company_profile_id: {"has already been taken", _}
         ]
       }} ->
        {:error, "This email is already in use. Enter a unique email."}

      {:error,
       %Ecto.Changeset{
         changes: %{email: _},
         errors: [
           email: {_, [validation: :format]}
         ]
       }} ->
        {:error, "Invalid email address, please try again."}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update contact"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot update contact"}
  end
end
