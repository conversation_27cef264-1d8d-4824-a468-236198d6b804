defmodule AthenaWeb.Resolvers.Contacts.LinkContactWithInvestor do
  @moduledoc """
  LinkContactWithInvestor Mutation Resolvers
  """

  use Helper.Pipe

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Investors
  alias Investors.User

  def resolve(_, %{id: contact_id, investor_user_id: investor_user_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{} = contact <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:check_contact_no_existing_link, nil} <-
           {
             :check_contact_no_existing_link,
             Investors.get_user_by(%{contact_id: contact_id, company_profile_id: company_profile_id})
           },
         %User{contact_id: nil} = investor <-
           Investors.get_user_by(%{id: investor_user_id, company_profile_id: company_profile_id}),
         {:ok, %User{} = _updated_investor} <-
           Investors.update_user(investor, %{contact_id: contact_id}) do
      {:ok, contact}
    else
      nil ->
        {:error, "Contact or investor not found"}

      {:check_contact_no_existing_link, _} ->
        {:error, "Contact already linked to another investor. Please unlink contact before trying again"}

      %User{contact_id: _} ->
        {:error, "Investor already linked to another contact. Please choose another investor"}

      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Cannot link contact with investor"
         }}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot link contact with investor"}
  end
end
