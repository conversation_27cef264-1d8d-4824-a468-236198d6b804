defmodule AthenaWeb.Resolvers.Contacts.CreateTag do
  @moduledoc """
  CreateTag Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Contacts.Tag

  def resolve(_, %{contact_id: contact_id, tag: tag_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{} <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:ok, %Tag{} = created_tag} <-
           %{contact_id: contact_id, company_profile_id: company_profile_id}
           |> Enum.into(tag_input)
           |> Contacts.create_tag() do
      {:ok, created_tag}
    else
      nil ->
        {:error, "Contact not found"}

      {:error,
       %Ecto.Changeset{
         changes: %{name: tag_name},
         errors: [
           contact_id: {"has already been taken", _}
         ]
       }} ->
        {:error, "Tag \"#{tag_name}\" has already been added to this contact"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot create tag"}}
    end
  end
end
