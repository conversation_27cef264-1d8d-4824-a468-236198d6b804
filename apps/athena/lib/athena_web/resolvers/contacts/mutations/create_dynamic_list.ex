defmodule AthenaWeb.Resolvers.Contacts.CreateDynamicList do
  @moduledoc """
  CreateDynamicList Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.DynamicList

  def resolve(_, %{dynamic_list: dynamic_list_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    %{
      company_profile_id: company_profile_id,
      last_updated_by_profile_user_id: company_profile_user_id,
      last_updated_at: Helper.ExDay.utc_now()
    }
    |> Enum.into(dynamic_list_input)
    |> Contacts.create_dynamic_list()
    |> case do
      {:ok, %DynamicList{} = dynamic_list} ->
        # Update dynamic list's estimated_contacts_size cache
        # Reason to do async here is to not block the list creation workflow
        # The frontend will redirect to list detail page, so it's unnecessary to wait for this
        Gaia.Jobs.CalculateDynamicListSize.enqueue(%{"dynamic_list_id" => dynamic_list.id})
        {:ok, dynamic_list}

      {:error,
       %Ecto.Changeset{
         changes: %{name: _},
         errors: [
           company_profile_id: {"has already been taken", _}
         ]
       }} ->
        {:error, "Name already exists. Please choose a different name"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot create dynamic list"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot create dynamic list"}
  end
end
