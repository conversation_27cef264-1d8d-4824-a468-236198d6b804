defmodule AthenaWeb.Resolvers.Contacts.UpdateStaticList do
  @moduledoc """
  UpdateStaticList Mu<PERSON> Resolvers

  This resolver is responsible for updating an existing static list by:
  - Handling the addition of new contacts to the static list.
  - Updating metadata such as the last updated timestamp and user.
  """

  use Helper.Pipe

  alias Gaia.Contacts
  alias Gaia.Contacts.StaticList

  def resolve(_, %{id: list_id, static_list: static_list_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    {contact_ids, static_list_input} = Map.pop(static_list_input, :contact_ids, [])

    static_list_input =
      Map.put(static_list_input, :last_updated_by_profile_user_id, company_profile_user_id)

    with %StaticList{} = static_list <-
           Contacts.get_static_list_by(%{id: list_id, company_profile_id: company_profile_id}),
         {:ok, updated_static_list} <-
           Contacts.update_static_list_with_contacts(static_list, static_list_input, contact_ids) do
      {:ok, updated_static_list}
    else
      nil ->
        {:error, "Static list not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update static list"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot update static list"}
  end
end
