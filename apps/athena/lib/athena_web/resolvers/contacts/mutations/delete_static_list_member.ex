defmodule AthenaWeb.Resolvers.Contacts.DeleteStaticListMember do
  @moduledoc """
  DeleteStaticListM<PERSON>ber
  Removes a contact from a static list
  """

  alias <PERSON><PERSON>.Contacts
  alias Gaia.Contacts.StaticList
  alias Gaia.Contacts.StaticListMember

  def resolve(_, %{static_list_id: static_list_id, contact_id: contact_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %StaticListMember{static_list_id: slm_static_list_id} = member <-
           Contacts.get_static_list_member_by(%{static_list_id: static_list_id, contact_id: contact_id}),
         %StaticList{company_profile_id: sl_company_profile_id, id: db_static_list_id} <-
           Contacts.get_static_list(slm_static_list_id),
         true <-
           db_static_list_id == slm_static_list_id and sl_company_profile_id == company_profile_id,
         {:ok, _} <- Contacts.delete_static_list_member(member) do
      {:ok, true}
    else
      {:error, _} ->
        {:error, "Cannot remove contact from static list"}

      nil ->
        {:error, "Cannot remove contact from static list"}

      _ ->
        {:error, "Cannot remove contact from static list"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot remove contact from static list"}
  end
end
