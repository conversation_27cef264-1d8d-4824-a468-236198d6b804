defmodule AthenaWeb.Resolvers.Contacts.UpdateLeadStatus do
  @moduledoc """
  UpdateLeadStatus Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Investors
  alias Gaia.Repo
  alias Investors.User

  def resolve(_, %{id: contact_id, lead_status: :nominated_shareholder}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    resolve_as_nominated_shareholder(%{id: contact_id, company_profile_id: company_profile_id})
  end

  def resolve(_, %{id: contact_id, lead_status: :investor_lead}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    resolve_as_lead(%{id: contact_id, company_profile_id: company_profile_id})
  end

  def resolve(_, %{id: _, lead_status: _}, _), do: {:error, "Cannot update this contact to the specified lead status"}

  def resolve(_, _, _), do: {:error, "Cannot update contact's lead status"}

  defp resolve_as_nominated_shareholder(contact_params) do
    with %Contact{investor: %User{} = investor} = contact <-
           contact_params |> Contacts.get_contact_by() |> Repo.preload(:investor),
         investor_changeset =
           Ecto.Changeset.change(investor, %{
             is_self_nominated_shareholder: true,
             self_nominated_shareholder_identified_at: Helper.ExDay.ecto_timestamp()
           }),
         contact_changeset =
           Ecto.Changeset.change(contact, %{
             is_nominated_shareholder: true,
             nominated_shareholder_identified_at: Helper.ExDay.ecto_timestamp()
           }),
         {:ok,
          %{
            investor: %User{} = _updated_investor_user,
            contact: %Contact{} = updated_contact
          }} <-
           Ecto.Multi.new()
           |> Ecto.Multi.update(:investor, investor_changeset)
           |> Ecto.Multi.update(:contact, contact_changeset)
           |> Gaia.Repo.transaction() do
      {:ok, updated_contact}
    else
      %Contact{investor: nil} = contact ->
        Contacts.update_contact(contact, %{
          is_nominated_shareholder: true,
          nominated_shareholder_identified_at: Helper.ExDay.ecto_timestamp()
        })

      nil ->
        {:error, "Contact not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update contact's lead status"}}
    end
  end

  defp resolve_as_lead(contact_params) do
    with %Contact{investor: %User{} = investor} = contact <-
           contact_params |> Contacts.get_contact_by() |> Repo.preload(:investor),
         investor_changeset =
           Ecto.Changeset.change(investor, %{
             is_self_nominated_shareholder: false,
             self_nominated_shareholder_identified_at: nil
           }),
         contact_changeset =
           Ecto.Changeset.change(contact, %{
             is_nominated_shareholder: false,
             nominated_shareholder_identified_at: nil
           }),
         {:ok,
          %{
            investor: %User{} = _updated_investor_user,
            contact: %Contact{} = updated_contact
          }} <-
           Ecto.Multi.new()
           |> Ecto.Multi.update(:investor, investor_changeset)
           |> Ecto.Multi.update(:contact, contact_changeset)
           |> Gaia.Repo.transaction() do
      {:ok, updated_contact}
    else
      %Contact{investor: nil} = contact ->
        Contacts.update_contact(contact, %{
          is_nominated_shareholder: false,
          nominated_shareholder_identified_at: nil
        })

      nil ->
        {:error, "Contact not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update contact's lead status"}}
    end
  end
end
