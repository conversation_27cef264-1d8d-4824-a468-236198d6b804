defmodule AthenaWeb.Resolvers.Contacts.LinkContactWithShareholdings do
  @moduledoc """
  LinkContactWithShareholdings Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact

  def resolve(_, %{id: contact_id, shareholding_ids: shareholding_ids}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id},
            user: %Gaia.Companies.User{id: company_user_id}
          }
        }
      }) do
    with %Contact{} = contact <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:ok, _} <-
           Contacts.link_contact_with_shareholding_ids(contact, shareholding_ids, company_user_id) do
      {:ok, contact}
    else
      nil ->
        {:error, "Contact not found"}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Cannot link contact with shareholdings"
         }}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot link contact with shareholdings"}
  end
end
