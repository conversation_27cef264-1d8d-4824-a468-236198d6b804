defmodule AthenaWeb.Resolvers.Contacts.UpdateTag do
  @moduledoc """
  UpdateTag Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Tag

  def resolve(_, %{id: tag_id, tag: tag_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Tag{} = tag <-
           Contacts.get_tag_by(%{company_profile_id: company_profile_id, id: tag_id}),
         {:ok, %Tag{} = updated_tag} <- Contacts.update_tag(tag, tag_input) do
      {:ok, updated_tag}
    else
      nil ->
        {:error, "Tag not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update tag"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot update tag"}
  end
end
