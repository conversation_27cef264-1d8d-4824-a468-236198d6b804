defmodule AthenaWeb.Resolvers.Contacts.UpdateContactNote do
  @moduledoc """
  UpdateContactNote Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Note

  def resolve(_, %{id: note_id, contact_note: note_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id},
            user: %Gaia.Companies.User{id: company_user_id}
          }
        }
      }) do
    with %Note{} = note <-
           Contacts.get_note_by(%{id: note_id, company_profile_id: company_profile_id, company_user_id: company_user_id}),
         {:ok, %Note{} = updated_note} <-
           Contacts.update_note(note, note_input) do
      {:ok, updated_note}
    else
      nil ->
        {:error, "Note not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update note"}}
    end
  end

  def resolve(_, _, _) do
    {:error, %Helper.AbsintheError{error: "Pattern match not found", message: "Cannot update note"}}
  end
end
