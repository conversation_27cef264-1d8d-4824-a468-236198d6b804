defmodule AthenaWeb.Resolvers.Contacts.UnlinkContactWithShareholding do
  @moduledoc """
  UnlinkContactWithShareholding Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Registers
  alias Gaia.Registers.Shareholding
  alias Gaia.Repo

  def resolve(_, %{id: contact_id, shareholding_id: shareholding_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{shareholdings: shareholdings, investor: _} = contact <-
           %{id: contact_id, company_profile_id: company_profile_id}
           |> Contacts.get_contact_by()
           |> Repo.preload([:shareholdings, :investor]),
         %Shareholding{} = shareholding <-
           Registers.get_shareholding_by(%{
             contact_id: contact_id,
             id: shareholding_id,
             company_profile_id: company_profile_id
           }),
         {:ok, %Contact{}} <- maybe_update_contact_lead(shareholdings, contact),
         {:ok, %Shareholding{} = _updated_shareholding} <-
           Registers.update_shareholding(shareholding, %{
             contact_id: nil,
             contact_linked_by_id: nil
           }) do
      {:ok, contact}
    else
      nil ->
        {:error, "Contact or shareholding not found"}

      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Cannot unlink contact with shareholding"
         }}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot unlink contact with shareholding"}
  end

  defp maybe_update_contact_lead([%Shareholding{} | tail], %Contact{} = contact) when tail == [] do
    Contacts.update_contact(
      contact,
      %{
        lead_converted_at: nil,
        lead_identified_at: NaiveDateTime.utc_now(:second)
      }
    )
  end

  defp maybe_update_contact_lead(_, contact), do: {:ok, contact}
end
