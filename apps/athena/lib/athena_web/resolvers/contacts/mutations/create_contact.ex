defmodule AthenaWeb.Resolvers.Contacts.CreateContact do
  @moduledoc """
  CreateContact Mutation Resolvers
  """

  alias Gaia.Companies.User
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact

  def resolve(_, %{contact: contact_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id},
            user: %Gaia.Companies.User{id: company_user_id} = company_user
          }
        }
      }) do
    %{
      company_profile_id: company_profile_id,
      creator_name: User.build_creator_name(company_user),
      creator_user_id: company_user_id,
      lead_identified_at: NaiveDateTime.utc_now(:second),
      contact_source: :manual_creation
    }
    |> Enum.into(contact_input)
    |> Contacts.create_contact()
    |> case do
      {:ok, %Contact{} = contact} ->
        {:ok, contact}

      {:error,
       %Ecto.Changeset{
         changes: %{email: email},
         errors: [
           company_profile_id: {"has already been taken", _}
         ]
       } = changeset} ->
        {:error,
         %Helper.AbsintheError{
           error: changeset,
           message: "There is an existing contact with the email #{email}"
         }}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot create contact"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot create contact"}
  end
end
