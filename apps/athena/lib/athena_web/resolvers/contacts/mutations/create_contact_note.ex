defmodule AthenaWeb.Resolvers.Contacts.CreateContactNote do
  @moduledoc """
  CreateContactNote Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Contacts.Note

  def resolve(_, %{contact_id: contact_id, contact_note: note_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id},
            user: %Gaia.Companies.User{id: company_user_id}
          }
        }
      }) do
    with %Contact{} <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:ok, %Note{} = created_note} <-
           %{
             contact_id: contact_id,
             company_profile_id: company_profile_id,
             company_user_id: company_user_id
           }
           |> Enum.into(note_input)
           |> Contacts.create_note() do
      {:ok, created_note}
    else
      nil ->
        {:error, "Contact not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot create note"}}
    end
  end

  def resolve(_, _, _) do
    {:error, %Helper.AbsintheError{error: "Pattern match not found", message: "Cannot create note"}}
  end
end
