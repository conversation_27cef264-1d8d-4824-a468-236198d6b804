defmodule AthenaWeb.Resolvers.Contacts.UpdateDynamicList do
  @moduledoc """
  UpdateDynamicList <PERSON><PERSON> Resolvers

  When updating dynamic list, we should invalidate old one and create a new one.
  The reason is so that we can keep track of the filters chosen for past emails.
  Also will be useful for email report.
  """

  use Helper.Pipe

  alias Gaia.Contacts
  alias Gaia.Contacts.DynamicList

  def resolve(_, %{id: list_id, dynamic_list: dynamic_list_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %DynamicList{} = dynamic_list <-
           Contacts.get_dynamic_list_by(%{id: list_id, company_profile_id: company_profile_id}),
         {:ok, updated_dynamic_list} <-
           Contacts.update_dynamic_list(
             dynamic_list,
             Map.merge(dynamic_list_input, %{
               last_updated_at: Helper.ExDay.utc_now(),
               last_updated_by_profile_user_id: company_profile_user_id
             })
           ) do
      {:ok, updated_dynamic_list}
    else
      nil ->
        {:error, "Dynamic list not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot update dynamic list"}}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot update dynamic list"}
  end
end
