defmodule AthenaWeb.Resolvers.Contacts.CreateStaticList do
  @moduledoc """
  CreateStaticList Mu<PERSON> Resolvers
  """

  alias Gaia.Contacts

  def resolve(_, %{static_list: static_list_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    {contact_ids, static_list_input} = Map.pop(static_list_input, :contact_ids, [])

    static_list_input =
      Enum.into(
        %{
          company_profile_id: company_profile_id,
          last_updated_by_profile_user_id: company_profile_user_id
        },
        static_list_input
      )

    case Contacts.create_static_list_with_contacts(static_list_input, contact_ids) do
      {:ok, static_list} ->
        {:ok, static_list}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot create static list"}}
    end
  end
end
