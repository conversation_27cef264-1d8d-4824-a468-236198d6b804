defmodule AthenaWeb.Resolvers.Contacts.DeleteStaticList do
  @moduledoc """
  DeleteStaticList Mu<PERSON> Resolvers
  """

  alias Gaia.Comms
  alias <PERSON>aia.Contacts
  alias Gaia.Contacts.StaticList
  alias <PERSON><PERSON>.Flows

  def resolve(_, %{id: static_list_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %StaticList{} = static_list <-
           Contacts.get_static_list_by(%{company_profile_id: company_profile_id, id: static_list_id}),
         [] <- Comms.list_draft_emails_by_static_list_id(company_profile_id, static_list_id),
         [] <- Flows.get_distribution_settings_by_static_list_id(company_profile_id, static_list_id),
         {:ok, _} <-
           Contacts.invalidate_static_list(
             static_list,
             %{
               last_updated_by_profile_user_id: company_profile_user_id
             }
           ) do
      {:ok, true}
    else
      nil ->
        {:error, "Static list not found"}

      _ ->
        {:error, "Cannot delete static list"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot delete static list"}
  end
end
