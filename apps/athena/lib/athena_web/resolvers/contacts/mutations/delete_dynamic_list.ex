defmodule AthenaWeb.Resolvers.Contacts.DeleteDynamicList do
  @moduledoc """
  DeleteDynamicList Mu<PERSON> Resolvers
  """

  alias Gaia.Comms
  alias Gaia.Contacts
  alias Gaia.Contacts.DynamicList
  alias Gaia.Flows

  def resolve(_, %{id: dynamic_list_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %DynamicList{} = dynamic_list <-
           Contacts.get_dynamic_list_by(%{company_profile_id: company_profile_id, id: dynamic_list_id}),
         [] <- Comms.list_draft_emails_by_dynamic_list_id(company_profile_id, dynamic_list_id),
         [] <- Flows.get_distribution_settings_by_dynamic_list_id(company_profile_id, dynamic_list_id),
         {:ok, _} <-
           Contacts.update_dynamic_list(
             dynamic_list,
             %{
               invalidated: true,
               last_updated_by_profile_user_id: company_profile_user_id
             }
           ) do
      {:ok, true}
    else
      nil ->
        {:error, "Dynamic list not found"}

      _ ->
        {:error, "Cannot delete dynamic list"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot delete dynamic list"}
  end
end
