defmodule AthenaWeb.Resolvers.Contacts.CreateStaticListMember do
  @moduledoc """
  CreateStaticListMember
  Adds a contact to a static list
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.StaticList
  alias Gaia.Contacts.StaticListMember

  def resolve(_, %{static_list_id: static_list_id, contact_id: contact_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: company_profile_user_id,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    static_list_member_input = %{
      static_list_id: String.to_integer(static_list_id),
      contact_id: String.to_integer(contact_id),
      company_profile_id: company_profile_id,
      last_updated_by_profile_user_id: company_profile_user_id,
      last_updated_at: Helper.ExDay.utc_now()
    }

    with %StaticList{company_profile_id: sl_company_profile_id} <-
           Contacts.get_static_list(static_list_id),
         %Contacts.Contact{company_profile_id: c_company_profile_id} <-
           Contacts.get_contact(contact_id),
         true <- sl_company_profile_id == company_profile_id and c_company_profile_id == company_profile_id,
         {:ok, %StaticListMember{} = static_list_member} <-
           Contacts.create_static_list_member(static_list_member_input) do
      {:ok, static_list_member}
    else
      {:error,
       %Ecto.Changeset{
         changes: %{contact_id: _},
         errors: [
           contact_id: {"has already been taken", _}
         ]
       }} ->
        {:error, "Contact already exists in the list"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Cannot add contact to static list"}}

      nil ->
        {:error, "Cannot add contact to static list"}

      _ ->
        {:error, "Cannot add contact to static list"}
    end
  end
end
