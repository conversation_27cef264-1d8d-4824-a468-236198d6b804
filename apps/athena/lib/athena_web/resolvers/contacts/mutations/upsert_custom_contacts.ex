defmodule AthenaWeb.Resolvers.Contacts.UpsertCustomContacts do
  @moduledoc """
  UpsertCustomContacts Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Contacts

  @big_import_count 500

  @gettext_context "Athena - UpsertCustomContacts mutation"

  def resolve(_, %{custom_contacts: custom_contacts} = args, %{context: context}) do
    if is_a_small_import?(custom_contacts) do
      import_contacts(args, context)
    else
      stage_import_until_approved(args, context)
    end
  end

  defp is_a_small_import?(custom_contacts) do
    length(custom_contacts) < @big_import_count
  end

  defp import_contacts(%{custom_contacts: custom_contacts} = args, %{
         current_company_profile_user: %ProfileUser{
           id: company_profile_user_id,
           user: %User{} = company_user,
           profile: %Profile{id: company_profile_id}
         }
       }) do
    audience_tags = Map.get(args, :audience_tags, [])

    is_global_unsubscribe = Map.get(args, :is_global_unsubscribe, false)
    unsubscribe_scopes = Map.get(args, :unsubscribe_scopes, [])

    apply_subscription_to_new_contact_only =
      Map.get(args, :apply_subscription_to_new_contact_only, true)

    custom_contacts
    |> Contacts.upsert_custom_contacts(
      audience_tags,
      is_global_unsubscribe,
      unsubscribe_scopes,
      apply_subscription_to_new_contact_only,
      company_profile_id,
      company_profile_user_id,
      company_user
    )
    |> case do
      {:ok, upserted_count, updated_count} ->
        Gaia.Jobs.VerifyContactEmails.enqueue(%{"company_profile_id" => company_profile_id})

        {:ok, %{updated_count: updated_count, upserted_count: upserted_count, needs_review: false}}

      error ->
        return_error(error)
    end
  end

  defp stage_import_until_approved(%{custom_contacts: custom_contacts} = args, %{
         current_company_profile_user: %ProfileUser{
           id: current_user_id,
           profile: %Profile{id: company_profile_id, ticker: %Gaia.Markets.Ticker{market_key: market_key}}
         }
       }) do
    is_uk = market_key in [:lse, :aqse]

    case Gaia.Contacts.BulkImport.create(%{
           company_profile_id: company_profile_id,
           uploader_profile_user_id: current_user_id,
           tags: args[:audience_tags],
           is_global_unsubscribe: args[:is_global_unsubscribe],
           unsubscribe_scopes: args[:unsubscribe_scopes],
           apply_subscription_to_new_contact_only: args[:apply_subscription_to_new_contact_only],
           client_answer_list_source: args[:client_answer_list_source],
           client_answer_last_usage: args[:client_answer_last_usage]
         }) do
      {:ok, bulk_import} ->
        Enum.each(custom_contacts, fn contact ->
          Gaia.Contacts.BulkImportContact.create(%{
            bulk_import_id: bulk_import.id,
            email: contact.email,
            first_name: contact[:first_name],
            last_name: contact[:last_name]
          })
        end)

        bulk_import_with_contact_count =
          Gaia.Contacts.BulkImport.get_with_full_preloads!(bulk_import.id)

        recipient = if is_uk, do: "<EMAIL>", else: "<EMAIL>"

        Gaia.Notifications.Email.deliver(EmailTransactional.Operations, :new_bulk_import, [
          bulk_import_with_contact_count,
          recipient
        ])

        Gaia.Jobs.VerifyContactEmails.enqueue(%{"bulk_import_id" => bulk_import.id})

        {:ok, %{updated_count: 0, upserted_count: length(custom_contacts), needs_review: true}}

      {:error, error} ->
        return_error(error)
    end
  end

  defp return_error(error) do
    {:error,
     %Helper.AbsintheError{
       error: error,
       message:
         dpgettext(
           "errors",
           @gettext_context,
           "Unfortunately, we could not update your contact list at this time, please try again later."
         )
     }}
  end
end
