defmodule AthenaWeb.Resolvers.Contacts.UnlinkContactWithInvestor do
  @moduledoc """
  UnlinkContactWithInvestor Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Investors
  alias Investors.User

  def resolve(_, %{id: contact_id, investor_user_id: investor_user_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{} = contact <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         %User{} = investor <-
           Investors.get_user_by(%{contact_id: contact_id, id: investor_user_id, company_profile_id: company_profile_id}),
         {:ok, %User{} = _updated_investor} <-
           Investors.update_user(investor, %{contact_id: nil}) do
      {:ok, contact}
    else
      nil ->
        {:error, "Contact or investor not found"}

      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Cannot unlink contact with investor"
         }}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot unlink contact with investor"}
  end
end
