defmodule AthenaWeb.Resolvers.Contacts.LinkContactWithBeneficialOwnerAccounts do
  @moduledoc """
  LinkContactWithBeneficialOwnerAccounts Mutation Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Contacts.Contact

  def resolve(_, %{id: contact_id, beneficial_owner_account_ids: beneficial_owner_account_ids}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Contact{} = contact <-
           Contacts.get_contact_by(%{id: contact_id, company_profile_id: company_profile_id}),
         {:ok, _} <-
           Contacts.link_contact_with_beneficial_owner_account_ids(contact, beneficial_owner_account_ids) do
      {:ok, contact}
    else
      nil ->
        {:error, "Contact not found"}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Cannot link contact with beneficial owner accounts"
         }}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot link contact with beneficial owner accounts"}
  end
end
