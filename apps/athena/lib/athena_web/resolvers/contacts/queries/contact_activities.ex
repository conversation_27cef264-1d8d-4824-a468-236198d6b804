defmodule AthenaWeb.Resolvers.Contacts.ContactActivities do
  @moduledoc """
  ContactActivities Query Resolvers
  """

  use Helper.Pipe

  alias Absinthe.Relay.Connection
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Repo

  def resolve(_, %{contact_id: contact_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters = update_filters_company_profile_id(args)

    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)

    with %Contact{} = contact <-
           %{id: contact_id, company_profile_id: company_profile_id}
           |> Contacts.get_contact_by()
           |> Repo.preload([:investor, :shareholdings, company_profile: [:ticker]]),
         {:ok, connection} <-
           contact
           |> Contacts.contact_activities_query(options)
           |> Connection.from_query(&Repo.all/1, args) do
      {:ok, %{edges: connection.edges, page_info: connection.page_info, options: options}}
    else
      nil ->
        {:error, "Contact not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving activities"}}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving activities"
     }}
  end

  def total(_, %{contact_id: contact_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters = update_filters_company_profile_id(args)

    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)

    %{id: contact_id, company_profile_id: company_profile_id}
    |> Contacts.get_contact_by()
    |> Repo.preload([:investor, :shareholdings, company_profile: [:ticker]])
    |> case do
      %Contact{} = contact ->
        contact
        |> Contacts.contact_activities_query(options)
        |> Repo.aggregate(:count)
        |> {:ok, __}

      nil ->
        {:error, "Contact not found"}
    end
  end

  def total(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving total activities"
     }}
  end

  defp update_filters_company_profile_id(args) do
    args
    |> Map.get(:options, %{})
    |> Map.get(:filters, [])
    |> Enum.filter(&(&1.key != "company_profile_id"))
  end
end
