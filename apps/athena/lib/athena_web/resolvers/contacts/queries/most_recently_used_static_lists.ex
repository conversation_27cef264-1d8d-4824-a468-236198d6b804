defmodule AthenaWeb.Resolvers.Contacts.MostRecentlyUsedStaticLists do
  @moduledoc """
  MostUsedStaticLists Query Resolvers
  """

  alias Gaia.Contacts
  alias Gaia.Repo

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = %{
      filters: [],
      orders: [%{key: "last_updated_at", value: "desc"}]
    }

    case company_profile_id |> Contacts.static_lists_query(options) |> Repo.all() do
      static_lists when is_list(static_lists) -> {:ok, static_lists}
      _ -> {:error, "Cannot get most used static lists"}
    end
  end

  def resolve(_, _, _) do
    # Return an empty list for unauthenticated users instead of an error
    {:ok, []}
  end
end
