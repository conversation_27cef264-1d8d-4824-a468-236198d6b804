defmodule AthenaWeb.Resolvers.Contacts.BulkImportContact do
  @moduledoc """
  BulkImportContact Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Contacts.BulkImport

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    bulk_imports = BulkImport.list_pending_for_company(company_profile_id)

    {:ok, bulk_imports}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
