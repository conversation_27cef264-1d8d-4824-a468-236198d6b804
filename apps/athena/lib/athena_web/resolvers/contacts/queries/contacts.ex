defmodule AthenaWeb.Resolvers.Contacts.Contacts do
  @moduledoc """
  Contacts Query Resolvers
  """

  alias Absinthe.Relay.Connection
  alias Gaia.Contacts
  alias Gaia.Repo

  def resolve(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = Map.get(args, :options, %{})

    company_profile_id
    |> Contacts.contacts_query(options)
    |> Connection.from_query(&Repo.all/1, args)
    |> case do
      {:ok, connection} ->
        {:ok, %{edges: connection.edges, options: options, page_info: connection.page_info}}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving contacts"}}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving contacts"
     }}
  end
end
