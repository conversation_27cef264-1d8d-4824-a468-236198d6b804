defmodule AthenaWeb.Resolvers.Contacts.StaticList do
  @moduledoc """
  StaticList Query Resolvers
  """

  use Helper.Pipe

  def resolve(_, %{id: static_list_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    {:ok, Gaia.Contacts.get_static_list_by(%{id: static_list_id, company_profile_id: company_profile_id})}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end

  def resolve_total_members(%{id: static_list_id}, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    args = %{company_profile_id: company_profile_id}

    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_static_list_total_members, args},
      static_list_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.static_list_id == static_list_id))
        |> case do
          nil -> {:ok, 0}
          %{total_members: total_members} -> {:ok, total_members}
        end
      end
    )
  end

  def resolve_total_contactable_members(%{id: static_list_id}, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    args = %{company_profile_id: company_profile_id}

    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_static_contactable_total_members, args},
      static_list_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.static_list_id == static_list_id))
        |> case do
          nil -> {:ok, 0}
          %{total_members: total_members} -> {:ok, total_members}
        end
      end
    )
  end

  def resolve_members_contact_ids(%{id: static_list_id}, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    args = %{company_profile_id: company_profile_id}

    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_static_list_member_contact_ids, args},
      static_list_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.static_list_id == static_list_id))
        |> case do
          nil -> {:ok, []}
          %{contact_ids: contact_ids} -> {:ok, contact_ids}
        end
      end
    )
  end
end
