defmodule AthenaWeb.Resolvers.Contacts.DynamicLists do
  @moduledoc """
  DynamicLists Query Resolvers
  """

  use Helper.Pipe

  alias Absinthe.Relay.Connection
  alias Gaia.Contacts
  alias Gaia.Repo

  def resolve(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = Map.get(args, :options, %{})

    company_profile_id
    |> Contacts.dynamic_lists_query(options)
    |> Connection.from_query(&Repo.all/1, args)
    |> case do
      {:ok, connection} ->
        {:ok, %{edges: connection.edges, options: options, page_info: connection.page_info}}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving data"}}
    end
  end

  def resolve(_, _, _) do
    # Return a proper error response instead of raising an exception
    {:ok,
     %{
       edges: [],
       options: %{},
       page_info: %{
         end_cursor: nil,
         has_next_page: false,
         has_previous_page: false,
         start_cursor: nil
       }
     }}
  end

  def total(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = Map.get(args, :options, %{})

    company_profile_id
    |> Contacts.dynamic_lists_query(options)
    |> Repo.aggregate(:count)
    |> {:ok, __}
  end

  def total(_, _, _) do
    # Return 0 for unauthenticated users
    {:ok, 0}
  end
end
