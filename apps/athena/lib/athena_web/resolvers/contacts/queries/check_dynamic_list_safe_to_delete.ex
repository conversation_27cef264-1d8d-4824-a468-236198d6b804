defmodule AthenaWeb.Resolvers.Contacts.CheckDynamicListSafeToDelete do
  @moduledoc """
  CheckDynamicListSafeToDelete Query Resolvers
  """

  alias Gaia.Comms
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Contacts
  alias <PERSON>aia.Contacts.DynamicList
  alias <PERSON><PERSON>.Flows

  def resolve(_, %{id: dynamic_list_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    %{company_profile_id: company_profile_id, id: dynamic_list_id}
    |> Contacts.get_dynamic_list_by()
    |> case do
      %DynamicList{} ->
        {:ok,
         %{
           id: dynamic_list_id,
           distribution_settings: Flows.get_distribution_settings_by_dynamic_list_id(company_profile_id, dynamic_list_id),
           draft_emails: Comms.list_draft_emails_by_dynamic_list_id(company_profile_id, dynamic_list_id)
         }}

      nil ->
        {:error, "Dynamic list not found."}
    end
  end

  def resolve(_, _, _), do: {:ok, nil}
end
