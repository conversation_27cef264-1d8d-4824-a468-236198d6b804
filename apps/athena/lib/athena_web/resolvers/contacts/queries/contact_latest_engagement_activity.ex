defmodule AthenaWeb.Resolvers.Contacts.ContactLatestEngagementActivityResolver do
  @moduledoc """
  ContactLatestEngagementActivityResolver
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Repo
  alias Helper.AbsintheError

  def resolve(_, %{contact_id: contact_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    case %{id: contact_id, company_profile_id: company_profile_id}
         |> Contacts.get_contact_by()
         |> Repo.preload([:investor, company_profile: [:ticker]]) do
      %Contact{} = contact ->
        activity = Contacts.get_contact_latest_engagement_activity(contact)
        {:ok, activity}

      nil ->
        {:error, %AbsintheError{error: "Contact not found", message: "Contact not found"}}

      activity ->
        {:ok, activity}
    end
  end
end
