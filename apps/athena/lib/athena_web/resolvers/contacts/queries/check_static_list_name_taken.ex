defmodule AthenaWeb.Resolvers.Contacts.CheckStaticListNameTaken do
  @moduledoc """
  CheckStaticListNameTaken Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{name: name}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok, Gaia.Contacts.is_static_list_name_taken?(company_profile_id, name)}
  end

  def resolve(_, _, _), do: {:error, "Cannot check if name is taken."}
end
