defmodule AthenaWeb.Resolvers.Contacts.Contact do
  @moduledoc """
  Contact Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Contacts

  def resolve(_, %{id: contact_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    %{id: contact_id, company_profile_id: company_profile_id}
    |> Contacts.get_contact_by()
    |> {:ok, __}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
