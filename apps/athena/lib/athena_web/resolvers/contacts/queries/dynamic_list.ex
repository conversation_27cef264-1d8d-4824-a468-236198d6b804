defmodule AthenaWeb.Resolvers.Contacts.DynamicList do
  @moduledoc """
  DynamicList Query Resolvers
  """

  use Helper.Pipe

  def resolve(_, %{id: dynamic_list_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    %{id: dynamic_list_id, company_profile_id: company_profile_id}
    |> Gaia.Contacts.get_dynamic_list_by()
    |> case do
      nil ->
        {:ok, nil}

      %Gaia.Contacts.DynamicList{} = dynamic_list ->
        # Update dynamic list's estimated_contacts_size cache
        Gaia.Jobs.CalculateDynamicListSize.enqueue(%{"dynamic_list_id" => dynamic_list.id})
        {:ok, dynamic_list}
    end
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
