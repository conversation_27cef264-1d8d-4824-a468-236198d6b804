defmodule AthenaWeb.Resolvers.Contacts.StaticListSuggestedNames do
  @moduledoc """
  StaticListSuggestedNames Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}}) do
    {:ok, Gaia.Contacts.get_static_list_suggested_names(company_profile_id)}
  end

  def resolve(_, _, _), do: {:error, "Cannot get suggested names."}
end
