defmodule AthenaWeb.Resolvers.Contacts.ContactShareholdingSummary do
  @moduledoc """
  ContactShareholdingSummary Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Interactions
  alias Gaia.Markets.Ticker
  alias Gaia.Repo

  def resolve(
        _,
        %{contact_id: contact_id, start_date: %Date{} = start_date, end_date: %Date{} = end_date, sort_order: sort_order},
        %{
          context: %{
            current_company_profile_user: %ProfileUser{
              profile: %Profile{
                id: company_profile_id,
                ticker: %Ticker{listing_key: listing_key, market_key: market_key} = ticker,
                timezone: timezone
              }
            }
          }
        }
      ) do
    %{id: contact_id, company_profile_id: company_profile_id}
    |> Contacts.get_contact_by()
    |> Repo.preload(:shareholdings)
    |> case do
      %Contact{} = contact ->
        {share_movements, daily_holdings} =
          Contacts.get_contact_movements_and_daily_holdings(contact, start_date, end_date, sort_order: sort_order)

        beneficial_owner_holdings_by_account =
          Gaia.BeneficialOwners.beneficial_owner_latest_holdings_by_contact(contact, start_date, end_date)

        # the beneficial_owner_holdings_by_account is separated by account name, so we need to group by date and id
        beneficial_owner_holdings =
          Gaia.BeneficialOwners.merge_beneficial_owner_holdings_by_account_name(beneficial_owner_holdings_by_account)

        {:ok,
         %{
           id: "#{contact_id}-#{start_date}-#{end_date}",
           announcements: get_announcements(listing_key, market_key, start_date, end_date, timezone),
           daily_holdings: daily_holdings,
           share_movements: share_movements,
           timeseries: get_timeseries(ticker, start_date, end_date),
           beneficial_owner_holdings: beneficial_owner_holdings,
           beneficial_owner_holdings_by_account: beneficial_owner_holdings_by_account
         }}

      nil ->
        {:ok, nil}
    end
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end

  defp get_announcements(listing_key, market_key, %Date{} = start_date, %Date{} = end_date, timezone) do
    %{
      filters: [
        %{key: "listing_key", value: String.upcase(listing_key)},
        %{key: "market_key", value: String.upcase("#{market_key}")},
        %{
          key: "posted_at_greater_than",
          value:
            start_date
            |> Timex.to_datetime(timezone)
            |> Timex.to_naive_datetime()
        },
        %{
          key: "posted_at_less_than",
          value:
            end_date
            |> Timex.to_datetime(timezone)
            |> Timex.end_of_day()
            |> Timex.to_naive_datetime()
        }
      ],
      orders: [%{key: "posted_at", value: "asc"}, %{key: "id", value: "asc"}]
    }
    |> Interactions.media_announcements_query()
    |> Repo.all()
  end

  defp get_timeseries(%Ticker{} = ticker, %Date{} = start_date, %Date{} = end_date) do
    case Gaia.MarketData.get_timeseries(ticker, start_date, end_date) do
      {:ok, ts} -> Gaia.MarketData.YahooFinance.combine_refinitiv_timeseries_with_chix(ts, ticker, start_date, end_date)
      _ -> []
    end
  end
end
