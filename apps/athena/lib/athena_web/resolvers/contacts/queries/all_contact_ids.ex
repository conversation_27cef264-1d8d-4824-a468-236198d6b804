defmodule AthenaWeb.Resolvers.Contacts.AllContactIds do
  @moduledoc """
  AllContactIds Query Resolvers

  Get the full list of only contact ids for a filter combination.
  """

  use Helper.Pipe

  alias Gaia.Contacts

  def resolve(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters = Map.get(args, :filters, [])
    orders = [%{key: "id", value: "asc"}]
    options = %{} |> Map.put(:filters, filters) |> Map.put(:orders, orders)

    company_profile_id
    |> Contacts.contacts_query(options)
    |> Contacts.contact_ids()
    |> {:ok, __}
  end

  def resolve(_, _, _) do
    {:ok, []}
  end
end
