defmodule AthenaWeb.Resolvers.Contacts.AmplifyInvestors do
  @moduledoc """
  AmplifyInvestors Query Resolvers
  """

  alias Absinthe.Relay.Connection
  alias Gaia.Contacts
  alias Gaia.Repo

  def resolve(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    company_profile_id
    |> Contacts.amplify_investors_query(args)
    |> Connection.from_query(&Repo.all/1, args)
    |> case do
      {:ok, connection} ->
        {:ok, %{edges: connection.edges, page_info: connection.page_info}}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving investors"}}
    end
  end
end
