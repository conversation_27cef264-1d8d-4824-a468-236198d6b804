defmodule AthenaWeb.Resolvers.Contacts.ExistingTags do
  @moduledoc """
  ExistingTags Query Resolvers
  """

  alias Gaia.Contacts

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{} = current_company_profile
          }
        }
      }) do
    {:ok, Contacts.existing_tags_by_company_profile(current_company_profile)}
  end

  def resolve(_, _, _) do
    {:ok, []}
  end
end
