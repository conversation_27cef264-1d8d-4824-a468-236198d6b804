defmodule AthenaWeb.Resolvers.Brokers.AllBrokersDisclosed do
  @moduledoc """
  Brokers AllBrokersDisclosed Query Resolvers
  """

  use Helper.Pipe

  import Ecto.Query, warn: false

  alias Gaia.Brokers
  alias Gaia.Investors

  def resolve(_parents, _args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    # Gets the number of shareholdings under each broker
    brokers_breakdown = Investors.get_brokers_breakdown_uk_by_company_profile_id(id)

    # Gets the list of brokers that we have
    # Loop through each broker
    # Check if the company has any shareholdings using its name
    # We should only returns the brokers that have shareholdings
    Brokers.list_brokers_uk()
    |> Enum.reduce({[], brokers_breakdown}, fn
      %{name: name} = broker, {brokers_acc, brokers_breakdown_acc} ->
        [name]
        |> Enum.reduce(0, &(&2 + Map.get(brokers_breakdown_acc, &1, 0)))
        |> case do
          0 ->
            # This broker is not returned because they do not have shareholdings
            {brokers_acc, brokers_breakdown_acc}

          count ->
            # This broker has shareholdings, add to the list
            # Remove the names from the brokers_breakdown mapping
            {
              [Map.put(broker, :shareholdings_count, count) | brokers_acc],
              Map.drop(brokers_breakdown_acc, [name])
            }
        end
    end)
    |> case do
      {brokers, leftover_brokers_breakdown} when leftover_brokers_breakdown == %{} ->
        # Empty map means that we have all of the broker information
        # Nothing more to do, just returns the brokers
        brokers

      {brokers, leftover_brokers_breakdown} ->
        # We don't have the broker information for some of the names
        # Put this leftover information into one unknown_broker and add it to list
        {names, shareholdings_count} =
          Enum.reduce(leftover_brokers_breakdown, {[], 0}, fn {broker_name, count}, {names_acc, count_acc} ->
            {[broker_name | names_acc], count + count_acc}
          end)

        # Sort the names for consistency
        unknown_broker =
          Enum.into(%{names: Enum.sort(names), shareholdings_count: shareholdings_count}, Brokers.unknown_broker_uk())

        [unknown_broker | brokers]
    end
    |> Enum.sort_by(& &1.name)
    |> {:ok, __}
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
