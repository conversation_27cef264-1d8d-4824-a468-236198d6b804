defmodule AthenaWeb.Resolvers.Webinars.ListWebinars do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Webinars

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    options = build_options(args, current_company_profile_id)

    case options
         |> Webinars.webinars_query()
         |> Connection.from_query(&Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      {:error, _reason} ->
        {:error, "Failed to fetch webinars"}
    end
  end

  def resolve_total(_, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    options = build_options(args, current_company_profile_id)

    case options
         |> Webinars.webinars_query()
         |> Repo.aggregate(:count) do
      count when is_integer(count) ->
        {:ok, count}

      _error ->
        {:error, "Failed to get the total number of webinars"}
    end
  end

  defp build_options(args, current_company_profile_id) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    args
    |> Map.get(:options, %{})
    |> Map.put(:filters, filters)
  end
end
