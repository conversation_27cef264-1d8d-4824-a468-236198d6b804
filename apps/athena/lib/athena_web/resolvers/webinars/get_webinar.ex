defmodule AthenaWeb.Resolvers.Webinars.GetWebinar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    case Webinars.get_webinar(profile_id, id) do
      nil ->
        {:error, "Webinar not found"}

      webinar ->
        {:ok, webinar}
    end
  end
end
