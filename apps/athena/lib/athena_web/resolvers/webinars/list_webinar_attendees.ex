defmodule AthenaWeb.Resolvers.Webinars.ListWebinarAttendees do
  @moduledoc """
  ListWebinarAttendees Query Resolvers
  """

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Webinars

  def resolve(_, %{webinar_id: webinar_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}
      }) do
    options = Map.get(args, :options, %{})

    with {:ok, _webinar} <- check_authorized_webinar(webinar_id, profile_id),
         {:ok, connection} <-
           webinar_id
           |> Webinars.list_attendees_query(options)
           |> Connection.from_query(&Repo.all/1, args) do
      {:ok,
       %{
         edges: connection.edges,
         options: options,
         page_info: connection.page_info
       }}
    else
      {:error, :unauthorized} ->
        {:error, "Unauthorized to fetch webinar attendees"}

      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, _reason} ->
        {:error, "Failed to fetch webinar attendees"}
    end
  end

  def resolve_total(_, %{webinar_id: webinar_id} = args, _) do
    options = Map.get(args, :options, %{})

    case webinar_id
         |> Webinars.list_attendees_query(options)
         |> Repo.aggregate(:count) do
      count when is_integer(count) ->
        {:ok, count}

      _error ->
        {:error, "Failed to get the total number of webinar attendees"}
    end
  end

  defp check_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
