defmodule AthenaWeb.Resolvers.Webinars.CreateWebinarDocument do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, webinar} <- get_authorized_webinar(args.webinar_id, profile_id),
         {:ok, document} <- Webinars.create_document(webinar, args) do
      {:ok, document}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to create document for this webinar"}

      {:error, _changeset} ->
        {:error, "Could not create webinar document"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
