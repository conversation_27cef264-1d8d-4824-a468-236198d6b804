defmodule AthenaWeb.Resolvers.Webinars.DeleteWebinarDocument do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  require Logger

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, document} <- get_authorized_document(id, profile_id),
         {:ok, deleted_document} <- Webinars.delete_document(document) do
      Task.start(fn -> try_delete_cloudinary_file(deleted_document) end)
      {:ok, deleted_document}
    else
      {:error, :not_found} ->
        {:error, "Webinar document not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to delete this webinar document"}

      {:error, _changeset} ->
        {:error, "Could not delete webinar document"}
    end
  end

  defp try_delete_cloudinary_file(document) do
    case Gaia.Websites.CloudinaryUploader.delete(document.cloudinary_id, %{resource_type: document.file_type}) do
      {:ok, %{"result" => "ok"}} ->
        :ok

      {:error, _} ->
        Logger.error("Could not delete Cloudinary file for webinar document #{inspect(document)}")
        :error
    end
  end

  defp get_authorized_document(id, profile_id) do
    case Webinars.get_document(id) do
      nil ->
        {:error, :not_found}

      document ->
        document = Gaia.Repo.preload(document, :webinar)

        if document.webinar.company_profile_id == profile_id do
          {:ok, document}
        else
          {:error, :unauthorized}
        end
    end
  end
end
