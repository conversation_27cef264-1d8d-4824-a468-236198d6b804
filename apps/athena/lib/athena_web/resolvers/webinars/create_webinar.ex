defmodule AthenaWeb.Resolvers.Webinars.CreateWebinar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, params, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id, id: id}}}) do
    whitelisted_params =
      %{
        type: params.type,
        title: params.title,
        summary: Map.get(params, :summary),
        start_time: params.start_time,
        end_time: params.end_time,
        timezone: params.timezone,
        allow_pre_webinar_comments: params.allow_pre_webinar_comments,
        organiser_company_profile_user_id: id,
        company_profile_id: profile_id,
        recording_needs_login: params.recording_needs_login,
        attendance_needs_login: Map.get(params, :attendance_needs_login, true),
        discoverable_on_hub:
          if(Map.has_key?(params, :discoverable_on_hub),
            do: params.discoverable_on_hub,
            else: params.type == "public"
          )
      }

    case Webinars.create_webinar(whitelisted_params) do
      {:ok, webinar} ->
        case Webinars.sync_with_hms(webinar) do
          {:ok, synced_webinar} ->
            Task.start(fn ->
              notify_slack(webinar)
            end)

            {:ok, synced_webinar}

          {:error, _reason} ->
            # Delete the webinar if we can't sync with HMS cause without a room ID it's useless
            Webinars.delete_webinar(webinar)

            {:error, "Failed to create webinar"}
        end

      {:error, _changeset} ->
        {:error, "Could not create webinar"}
    end
  end

  def notify_slack(webinar) do
    if Application.get_env(:helper, :runtime_env) == "production" do
      webinar = Gaia.Repo.preload(webinar, [:company_profile, organiser_company_profile_user: :user])

      with false <- webinar.company_profile.is_demo,
           false <- webinar.company_profile.is_trial do
        formatted_start =
          webinar.start_time
          |> DateTime.from_naive!("Etc/UTC")
          |> DateTime.shift_zone!(webinar.timezone)
          |> Calendar.strftime("%Y-%m-%d %H:%M")

        formatted_end =
          webinar.end_time
          |> DateTime.from_naive!("Etc/UTC")
          |> DateTime.shift_zone!(webinar.timezone)
          |> Calendar.strftime("%Y-%m-%d %H:%M")

        type =
          case webinar.type do
            :public -> "public"
            :private -> "private"
            :recording_only -> "recording-only"
            _ -> ""
          end

        Slack.Message.send(
          %{
            text:
              ":video_camera: New webinar created! #{webinar.organiser_company_profile_user.user.first_name} #{webinar.organiser_company_profile_user.user.last_name} from #{webinar.company_profile.name} has created a new #{type} webinar called '#{webinar.title}'. Time: #{formatted_start} - #{formatted_end} (#{webinar.timezone})"
          },
          :csm_channel_slack_webhook_url
        )
      end
    end
  end
end
