defmodule AthenaWeb.Resolvers.Webinars.GetPostWebinarAudienceMovement do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{webinar_id: webinar_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}
      }) do
    case check_authorized_webinar(webinar_id, profile_id) do
      {:ok, webinar} ->
        webinar
        |> Gaia.Repo.preload(:attendees)
        |> Map.get(:attendees)
        |> calculate_stats(webinar.timezone)

      {:error, :unauthorized} ->
        {:error, "Unauthorized to fetch webinar attendees"}

      {:error, :not_found} ->
        {:error, "Webinar not found"}
    end
  end

  defp calculate_stats(attendees, webinar_timezone) do
    {buyers, sellers, net_changes, total_volume_traded} =
      Enum.reduce(attendees, {0, 0, 0, 0}, fn attendee, {buyers, sellers, net_changes, total_volume_traded} ->
        case get_net_changes(attendee, webinar_timezone) do
          {:ok, 0} ->
            {buyers, sellers, net_changes, total_volume_traded}

          {:ok, current_net_change} ->
            new_net_changes = net_changes + current_net_change
            new_total_volume_traded = total_volume_traded + abs(current_net_change)

            cond do
              current_net_change > 0 ->
                {buyers + 1, sellers, new_net_changes, new_total_volume_traded}

              current_net_change < 0 ->
                {buyers, sellers + 1, new_net_changes, new_total_volume_traded}

              current_net_change == 0 ->
                {buyers, sellers, new_net_changes, new_total_volume_traded}
            end
        end
      end)

    {:ok,
     %{total_buyers: buyers, total_sellers: sellers, net_changes: net_changes, total_volume_traded: total_volume_traded}}
  end

  defp get_net_changes(attendee, webinar_timezone) do
    case Gaia.Webinars.get_watch_date(attendee, webinar_timezone) do
      nil ->
        {:ok, 0}

      date ->
        {:ok, shareholding_at_watching_time} =
          Gaia.Registers.calculate_investor_shareholding_at_date(
            attendee.investor_user_id,
            Date.add(date, -1)
          )

        today = Date.utc_today()

        end_date =
          case Date.compare(Date.add(date, 6), today) do
            :lt ->
              Date.add(date, 6)

            _ ->
              today
          end

        {:ok, shareholding_one_week_after_watching_time} =
          Gaia.Registers.calculate_investor_shareholding_by_date(
            attendee.investor_user_id,
            end_date,
            date
          )

        if is_number(shareholding_at_watching_time.share_count) and
             is_number(shareholding_one_week_after_watching_time.share_count) do
          {:ok,
           shareholding_one_week_after_watching_time.share_count -
             shareholding_at_watching_time.share_count}
        else
          {:ok, 0}
        end
    end
  end

  defp check_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
