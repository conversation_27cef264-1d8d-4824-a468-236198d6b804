defmodule AthenaWeb.Resolvers.Webinars.StopWebinarRecording do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars
  alias Gaia.Webinars.HMSApi

  require Logger

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, webinar} <- get_authorized_webinar(id, profile_id),
         {:ok, _} <- HMSApi.stop_recording_room(webinar.hms_room_id),
         {:ok, _} <- HMSApi.update_room_status(webinar.hms_room_id, false),
         {:ok, updated_webinar} <- Webinars.stop_broadcasting_webinar(webinar) do
      Task.start(fn -> Webinars.sync_with_hms(updated_webinar) end)
      {:ok, updated_webinar}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to stop recording for this webinar"}

      {:error, _changeset} ->
        {:error, "Could not stop webinar recording"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
