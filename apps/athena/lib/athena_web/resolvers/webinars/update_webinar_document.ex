defmodule AthenaWeb.Resolvers.Webinars.UpdateWebinarDocument do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id} = args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, document} <- get_authorized_document(id, profile_id),
         {:ok, updated_document} <- Webinars.update_document(document, args) do
      {:ok, updated_document}
    else
      {:error, :not_found} ->
        {:error, "Webinar document not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to update this webinar document"}

      {:error, _changeset} ->
        {:error, "Could not update webinar document"}
    end
  end

  defp get_authorized_document(id, profile_id) do
    case Webinars.get_document(id) do
      nil ->
        {:error, :not_found}

      document ->
        document = Gaia.Repo.preload(document, :webinar)

        if document.webinar.company_profile_id == profile_id do
          {:ok, document}
        else
          {:error, :unauthorized}
        end
    end
  end
end
