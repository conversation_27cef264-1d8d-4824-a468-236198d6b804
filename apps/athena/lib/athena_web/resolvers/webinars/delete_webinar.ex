defmodule AthenaWeb.Resolvers.Webinars.DeleteWebinar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, webinar} <- get_authorized_webinar(id, profile_id),
         {:ok, deleted_webinar} <- Webinars.delete_webinar(webinar) do
      {:ok, deleted_webinar}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized"}

      {:error, _changeset} ->
        {:error, "Could not delete webinar"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
