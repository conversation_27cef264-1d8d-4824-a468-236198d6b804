defmodule AthenaWeb.Resolvers.Webinars.SyncWebinar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, "Webinar not found"}

      webinar ->
        if webinar.company_profile_id == profile_id do
          case Webinars.sync_with_hms(webinar) do
            {:ok, webinar} ->
              {:ok, webinar}

            {:error, _reason} ->
              {:error, "Failed to sync webinar"}
          end
        else
          {:error, "Unauthorized"}
        end
    end
  end
end
