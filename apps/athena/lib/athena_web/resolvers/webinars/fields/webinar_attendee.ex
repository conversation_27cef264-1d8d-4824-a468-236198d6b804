defmodule AthenaWeb.Resolvers.Webinars.Fields.WebinarAttendee do
  @moduledoc false

  alias Gaia.Webinars.Attendee

  def shareholding_at_watch_time(%Attendee{} = attendee, _, _) do
    attendee
    |> Gaia.Repo.preload(:webinar)
    |> Map.get(:webinar)
    |> case do
      nil ->
        {:ok, nil}

      webinar ->
        case Gaia.Webinars.get_watch_date(attendee, webinar.timezone) do
          nil ->
            {:ok, nil}

          date ->
            Gaia.Registers.calculate_investor_shareholding_at_date(
              attendee.investor_user_id,
              Date.add(date, -1)
            )
        end
    end
  end

  def shareholding_up_to_one_week_after_watch_time(%Attendee{} = attendee, _, _) do
    attendee
    |> Gaia.Repo.preload(:webinar)
    |> Map.get(:webinar)
    |> case do
      nil ->
        {:ok, nil}

      webinar ->
        case Gaia.Webinars.get_watch_date(attendee, webinar.timezone) do
          nil ->
            {:ok, nil}

          date ->
            today = Date.utc_today()

            end_date =
              case Date.compare(Date.add(date, 6), today) do
                :lt ->
                  Date.add(date, 6)

                _ ->
                  today
              end

            Gaia.Registers.calculate_investor_shareholding_by_date(
              attendee.investor_user_id,
              end_date,
              date
            )
        end
    end
  end
end
