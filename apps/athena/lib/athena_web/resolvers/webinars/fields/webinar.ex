defmodule AthenaWeb.Resolvers.Webinars.Fields.Webinar do
  @moduledoc false
  alias G<PERSON>.Webinars
  alias Gaia.Webinars.Webinar

  def slug(%Webinar{} = webinar, _, _) do
    {:ok, Webinars.generate_webinar_page_slug(webinar)}
  end

  def has_published_webinar_smart_block(%Webinar{company_profile_id: company_profile_id}, _, _) do
    Absinthe.Resolution.Helpers.batch(
      {Webinars, :batch_get_has_published_webinar_smart_block},
      company_profile_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.company_profile_id == company_profile_id))
        |> case do
          nil -> {:ok, false}
          %{count: 0} -> {:ok, false}
          _ -> {:ok, true}
        end
      end
    )
  end
end
