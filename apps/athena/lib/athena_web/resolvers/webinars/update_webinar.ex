defmodule AthenaWeb.Resolvers.Webinars.UpdateWebinar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id} = params, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, webinar} <- get_authorized_webinar(id, profile_id),
         {:ok, updated_webinar} <- Webinars.update_webinar_and_email_reminders(webinar, params) do
      {:ok, updated_webinar}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to update this webinar"}

      {:error, _multi_name, _changeset} ->
        {:error, "Could not update webinar"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
