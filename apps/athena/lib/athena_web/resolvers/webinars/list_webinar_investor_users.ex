defmodule AthenaWeb.Resolvers.Webinars.ListWebinarInvestorUsers do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Webinars

  def resolve(_, %{webinar_id: webinar_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}
      }) do
    with {:ok, webinar} <- check_authorized_webinar(webinar_id, profile_id),
         {:ok, connection} <-
           webinar
           |> Repo.preload(:attendees)
           |> Map.get(:attendees)
           |> Enum.map(&update_first_watch_date(&1, webinar.timezone))
           |> Enum.filter(&(&1.first_watch_date != nil))
           |> Webinars.list_investor_users_query(webinar_id)
           |> Connection.from_query(&Repo.all/1, args) do
      {:ok,
       %{
         edges: connection.edges,
         page_info: connection.page_info
       }}
    else
      {:error, :unauthorized} ->
        {:error, "Unauthorized to fetch webinar investor users"}

      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, _reason} ->
        {:error, "Failed to fetch webinar investor users"}
    end
  end

  def resolve_total(_, %{webinar_id: webinar_id}, _) do
    case Webinars.get_webinar(webinar_id) do
      nil ->
        {:error, :not_found}

      webinar ->
        case webinar
             |> Repo.preload(:attendees)
             |> Map.get(:attendees)
             |> Enum.map(&update_first_watch_date(&1, webinar.timezone))
             |> Enum.filter(&(&1.first_watch_date != nil))
             |> Webinars.list_investor_users_query(webinar_id)
             |> Repo.aggregate(:count) do
          count when is_integer(count) ->
            {:ok, count}

          _error ->
            {:error, "Failed to get the total number of webinar investor users"}
        end
    end
  end

  defp update_first_watch_date(attendee, timezone) do
    first_watch_date = Gaia.Webinars.get_watch_date(attendee, timezone)

    updated_attendee =
      case first_watch_date do
        nil -> Map.put(attendee, :first_watch_date, nil)
        date -> Map.put(attendee, :first_watch_date, Date.add(date, -1))
      end

    updated_attendee
  end

  defp check_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
