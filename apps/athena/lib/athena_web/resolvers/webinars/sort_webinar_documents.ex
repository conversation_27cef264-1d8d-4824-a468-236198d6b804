defmodule AthenaWeb.Resolvers.Webinars.SortWebinarDocuments do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{webinar_id: webinar_id, document_ids: document_ids}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}
      }) do
    with {:ok, webinar} <- get_authorized_webinar(webinar_id, profile_id),
         {:ok, _} <- Webinars.sort_documents(webinar, document_ids) do
      {:ok, true}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to sort documents for this webinar"}

      {:error, _reason} ->
        {:error, "Could not sort webinar documents"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
