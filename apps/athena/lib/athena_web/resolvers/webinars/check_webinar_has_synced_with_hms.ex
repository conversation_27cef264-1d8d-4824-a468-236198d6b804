defmodule AthenaWeb.Resolvers.Webinars.CheckWebinarHasSynced<PERSON>ithHMS do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    case get_authorized_webinar(id, profile_id) do
      {:ok, webinar} ->
        {:ok, check_webinar_has_synced_with_hms(webinar)}

      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to check if this webinar ended"}
    end
  end

  defp check_webinar_has_synced_with_hms(webinar) do
    webinar.sync_with_hms_at != nil and webinar.sync_with_hms_at > webinar.stopped_broadcasting_at
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
