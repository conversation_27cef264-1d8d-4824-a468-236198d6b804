defmodule AthenaWeb.Resolvers.Webinars.StartWebinarRecording do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Webinars
  alias Gaia.Webinars.HMSApi

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}}) do
    with {:ok, webinar} <- get_authorized_webinar(id, profile_id),
         {:ok, _} <- HMSApi.start_recording_room(webinar.hms_room_id),
         {:ok, updated_webinar} <- Webinars.start_broadcasting_webinar(webinar) do
      {:ok, updated_webinar}
    else
      {:error, :not_found} ->
        {:error, "Webinar not found"}

      {:error, :unauthorized} ->
        {:error, "Unauthorized to start recording for this webinar"}

      {:error, _changeset} ->
        {:error, "Could not start webinar recording"}
    end
  end

  defp get_authorized_webinar(id, profile_id) do
    case Webinars.get_webinar(id) do
      nil ->
        {:error, :not_found}

      webinar ->
        if webinar.company_profile_id == profile_id do
          {:ok, webinar}
        else
          {:error, :unauthorized}
        end
    end
  end
end
