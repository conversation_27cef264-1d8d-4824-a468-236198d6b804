defmodule AthenaWeb.Resolvers.Comms.CustomEmails do
  @moduledoc """
  CustomEmails Query Resolvers
  """

  alias Gaia.Comms
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}}) do
    {:ok, Comms.get_custom_emails_by_company_profile_id(company_profile_id)}
  end
end
