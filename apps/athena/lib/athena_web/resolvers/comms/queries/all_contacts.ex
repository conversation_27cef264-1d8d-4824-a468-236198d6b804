defmodule AthenaWeb.Resolvers.Comms.AllContacts do
  @moduledoc false

  use Helper.Pipe

  def resolve(_parent, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    options = Map.get(args, :options, %{})

    company_profile_id
    |> Gaia.Contacts.contacts_query(options)
    |> Gaia.Repo.all()
    |> {:ok, __}
  end
end
