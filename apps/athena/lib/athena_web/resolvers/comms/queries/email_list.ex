defmodule AthenaWeb.Resolvers.Comms.EmailList do
  @moduledoc """
    EmailList Query Resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Comms
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  def cursor(_parents, args, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}}) do
    options = Map.get(args, :options, %{filters: []})

    connection_result =
      options
      |> Comms.emails_query_by_company_profile_id(id)
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
