defmodule AthenaWeb.Resolvers.Comms.EstimateEmailRecipientsCount do
  @moduledoc """
  EstimateEmailRecipientsCount Query Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - EstimateEmailRecipientsCount Query"

  def resolve(_, %{email_id: email_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    %{company_profile_id: company_profile_id, id: email_id}
    |> Comms.get_email_by()
    |> Gaia.Repo.preload(media: [:media_announcement, :media_update])
    |> case do
      %Email{} = email ->
        {:ok, Comms.count_email_recipients(email, Comms.subscription_scope_from_email(email))}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: dpgettext("errors", @gettext_context, "Could not estimate")
         }}
    end
  end
end
