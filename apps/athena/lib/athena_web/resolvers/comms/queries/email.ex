defmodule AthenaWeb.Resolvers.Comms.Email do
  @moduledoc """
  Email Query Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{id: email_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with {:get_email, %Email{company_profile_id: company_profile_id} = email} <-
           {:get_email, Comms.get_email(email_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id} do
      {:ok, email}
    else
      _error ->
        {:error, "Could not find email"}
    end
  end
end
