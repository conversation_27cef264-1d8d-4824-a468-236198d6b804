defmodule AthenaWeb.Resolvers.Comms.EmailRecipients do
  @moduledoc """
  EmailRecipients Query Resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Helper.AbsintheError

  def cursor(_, %{email_id: email_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    options = Map.get(args, :options, %{})

    with {:get_email, %Email{company_profile_id: company_profile_id} = _email} <-
           {:get_email, Comms.get_email(email_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:get_email_recipients_connection, {:ok, connection}} <-
           {:get_email_recipients_connection,
            email_id
            |> Comms.list_email_recipients(options)
            |> Connection.from_list(args)} do
      {:ok,
       %{
         email_id: email_id,
         edges: connection.edges,
         page_info: connection.page_info,
         options: options
       }}
    else
      error ->
        {:error, %AbsintheError{error: error, message: "Cannot retrieve email recipients"}}
    end
  end

  def total(_, %{email_id: email_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with {:get_email, %Email{company_profile_id: company_profile_id} = _email} <-
           {:get_email, Comms.get_email(email_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id} do
      options = Map.get(args, :options, %{})

      {:ok,
       email_id
       |> Comms.list_email_recipients(options)
       |> length()}
    else
      _error ->
        {:error, "Cannot retrieve total email recipients"}
    end
  end
end
