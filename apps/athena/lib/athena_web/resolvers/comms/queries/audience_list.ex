defmodule AthenaWeb.Resolvers.Comms.AudienceList do
  @moduledoc false

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    audiences = Gaia.Contacts.group_contacts_by_source(company_profile_id)
    unsub_list = Gaia.Contacts.list_unsubscribed_contacts(company_profile_id)
    {:ok, Map.merge(audiences, unsub_list)}
  end
end
