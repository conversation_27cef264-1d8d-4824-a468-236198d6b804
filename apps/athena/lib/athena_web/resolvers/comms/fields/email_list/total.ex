defmodule AthenaWeb.Resolvers.Comms.EmailList.Total do
  @moduledoc """
    EmailList Field Resolvers - Total
  """

  import Ecto.Query, warn: false

  alias Gaia.Comms
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  def total(_parents, args, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}}) do
    options = Map.get(args, :options, %{filters: []})

    length =
      options
      |> Comms.emails_query_by_company_profile_id(id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end
end
