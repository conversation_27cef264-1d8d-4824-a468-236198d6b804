defmodule AthenaWeb.Resolvers.Comms.CustomEmailsFields do
  @moduledoc """
  CustomEmailsFields Resolvers
  """

  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def custom_campaign_template(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}
      }) do
    {:ok, Gaia.Comms.get_or_build_base_email_template_by_template_type(profile, :custom_campaign)}
  end

  def automated_distribution_template(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}
      }) do
    {:ok,
     Gaia.Comms.get_or_build_base_email_template_by_template_type(
       profile,
       :automated_distribution
     )}
  end

  def manual_distribution_template(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}
      }) do
    {:ok, Gaia.Comms.get_or_build_base_email_template_by_template_type(profile, :manual_distribution)}
  end

  def new_shareholder_welcome_template(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}
      }) do
    {:ok,
     Gaia.Comms.get_or_build_base_email_template_by_template_type(
       profile,
       :new_shareholder_welcome
     )}
  end
end
