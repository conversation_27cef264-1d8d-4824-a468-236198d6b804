defmodule AthenaWeb.Resolvers.Comms.Fields.Email do
  @moduledoc """
    Email Field Resolvers
  """

  import Ecto.Query, warn: false

  alias <PERSON>aia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Comms.Helper.EmailMergeTags
  alias Gaia.Repo

  @stats [:Total, :Delivery, :Open, :Click, :Unsubscribed, :<PERSON><PERSON><PERSON>, :<PERSON><PERSON><PERSON><PERSON>]
  def get_email_stats_type, do: @stats

  def email_stats(%Email{id: id}, %{type: type}, _), do: {:ok, Comms.count_total_recipient_events_by_email(id, type)}

  def preview_email(%Email{email_html: email_html} = email, _, _) when is_binary(email_html) do
    merge_tags = Comms.build_merge_tags_for_email(email, nil)

    {:ok,
     EmailMergeTags.replace_merge_tag(
       struct(%EmailMergeTags{}, merge_tags),
       email_html
     )}
  end

  def preview_email(_, _, _), do: {:ok, nil}

  def preview_email_subject(%Email{subject: subject} = email, _, _) when is_binary(subject) do
    merge_tags =
      email
      |> Repo.preload(media: [:media_announcement, :media_update])
      |> Map.get(:media)
      |> Comms.build_subject_merge_tags_for_email_media()

    {:ok,
     EmailMergeTags.replace_merge_tag(
       struct(%EmailMergeTags{}, merge_tags),
       subject
       |> String.replace(~r/{{[\n| ]*announcement_title[\n| ]*}}/, "{{ interactive_media_title }}")
       |> String.replace(~r/{{[\n| ]*update_title[\n| ]*}}/, "{{ interactive_media_title }}")
     )}
  end

  def preview_email_subject(_, _, _), do: {:ok, nil}

  def do_not_send_to_contacts(%Email{do_not_send_to_contact_ids: do_not_send_to_contact_ids} = email, _, _) do
    resolve_batch_contacts(Map.take(email, [:company_profile_id]), do_not_send_to_contact_ids)
  end

  def send_to_contacts(%Email{send_to_contact_ids: send_to_contact_ids} = email, _, _) do
    resolve_batch_contacts(Map.take(email, [:company_profile_id]), send_to_contact_ids)
  end

  defp resolve_batch_contacts(options, contact_ids) do
    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_contacts, options},
      contact_ids,
      fn batch_results ->
        {
          :ok,
          contact_ids
          |> Enum.map(&Map.get(batch_results, &1))
          |> Enum.filter(&(not is_nil(&1)))
        }
      end
    )
  end

  def do_not_send_to_dynamic_lists(%Email{do_not_send_to_dynamic_list_ids: do_not_send_to_dynamic_list_ids} = email, _, _) do
    resolve_batch_dynamic_lists(Map.take(email, [:company_profile_id]), do_not_send_to_dynamic_list_ids)
  end

  def send_to_dynamic_lists(%Email{send_to_dynamic_list_ids: send_to_dynamic_list_ids} = email, _, _) do
    resolve_batch_dynamic_lists(Map.take(email, [:company_profile_id]), send_to_dynamic_list_ids)
  end

  def do_not_send_to_static_lists(%Email{do_not_send_to_static_list_ids: do_not_send_to_static_list_ids} = email, _, _) do
    resolve_batch_static_lists(Map.take(email, [:company_profile_id]), do_not_send_to_static_list_ids)
  end

  def send_to_static_lists(%Email{send_to_static_list_ids: send_to_static_list_ids} = email, _, _) do
    resolve_batch_static_lists(Map.take(email, [:company_profile_id]), send_to_static_list_ids)
  end

  defp resolve_batch_dynamic_lists(options, dynamic_list_ids) do
    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_dynamic_lists, options},
      dynamic_list_ids,
      fn batch_results ->
        {
          :ok,
          dynamic_list_ids
          |> Enum.map(&Map.get(batch_results, &1))
          |> Enum.filter(&(not is_nil(&1)))
        }
      end
    )
  end

  defp resolve_batch_static_lists(options, static_list_ids) do
    Absinthe.Resolution.Helpers.batch(
      {Gaia.Contacts, :batch_get_static_lists, options},
      static_list_ids,
      fn batch_results ->
        {
          :ok,
          static_list_ids
          |> Enum.map(&Map.get(batch_results, &1))
          |> Enum.filter(&(not is_nil(&1)))
        }
      end
    )
  end
end
