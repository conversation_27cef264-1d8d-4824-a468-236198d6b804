defmodule AthenaWeb.Resolvers.Comms.UpdateEmailAndEmailRecipients do
  @moduledoc """
  UpdateEmailAndEmailRecipients Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias <PERSON>aia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Comms.EmailRecipient
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @gettext_context "Athena - UpdateEmailAndEmailRecipients mutation"

  @batch_size 5000

  def resolve(_, %{email: email_input, email_id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_email,
          %Email{
            company_profile_id: company_profile_id,
            sent_at: sent_at
          } = email} <-
           {:get_email, email_id |> Comms.get_email() |> Repo.preload(:email_recipients)},
         {:is_not_sent, true} <- {:is_not_sent, is_nil(sent_at)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:update_email_and_email_recipients, {:ok, %{update_email: %Email{} = updated_email}}} <-
           {:update_email_and_email_recipients,
            update_email_and_email_recipients(email, Map.put(email_input, :last_updated_by, company_profile_user_id))} do
      {:ok, updated_email}
    else
      {:get_email, nil} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Email campaign does not exist.")
         }}

      {:is_not_sent, _} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Email campaign has already been sent.")
         }}

      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Cannot update email campaign."
              )
          }
        }
    end
  end

  defp update_email_and_email_recipients(%Email{id: email_id, email_recipients: email_recipients} = email, email_input) do
    email_recipient_contact_ids_input = Map.get(email_input, :contact_ids)

    email_changeset = Ecto.Changeset.change(email, Map.delete(email_input, :contact_ids))

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update_email, email_changeset)
    |> upsert_email_recipients(email_recipients, email_recipient_contact_ids_input, email_id)
    |> Repo.transaction()
  end

  defp upsert_email_recipients(multi, email_recipients, email_recipient_contact_ids_input, email_id)
       when not is_nil(email_recipient_contact_ids_input) do
    existing_email_recipient_contact_ids = Enum.map(email_recipients, & &1.contact_id)
    incoming_email_recipient_contact_ids = Enum.map(email_recipient_contact_ids_input, &String.to_integer(&1))

    inserted_email_recipient_contact_ids = incoming_email_recipient_contact_ids -- existing_email_recipient_contact_ids

    delete_email_recipients_query =
      where(
        EmailRecipient,
        [er],
        er.contact_id not in ^incoming_email_recipient_contact_ids and er.email_id == ^email_id
      )

    multi
    |> Ecto.Multi.delete_all(:delete_email_recipients, delete_email_recipients_query)
    |> insert_email_recipients_chunked(inserted_email_recipient_contact_ids, email_id)
  end

  defp upsert_email_recipients(multi, _email_recipients, _email_recipient_contact_ids_input, _email_id) do
    multi
  end

  defp insert_email_recipients_chunked(multi, contact_ids, email_id) do
    contact_ids
    |> Enum.chunk_every(@batch_size)
    |> Enum.reduce({multi, 0}, fn chunk, {acc_multi, count} ->
      new_multi =
        Ecto.Multi.insert_all(
          acc_multi,
          :"insert_#{email_id}_#{count}",
          EmailRecipient,
          Enum.map(
            chunk,
            &%{
              contact_id: &1,
              email_id: email_id,
              inserted_at: NaiveDateTime.utc_now(:second),
              updated_at: NaiveDateTime.utc_now(:second)
            }
          )
        )

      {new_multi, count + 1}
    end)
    |> elem(0)
  end
end
