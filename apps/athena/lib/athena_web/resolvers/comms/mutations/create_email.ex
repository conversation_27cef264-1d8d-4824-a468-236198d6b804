defmodule AthenaWeb.Resolvers.Comms.CreateEmail do
  @moduledoc """
  CreateEmail Mutation Resolvers
  """
  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - CreateEmail mutation"

  def resolve(_parents, %{campaign_name: campaign_name} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{id: company_profile_user_id, profile: %Profile{id: profile_id}}
        }
      }) do
    %{
      campaign_name: campaign_name,
      company_profile_id: profile_id,
      last_updated_by: company_profile_user_id,
      subject: Map.get(args, :subject),
      media_id: Map.get(args, :media_id)
    }
    |> Comms.create_email()
    |> case do
      {:ok, %Email{} = created_email} ->
        {:ok, created_email}

      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Unfortunately we could not create an email campaign at this time, please try again later or contact us for support."
              )
          }
        }
    end
  end
end
