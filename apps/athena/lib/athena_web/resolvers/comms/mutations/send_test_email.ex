defmodule AthenaWeb.Resolvers.Comms.Mutations.SendTestEmail do
  @moduledoc """
    SendTestEmail Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.CustomEmail
  alias Gaia.Comms.Email
  alias Gaia.Comms.Helper.EmailMergeTags
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @gettext_context "Athena - SendTestEmail mutation"

  def resolve(_, %{id: email_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with {:get_email, %Email{company_profile_id: company_profile_id} = email} <-
           {:get_email, email_id |> Comms.get_email() |> Repo.preload(company_profile: :custom_emails)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:send_test_email, {:ok, true}} <- {:send_test_email, send_with_custom_emails(email, args)} do
      {:ok, true}
    else
      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Unfortunately we could not send a test email at this time, please try again later or contact us for support."
              )
          }
        }
    end
  end

  defp send_with_custom_emails(
         %Email{
           company_profile: %Profile{custom_emails: custom_emails},
           subject: subject,
           email_html: email_html_from_db,
           from_name: from_name,
           from_email: from_email
         } = email,
         args
       ) do
    with %CustomEmail{
           send_from_email: send_from_email,
           send_from_name: send_from_name,
           reply_to_email: reply_to_email,
           type: :marketing
         } <- Comms.get_custom_email_by_type(custom_emails, :marketing),
         recipient_emails = Map.get(args, :recipient_emails, []),
         # Logic for sending test email
         # If email_html is provided from frontend, prioritise it over the one from database
         email_html_to_use = Map.get(args, :email_html, email_html_from_db),
         merge_tags = Comms.build_merge_tags_for_email(email, nil),
         html_body =
           EmailMergeTags.replace_merge_tag(
             struct(%EmailMergeTags{}, merge_tags),
             email_html_to_use
           ),
         {:ok, _} <-
           Gaia.Notifications.Email.deliver(
             EmailTransactional.Investor,
             :campaign_email,
             [
               %{
                 from: {from_name || send_from_name, if(is_nil(from_email), do: send_from_email, else: from_email)},
                 to: recipient_emails,
                 html_body: html_body,
                 subject: "[TEST] #{subject}",
                 reply_to: reply_to_email
               }
             ]
           ) do
      {:ok, true}
    end
  end
end
