defmodule AthenaWeb.Resolvers.Comms.Mutations.UpsertBaseEmailTemplate do
  @moduledoc """
  UpsertBaseEmailTemplate Resolvers
  """
  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.BaseEmailTemplate
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - UpsertBaseEmailTemplate mutation"

  def resolve(_, %{email_html: email_html, email_json: email_json} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: last_edited_by_company_profile_user_id,
            profile: %Profile{id: company_profile_id} = profile
          }
        }
      }) do
    template_type = Map.get(args, :template_type, :custom_campaign)

    with {:ok, email_html} <- get_email_template_html(email_html, profile, template_type),
         {:ok, email_json} <- get_email_template_json(email_json, profile, template_type),
         {:ok, %BaseEmailTemplate{} = upserted_base_email_template} <-
           Comms.upsert_base_email_template_by_company_profile_id(
             %{
               email_html: email_html,
               email_json: email_json,
               template_type: template_type,
               last_edited_by_company_profile_user_id: last_edited_by_company_profile_user_id
             },
             company_profile_id
           ),
         :ok <-
           Comms.maybe_update_flows_distributions_settings_emails(
             %{email_html: email_html, email_json: email_json, company_profile_id: company_profile_id},
             template_type
           ) do
      {:ok, upserted_base_email_template}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately we could not update the base email template at this time, please check your input and try again later."
             )
         }}
    end
  end

  defp get_email_template_html(email_html, profile, template_type) do
    if String.trim(email_html) === "" do
      get_company_default_email_template_html(profile, template_type)
    else
      {:ok, email_html}
    end
  end

  defp get_email_template_json(email_json, %Profile{} = profile, template_type) do
    if String.trim(email_json) === "" do
      get_company_default_email_template_json(profile, template_type)
    else
      {:ok, email_json}
    end
  end

  def get_company_default_email_template_html(%Profile{} = profile, template_type) do
    case Comms.get_company_email_template_merge_tag_params(profile, template_type) do
      {:ok, merge_tag_params} ->
        template_html =
          template_type
          |> Comms.get_default_email_template_html()
          |> replace_company_email_template_merge_tag_params(merge_tag_params, template_type)

        {:ok, template_html}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately, we could not get your email template at this time. Please check your investor hub settings and try again."
             )
         }}
    end
  end

  def get_company_default_email_template_json(%Profile{} = profile, template_type) do
    case Comms.get_company_email_template_merge_tag_params(profile, template_type) do
      {:ok, merge_tag_params} ->
        template_json =
          template_type
          |> Comms.get_default_email_template_json()
          |> replace_company_email_template_merge_tag_params(
            merge_tag_params,
            template_type
          )

        {:ok, template_json}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately, we could not get your email template at this time. Please check your investor hub settings and try again."
             )
         }}
    end
  end

  defp replace_company_email_template_merge_tag_params(template, merge_tag_params, :custom_campaign) do
    Gaia.Helpers.Comms.CustomCampaignMergeTags.replace_merge_tag(merge_tag_params, template)
  end

  defp replace_company_email_template_merge_tag_params(template, merge_tag_params, :automated_distribution) do
    Gaia.Helpers.Comms.AutomatedDistributionMergeTags.replace_merge_tag(merge_tag_params, template)
  end

  defp replace_company_email_template_merge_tag_params(template, merge_tag_params, :manual_distribution) do
    Gaia.Helpers.Comms.ManualDistributionMergeTags.replace_merge_tag(merge_tag_params, template)
  end

  defp replace_company_email_template_merge_tag_params(template, merge_tag_params, :new_shareholder_welcome) do
    Gaia.Helpers.Comms.NewShareholderWelcomeMergeTags.replace_merge_tag(merge_tag_params, template)
  end
end
