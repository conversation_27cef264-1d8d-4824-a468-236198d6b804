defmodule AthenaWeb.Resolvers.Comms.ContactUs do
  @moduledoc false

  def resolve(_parent, args, %{
        context: %{
          current_company_profile_user:
            %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{id: company_profile_id},
              user: %Gaia.Companies.User{}
            } = profile_user,
          cloud_ip: cloud_ip
        }
      }) do
    cloud_ip = cloud_ip |> Tuple.to_list() |> Enum.join(".")

    case Gaia.Notifications.Email.deliver(
           EmailTransactional.Operations,
           :contact_form,
           [Map.put(profile_user, :ip_address, cloud_ip), args],
           company_profile_id
         ) do
      {:ok, _} ->
        {:ok, true}

      _ ->
        {:ok, false}
    end
  end

  # If there is no ticker but current_company_user has already logged in
  def resolve(_parent, args, %{
        context: %{current_company_user: %Gaia.Companies.User{} = company_user, cloud_ip: cloud_ip}
      }) do
    cloud_ip = cloud_ip |> Tuple.to_list() |> Enum.join(".")

    case Gaia.Notifications.Email.deliver(EmailTransactional.Operations, :contact_form, [
           %{user: company_user, profile: %{}, ip_address: cloud_ip},
           args
         ]) do
      {:ok, _} ->
        {:ok, true}

      _ ->
        {:ok, false}
    end
  end

  # If there is no ticker and no user has logged in
  def resolve(_parent, args, %{context: %{cloud_ip: cloud_ip}}) do
    cloud_ip = cloud_ip |> Tuple.to_list() |> Enum.join(".")

    case Gaia.Notifications.Email.deliver(EmailTransactional.Operations, :contact_form, [
           %{user: %{}, profile: %{}, ip_address: cloud_ip},
           args
         ]) do
      {:ok, _} ->
        {:ok, true}

      _ ->
        {:ok, false}
    end
  end
end
