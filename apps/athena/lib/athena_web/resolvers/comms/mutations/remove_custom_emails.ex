defmodule AthenaWeb.Resolvers.Comms.RemoveCustomEmails do
  @moduledoc """
  RemoveCustomEmails Mutation Resolvers
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - RemoveCustomEmails mutation"

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}}) do
    with {:get_custom_domain, %CustomDomain{root_domain: root_domain}} <-
           {:get_custom_domain, Gaia.Companies.get_custom_domain_by(%{company_profile_id: id})},
         {:remove_custom_emails, {_count, _results}} <-
           {:remove_custom_emails, Gaia.Comms.delete_all_custom_emails_by(company_profile_id: id)} do
      AmazonWebService.SES.delete_domain_identity(root_domain)
      {:ok, true}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately, we could not remove your custom emails at this time, please try again later."
             )
         }}
    end
  end
end
