defmodule AthenaWeb.Resolvers.Comms.UpsertContactUnsubscribes do
  @moduledoc """
  UpsertContactUnsubscribes Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Repo

  @gettext_context "Athena - UpsertContactUnsubscribes mutation"

  def resolve(_, %{contact_id: contact_id_input, global_unsubscribe: global_unsubscribe, scopes: scopes}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %Contact{company_profile_id: company_profile_id} = contact <-
           contact_id_input
           |> Contacts.get_contact()
           |> Repo.preload([:comms_unsubscribes, :global_unsubscribe]),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, updated_contact} <-
           Contacts.update_contact(contact, %{
             comms_unsubscribes: comms_unsubscribes_attrs(contact, global_unsubscribe, scopes),
             global_unsubscribe: global_unsubscribe_attrs(contact, global_unsubscribe)
           }) do
      {:ok, updated_contact}
    else
      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Unfortunately we could not update the subscription settings at this time, please try again later or contact us for support."
              )
          }
        }
    end
  end

  # If global_unsubscribe true, delete all comms_contact_unsubscribes
  defp comms_unsubscribes_attrs(_contact, true = _global_unsubscribe, _scopes) do
    []
  end

  defp comms_unsubscribes_attrs(
         %Contact{comms_unsubscribes: comms_unsubscribes} = contact,
         false = _global_unsubscribe,
         scopes
       ) do
    # Delete comms_contact_unsubscribes not in scopes
    filtered_comms_unsubscribes =
      comms_unsubscribes
      |> Enum.filter(&(&1.scope in scopes))
      |> Enum.map(&Map.from_struct/1)

    # Insert comms_contact_unsubscribes in scopes that are not yet inserted
    Enum.reduce(scopes, filtered_comms_unsubscribes, fn scope, acc ->
      acc
      |> Enum.find(&(&1.scope == scope))
      |> case do
        nil -> [%{company_profile_id: contact.company_profile_id, contact_id: contact.id, scope: scope} | acc]
        _ -> acc
      end
    end)

    # Do not need to insert when already exists
  end

  defp global_unsubscribe_attrs(%Contact{global_unsubscribe: global_unsubscribe} = contact, true = _global_unsubscribe) do
    case global_unsubscribe do
      nil ->
        %{company_profile_id: contact.company_profile_id, contact_id: contact.id}

      existing ->
        existing
    end
  end

  defp global_unsubscribe_attrs(_contact, false = _global_unsubscribe) do
    nil
  end
end
