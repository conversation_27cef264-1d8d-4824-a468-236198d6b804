defmodule AthenaWeb.Resolvers.Comms.UpsertNotificationPreference do
  @moduledoc """
  UpsertNotificationPreference Mutation Resolvers
  """
  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms.CompanyUsers
  alias Gaia.Comms.CompanyUsers.NotificationPreference

  def upsert_notification_preference(
        _parents,
        %{notification_preference: %{scope: scope} = notification_preference_input},
        %{
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{id: company_user_id}}
          }
        }
      ),
      do: resolve_non_marketing_scope(scope, notification_preference_input, company_user_id)

  def upsert_notification_preference(_parents, _, _), do: {:error, gettext("Oops! Something went wrong.")}

  defp resolve_non_marketing_scope(scope, notification_preference_input, company_user_id) do
    if Enum.member?(NotificationPreference.get_scope_values(), scope) do
      upsert_notification_preference_in_db(notification_preference_input, company_user_id)
    else
      {:error, gettext("Invalid notification preference scope")}
    end
  end

  defp upsert_notification_preference_in_db(%{channel: channel, scope: scope, is_on: is_on}, company_user_id) do
    case CompanyUsers.get_notification_preference_by(
           channel: channel,
           scope: scope,
           company_user_id: company_user_id
         ) do
      nil ->
        create_notification_preference_exec(%{
          channel: channel,
          scope: scope,
          company_user_id: company_user_id,
          is_on: is_on
        })

      %CompanyUsers.NotificationPreference{} = existing ->
        update_notification_preference_exec(existing, %{is_on: is_on})
    end
  end

  defp create_notification_preference_exec(attrs) do
    case CompanyUsers.create_notification_preference(attrs) do
      {:ok, %CompanyUsers.NotificationPreference{}} ->
        {:ok, true}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Could not create notification preference in database")
         }}
    end
  end

  defp update_notification_preference_exec(existing, attrs) do
    case CompanyUsers.update_notification_preference(existing, attrs) do
      {:ok, %CompanyUsers.NotificationPreference{}} ->
        {:ok, true}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Could not update notification preference in database")
         }}
    end
  end
end
