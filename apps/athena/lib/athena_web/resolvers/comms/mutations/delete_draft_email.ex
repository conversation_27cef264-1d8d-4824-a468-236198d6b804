defmodule AthenaWeb.Resolvers.Comms.DeleteDraftEmail do
  @moduledoc """
    DeleteDraftEmail Mutation Resolvers
  """

  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - DeleteDraftEmail mutation"

  def resolve(_, %{id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_draft_email, %Email{company_profile_id: company_profile_id, is_draft: true} = email} <-
           {:get_draft_email, Comms.get_email(email_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:invalidate_email, {:ok, %Email{}}} <-
           {:invalidate_email,
            Comms.update_email(email, %{
              invalidated: true,
              last_updated_by: company_profile_user_id
            })} do
      {:ok, true}
    else
      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Unfortunately we could not delete an email campaign at this time, please try again later or contact us for support."
              )
          }
        }
    end
  end
end
