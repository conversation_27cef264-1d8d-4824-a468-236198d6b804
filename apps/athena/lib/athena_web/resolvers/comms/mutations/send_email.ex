defmodule AthenaWeb.Resolvers.Comms.Mutations.SendEmail do
  @moduledoc """
    SendEmail Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions.Media
  alias Gaia.Repo

  @gettext_context "Athena - SendEmail mutation"

  def resolve(_, %{email_id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_email,
          %Email{
            company_profile_id: company_profile_id,
            sent_at: sent_at
          } = email} <-
           {:get_email,
            email_id
            |> Comms.get_email()
            |> Repo.preload([:email_recipients, :media])},
         {:is_not_sent, true} <- {:is_not_sent, is_nil(sent_at)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:queue_and_update_email, {:ok, %Email{} = queued_email}} <-
           {:queue_and_update_email, queue_and_update_email(email, company_profile_user_id)} do
      {:ok, queued_email}
    else
      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Cannot send email campaign."
              )
          }
        }
    end
  end

  defp queue_and_update_email(%Email{id: email_id, scheduled_at: scheduled_at} = email, last_updated_by)
       when is_nil(scheduled_at) do
    %{"email_id" => email_id}
    |> Gaia.Jobs.SendEmail.enqueue()
    |> case do
      {:ok, _} ->
        update_email(email, %{sent_at: NaiveDateTime.utc_now(:second), last_updated_by: last_updated_by})

      _ ->
        {:error, "Failed to send email campaign"}
    end
  end

  defp queue_and_update_email(%Email{id: email_id, scheduled_at: scheduled_at} = email, last_updated_by) do
    %{"email_id" => email_id}
    |> Gaia.Jobs.SendEmail.enqueue(scheduled_at: DateTime.from_naive!(scheduled_at, "Etc/UTC"))
    |> case do
      {:ok, _} ->
        update_email(email, %{last_updated_by: last_updated_by})

      _ ->
        {:error, "Failed to send email campaign"}
    end
  end

  defp update_email_and_media_email_distribution_method(%Email{media: %Media{} = media} = email, update_email_input) do
    email_changeset = Ecto.Changeset.change(email, update_email_input)

    media_changeset = Ecto.Changeset.change(media, email_distribution_method: :manual)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update_email, email_changeset)
    |> Ecto.Multi.update(:update_media_email_distribution_method, media_changeset)
    |> Repo.transaction()
    |> case do
      {:ok, %{update_email: updated_email}} -> {:ok, updated_email}
      error -> error
    end
  end

  defp update_email(%Email{media: %Media{} = _media} = email, update_email_input) do
    update_email_and_media_email_distribution_method(email, update_email_input)
  end

  defp update_email(%Email{} = email, update_email_input) do
    Comms.update_email(email, update_email_input)
  end
end
