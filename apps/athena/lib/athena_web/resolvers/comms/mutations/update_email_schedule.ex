defmodule AthenaWeb.Resolvers.Comms.UpdateEmailSchedule do
  @moduledoc """
  UpdateEmailSchedule Mutation Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.ObanHelper

  @gettext_context "Athena - UpdateEmailSchedule mutation"

  def resolve(_, %{email: %{scheduled_at: new_scheduled_at} = email_input, email_id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_email,
          %Email{
            company_profile_id: company_profile_id,
            sent_at: sent_at
          } = email} <-
           {:get_email, Gaia.Comms.get_email(email_id)},
         {:is_not_sent, true} <- {:is_not_sent, is_nil(sent_at)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:maybe_reschedule, :ok} <-
           {:maybe_reschedule, maybe_reschedule(email, new_scheduled_at)},
         {:ok, %Gaia.Comms.Email{} = updated_email} <-
           Comms.update_email(
             email,
             Map.put(email_input, :last_updated_by, company_profile_user_id)
           ) do
      {:ok, updated_email}
    else
      {:get_email, nil} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Email campaign does not exist.")
         }}

      {:is_not_sent, _} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Email campaign has already been sent.")
         }}

      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              dpgettext(
                "errors",
                @gettext_context,
                "Cannot update email campaign."
              )
          }
        }
    end
  end

  def resolve(_, %{email: email_input, email_id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_email,
          %Email{
            company_profile_id: company_profile_id,
            sent_at: sent_at
          } = email} <-
           {:get_email, Gaia.Comms.get_email(email_id)},
         {:is_not_sent, true} <- {:is_not_sent, is_nil(sent_at)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %Email{} = updated_email} <-
           Comms.update_email(
             email,
             Map.put(email_input, :last_updated_by, company_profile_user_id)
           ) do
      {:ok, updated_email}
    else
      {:get_email, nil} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Campaign does not exist")
         }}

      {:is_not_sent, _} ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Email campaign has already been sent")
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           message: gettext("Cannot update email campaign"),
           error: error
         }}
    end
  end

  defp maybe_reschedule(%Email{scheduled_at: scheduled_at, sent_at: sent_at}, nil)
       when is_nil(scheduled_at) and is_nil(sent_at) do
    :ok
  end

  # Cancel scheduled email campaign if new_scheduled_at is nil
  defp maybe_reschedule(%Email{id: email_id, scheduled_at: scheduled_at, sent_at: sent_at}, nil)
       when not is_nil(scheduled_at) and is_nil(sent_at) do
    cancel_scheduled_email(email_id)
  end

  # Reschedule email campaign if new_scheduled_at is not nil
  defp maybe_reschedule(
         %Email{id: email_id, scheduled_at: scheduled_at, sent_at: sent_at},
         %NaiveDateTime{} = new_scheduled_at
       )
       when not is_nil(scheduled_at) and is_nil(sent_at) do
    cancel_and_reschedule_email(email_id, new_scheduled_at)
  end

  defp maybe_reschedule(_, _), do: :ok

  defp cancel_scheduled_email(email_id) do
    email_id
    |> ObanHelper.cancel_email_jobs()
    |> case do
      cancelled_jobs when is_list(cancelled_jobs) -> :ok
      error -> error
    end
  end

  defp cancel_and_reschedule_email(email_id, %NaiveDateTime{} = scheduled_at) do
    email_id
    |> ObanHelper.cancel_email_jobs()
    |> ObanHelper.reschedule_email_jobs(email_id, scheduled_at)
  end
end
