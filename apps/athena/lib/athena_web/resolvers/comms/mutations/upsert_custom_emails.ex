defmodule AthenaWeb.Resolvers.Comms.UpsertCustomEmails do
  @moduledoc """
  UpsertCustomEmails Mutation Resolvers

  Check the following conditions before upsert_custom_emails
  1. Check custom_domain is not nil
  2. Check Vercel custom_domain is configured
  3. Both send_from_email and reply_to_email end with custom_domain
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.CustomEmail
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @gettext_context "Athena - upsert custom email mutation"

  def resolve(_, %{custom_emails: custom_emails}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id} = profile}}
      }) do
    with {:get_custom_domain, %CustomDomain{custom_domain: custom_domain, root_domain: root_domain}} <-
           {:get_custom_domain,
            profile
            |> Repo.preload(:custom_domain)
            |> Map.get(:custom_domain)},
         {:get_is_custom_domain_configured_on_vercel, {:ok, true}} <-
           {:get_is_custom_domain_configured_on_vercel, Vercel.Domain.get_is_configured(custom_domain)},
         {:get_is_custom_domain_configured_on_ses, {:ok, true}} <-
           {:get_is_custom_domain_configured_on_ses, AmazonWebService.SES.is_dkim_verified(root_domain)},
         {:upsert_custom_emails, [{:ok, %CustomEmail{}} | _]} <-
           {:upsert_custom_emails, Enum.map(custom_emails, &Comms.upsert_custom_email_by_type(&1, company_profile_id))} do
      {:ok, true}
    else
      {:get_custom_domain, nil} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Please set up custom domain first"
         )}

      {:get_is_custom_domain_configured_on_vercel, _} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Custom domain is not connected"
         )}

      {:get_is_custom_domain_configured_on_ses, _} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Custom domain is not connected"
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unfortunately we could not update the custom email at this time, please check your input and try again later."
             )
         }}
    end
  end
end
