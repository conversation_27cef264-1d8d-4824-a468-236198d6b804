defmodule AthenaWeb.Resolvers.Companies.LinkedinOrganisations do
  @moduledoc """
  LinkedinOrganisations Query Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection
  alias Gaia.Socials
  alias Socials.LinkedIn

  @gettext_context "LinkedinOrganisations query"

  def find_linkedin_organisations(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{social_connection: %SocialConnection{linkedin_access_token: access_token}}
          }
        }
      }) do
    case LinkedIn.find_linkedin_organisations(access_token) do
      {:ok, organisations} ->
        {:ok, organisations}

      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: dpgettext("errors", @gettext_context, "Unable to retrieve LinkedIn organisations")
         }}
    end
  end

  def find_linkedin_organisations(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to retrieve LinkedIn organisations")
     }}
  end
end
