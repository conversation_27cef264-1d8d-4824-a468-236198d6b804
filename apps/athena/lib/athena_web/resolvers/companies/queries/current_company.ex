defmodule AthenaWeb.Resolvers.Companies.CurrentCompany do
  @moduledoc """
  CurrentCompany Query Resolver
  """

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{} = current_company_profile
          }
        }
      }),
      do: {:ok, current_company_profile}

  def resolve(_parent, _args, _resolution), do: {:ok, nil}
end
