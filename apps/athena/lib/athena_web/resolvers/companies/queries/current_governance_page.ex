defmodule AthenaWeb.Resolvers.Companies.CurrentGovernancePage do
  @moduledoc false

  alias Gaia.Companies
  alias Gaia.Companies.CorporatePage
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Companies.get_corporate_page_by(%{company_profile_id: company_profile_id, type: :governance}) do
      %CorporatePage{} = governance_page ->
        {:ok, governance_page}

      _ ->
        {:ok, nil}
    end
  end
end
