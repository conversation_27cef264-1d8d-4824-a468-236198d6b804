defmodule AthenaWeb.Resolvers.Companies.RegistryStatus do
  @moduledoc """
  Resolver for the RegistryStatus query
  """

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.RegistryCredential

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    %{company_profile_id: company_profile_id}
    |> Companies.get_registry_credential_by()
    |> get_status_from_registry_credential()
  end

  def resolve(_, _, _), do: {:ok, %{status: :none, expiry_date: nil}}

  defp get_status_from_registry_credential(%RegistryCredential{method: :oauth, service: :automic}) do
    {:ok, %{status: :valid}}
  end

  defp get_status_from_registry_credential(_), do: {:ok, %{status: :none, expiry_date: nil}}
end
