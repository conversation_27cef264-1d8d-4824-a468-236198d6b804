defmodule AthenaWeb.Resolvers.Companies.CurrentCompanyProfileUser do
  @moduledoc """
  CurrentCompanyProfileUser Query Resolver
  """

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{} = current_company_profile_user}
      }) do
    {:ok, current_company_profile_user}
  end

  # Fallback for when there's no current_company_profile_user or it doesn't match the expected pattern
  def resolve(_parent, _args, _resolution) do
    {:ok, nil}
  end
end
