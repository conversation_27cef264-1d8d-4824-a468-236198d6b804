defmodule AthenaWeb.Resolvers.Companies.CurrentWelcomePage do
  @moduledoc false

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.WelcomePage

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Companies.get_welcome_page_by(profile_id: company_profile_id) do
      %WelcomePage{} = welcome_page ->
        {:ok, welcome_page}

      _ ->
        {:ok, nil}
    end
  end
end
