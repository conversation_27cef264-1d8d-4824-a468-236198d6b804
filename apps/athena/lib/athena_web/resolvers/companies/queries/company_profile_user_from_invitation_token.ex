defmodule AthenaWeb.Resolvers.Companies.CompanyProfileUserFromInvitationToken do
  @moduledoc """
   CompanyProfileUserFromInvitationToken Query Resolvers
  """
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_, %{token: token}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{id: current_company_profile_id}}
      }),
      do: get_company_profile_user_from_invitation_token_exec(token, current_company_profile_id)

  def resolve(_, %{token: token}, _), do: get_company_profile_user_from_invitation_token_exec(token, nil)

  def get_company_profile_user_from_invitation_token_exec(token, current_company_profile_id) do
    case Gaia.Companies.get_profile_user_by_invitation_token(token) do
      %Gaia.Companies.ProfileUser{id: company_profile_id} = company_profile_user
      when company_profile_id == current_company_profile_id or is_nil(current_company_profile_id) ->
        {:ok, Map.put(company_profile_user, :expired, false)}

      _error ->
        get_expired_company_profile_user_from_invitation_token(token, current_company_profile_id)
    end
  end

  defp get_expired_company_profile_user_from_invitation_token(token, current_company_profile_id) do
    case Gaia.Companies.get_profile_user_by_invitation_token_without_expiry(token) do
      %Gaia.Companies.ProfileUser{id: company_profile_id} = company_profile_user
      when company_profile_id == current_company_profile_id or is_nil(current_company_profile_id) ->
        {:ok, Map.put(company_profile_user, :expired, true)}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Your invitation token is invalid! Please ask your admin to invite you again")
         }}
    end
  end
end
