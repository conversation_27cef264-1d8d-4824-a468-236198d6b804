defmodule AthenaWeb.Resolvers.Companies.CompanyProfileUsers do
  @moduledoc """
    CompanyProfileUsers Query Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @gettext_context "Company profile users query"

  def resolve(_parents, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    options = Map.get(args, :options, %{})

    connection_result =
      options
      |> add_default_filters(company_profile_id)
      |> Companies.profile_users_query()
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get company profile users"
             )
         }}
    end
  end

  def total(_parents, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    options = Map.get(args, :options, %{})

    length =
      options
      |> add_default_filters(company_profile_id)
      |> Companies.profile_users_query()
      |> Repo.aggregate(:count)

    {:ok, length}
  end

  defp add_default_filters(options, company_profile_id) do
    filters = Map.get(options, :filters, [])

    default_filters =
      [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "type", value: "non_fresh"}
      ]

    Map.put(options, :filters, default_filters ++ filters)
  end
end
