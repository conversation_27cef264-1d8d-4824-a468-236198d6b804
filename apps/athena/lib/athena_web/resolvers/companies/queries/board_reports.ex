defmodule AthenaWeb.Resolvers.Companies.Queries.BoardReports do
  @moduledoc """
    List all generated board_reports
  """
  def resolve(_parents, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile_id: company_profile_id,
            id: company_profile_user_id
          }
        }
      }),
      do: {:ok, Gaia.Companies.list_companies_board_reports_by_owner(company_profile_id, company_profile_user_id)}
end
