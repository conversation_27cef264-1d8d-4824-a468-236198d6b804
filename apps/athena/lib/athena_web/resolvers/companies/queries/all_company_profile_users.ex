defmodule AthenaWeb.Resolvers.Companies.AllCompanyProfileUsers do
  @moduledoc """
    AllCompanyProfileUsers Query Resolver
  """

  import Ecto.Query, warn: false

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  def resolve(_parents, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    all_company_profile_users =
      %{
        filters: [
          %{key: "company_profile_id", value: company_profile_id},
          %{key: "type", value: "non_fresh"}
        ]
      }
      |> Companies.profile_users_query()
      |> Repo.all()

    {:ok, all_company_profile_users}
  end
end
