defmodule AthenaWeb.Resolvers.Companies.CurrentCompanyUser do
  @moduledoc """
    CurrentCompanyUser Query Resolvers
  """
  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{} = company_user}}
      }),
      do: {:ok, company_user}

  def resolve(_parent, _args, %{context: %{current_company_user: %Gaia.Companies.User{} = company_user}}),
    do: {:ok, company_user}

  def resolve(_parent, _args, _resolution), do: {:ok, nil}
end
