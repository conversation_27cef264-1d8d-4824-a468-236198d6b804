defmodule AthenaWeb.Resolvers.Companies.IsFeatureOnboarded do
  @moduledoc """
  IsFeatureOnboarded Query Resolver
  """
  def resolve(_parents, %{feature_name: feature_name}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{id: user_id}}}
      }) do
    %{feature_name: feature_name, user_id: user_id}
    |> Gaia.Companies.get_feature_onboarding_by()
    |> case do
      %Gaia.Companies.FeatureOnboarding{onboarded_at: onboarded_at}
      when not is_nil(onboarded_at) ->
        {:ok, true}

      _ ->
        {:ok, false}
    end
  end
end
