defmodule AthenaWeb.Resolvers.Companies.CurrentCorporatePages do
  @moduledoc false

  alias Gaia.Companies
  alias Gaia.Companies.CorporatePage
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Companies.list_corporate_pages_by_company_profile_id(company_profile_id) do
      [%CorporatePage{} | _] = corporate_pages ->
        {:ok, corporate_pages}

      _ ->
        {:ok, []}
    end
  end
end
