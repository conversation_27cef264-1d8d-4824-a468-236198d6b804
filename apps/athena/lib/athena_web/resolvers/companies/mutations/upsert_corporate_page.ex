defmodule AthenaWeb.Resolvers.Companies.UpsertCorporatePage do
  @moduledoc """
  UpdateCorporatePageStatus Mutation
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.CorporatePage
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User

  @gettext_context "UpdateCorporatePageStatus mutation"

  def resolve(_parent, %{corporate_page: corporate_page_input}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: user_id}
          }
        }
      }) do
    case corporate_page_input
         |> Map.merge(%{
           slug: corporate_page_input |> Map.get(:slug) |> Slug.slugify(),
           last_edited_by_user_id: user_id,
           company_profile_id: current_company_profile_id
         })
         |> Companies.upsert_corporate_page() do
      {:ok, %CorporatePage{} = upserted_corporate_page} ->
        {:ok, upserted_corporate_page}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not update corporate page status"
             )
         }}
    end
  end
end
