defmodule AthenaWeb.Resolvers.Companies.SendTestEmailDomainConnectionEmail do
  @moduledoc """
  SendTestEmailDomainConnectionEmail Mutation Resolver
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Repo

  def resolve(_parent, %{email_address: email_address}, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile_id: current_company_profile_id, user: %User{}} = company_profile_user
        }
      }) do
    Task.start(fn ->
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Company,
        :test_email_domain_connection,
        [Repo.preload(company_profile_user, profile: [:custom_emails, :ticker]), email_address],
        current_company_profile_id
      )
    end)

    {:ok, true}
  end
end
