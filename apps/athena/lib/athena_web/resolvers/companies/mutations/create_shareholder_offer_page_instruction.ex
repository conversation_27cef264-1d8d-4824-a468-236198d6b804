defmodule AthenaWeb.Resolvers.Companies.CreateShareholderOfferPageInstruction do
  @moduledoc """
  CreateShareholderOfferPageInstruction mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPages
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Repo
  alias ShareholderOfferPages.Instruction

  @gettext_context "CreateShareholderOfferPageInstruction mutation resolver"

  def resolve(_, %{instruction: instruction_input, shareholder_offer_id: shareholder_offer_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: user_id}
          }
        }
      }) do
    with %ShareholderOffer{
           company_profile_id: company_profile_id
         } = shareholder_offer <-
           shareholder_offer_id
           |> Raises.get_shareholder_offer()
           |> Repo.preload(:company_shareholder_offer_page),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, shareholder_offer_page_id} <-
           Companies.get_shareholder_offer_page_id(shareholder_offer, user_id),
         total_instructions = Companies.get_total_instructions_by_shareholder_offer_page_id(shareholder_offer_page_id),
         {:ok, %Instruction{} = created_instruction} <-
           instruction_input
           |> Map.merge(%{
             shareholder_offer_page_id: shareholder_offer_page_id,
             order_id: total_instructions + 1,
             last_edited_by_user_id: user_id
           })
           |> Companies.create_instruction() do
      {:ok, created_instruction}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not create instruction"
             )
         }}
    end
  end
end
