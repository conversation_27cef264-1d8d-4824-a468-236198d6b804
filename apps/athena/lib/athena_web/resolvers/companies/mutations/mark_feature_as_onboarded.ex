defmodule AthenaWeb.Resolvers.Companies.MarkFeatureAsOnboarded do
  @moduledoc """
  MarkFeatureAsOnboarded mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  @gettext_context "MarkFeatureAsOnboarded mutation resolver"

  def resolve(_, %{feature_name: feature_name}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{id: user_id}}}
      }) do
    %{feature_name: feature_name, onboarded_at: NaiveDateTime.utc_now(:second), user_id: user_id}
    |> Gaia.Companies.create_feature_onboarding()
    |> case do
      {:ok, %Gaia.Companies.FeatureOnboarding{} = feature_onboarding} ->
        {:ok, feature_onboarding}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not mark feature as onboarded for company user"
             )
         }}
    end
  end
end
