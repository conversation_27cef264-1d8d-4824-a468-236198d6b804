defmodule AthenaWeb.Resolvers.Companies.PublishWelcomePage do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Markets.Ticker

  @gettext_context "Athena - PublishWelcomePage mutation"

  def resolve(
        _parent,
        %{
          welcome_page: %{
            author: author,
            banner: banner,
            linkedin_profile_url: linkedin_profile_url,
            profile_picture: profile_picture,
            signature: signature,
            title: title,
            welcome_message: welcome_message
          }
        },
        %{
          context: %{
            current_company_profile_user: %ProfileUser{
              id: company_profile_user_id,
              user: %User{first_name: updated_by_first_name, last_name: updated_by_last_name},
              profile: %Profile{id: company_profile_id}
            }
          }
        }
      ) do
    with {invalidated_count, _} <-
           Companies.invalidate_all_welcome_page_by_profile_id(company_profile_id),
         {:ok, welcome_page} <-
           Companies.create_welcome_page(%{
             author: author,
             linkedin_profile_url: linkedin_profile_url,
             profile_id: company_profile_id,
             publisher_id: company_profile_user_id,
             title: title,
             welcome_message: welcome_message
           }),
         {:ok, updated_welcome_page} <-
           Companies.update_welcome_page(welcome_page, %{
             banner: banner,
             profile_picture: profile_picture,
             signature: signature
           }) do
      send_published_welcome_page_emails(
        updated_welcome_page,
        invalidated_count,
        "#{updated_by_first_name} #{updated_by_last_name}"
      )

      {:ok, updated_welcome_page}
    else
      _ ->
        {
          :error,
          dpgettext(
            "errors",
            @gettext_context,
            "Unable to publish welcome page. Please try again later."
          )
        }
    end
  end

  def send_published_welcome_page_emails(%Companies.WelcomePage{} = welcome_page, invalidated_count, updated_by_name) do
    %{
      profile:
        %{
          id: company_profile_id,
          ticker: %{} = ticker,
          custom_domain: custom_domain
        } = profile
    } = Gaia.Repo.preload(welcome_page, profile: [:ticker, :custom_domain])

    # Get welcome page live url
    live_url =
      get_welcome_page_live_url(
        ticker,
        custom_domain
      )

    admin_users = Companies.subscribed_profile_users_with_preloads(company_profile_id, :welcome_page, :email)

    # Check if a welcome page has been invalidated, if not send new email, if so send updated email
    invalidated_count
    |> Kernel.==(0)
    |> case do
      true ->
        # Send new welcome page published email to InvestorHub as none invalidated
        Gaia.Notifications.Email.deliver(EmailTransactional.Operations, :welcome_page_activated, [profile, live_url])

        # Send new welcome page published email to Profile admins as non invalidated
        send_welcome_page_emails_to_profile_admins(
          admin_users,
          live_url,
          updated_by_name,
          :new
        )

      false ->
        # Send editied welcome page published email to Profile admins as non invalidated
        send_welcome_page_emails_to_profile_admins(
          admin_users,
          live_url,
          updated_by_name,
          :updated
        )
    end

    {:ok, true}
  end

  # Send new welcome page emails to profile admins, if there are admins
  defp send_welcome_page_emails_to_profile_admins([_ | _] = admin_profile_users, live_url, _, :new) do
    Enum.each(admin_profile_users, fn admin_profile_user ->
      Gaia.Notifications.Email.deliver_with_permission_check(
        EmailTransactional.Company,
        :welcome_page_activated,
        [admin_profile_user, live_url],
        admin_profile_user,
        "websites.admin",
        admin_profile_user.profile.id
      )
    end)
  end

  # Send update welcome page emails to profile admins, if there are admins
  defp send_welcome_page_emails_to_profile_admins([_ | _] = admin_profile_users, live_url, updated_by_name, :updated) do
    Enum.each(admin_profile_users, fn admin_profile_user ->
      Gaia.Notifications.Email.deliver_with_permission_check(
        EmailTransactional.Company,
        :welcome_page_edited,
        [admin_profile_user, live_url, updated_by_name],
        admin_profile_user,
        "websites.admin",
        admin_profile_user.profile.id
      )
    end)
  end

  defp send_welcome_page_emails_to_profile_admins(_, _, _, _) do
    {:ok, true}
  end

  # If the company has a custom domain and it is configured
  defp get_welcome_page_live_url(%Ticker{} = ticker, %CustomDomain{custom_domain: custom_domain}) do
    custom_domain
    |> Vercel.Domain.get_is_configured()
    |> case do
      {:ok, true} -> "https://#{custom_domain}/welcome"
      _ -> get_welcome_page_live_url(ticker, nil)
    end
  end

  # If the company does not have a custom domain
  defp get_welcome_page_live_url(%Ticker{} = ticker, _),
    do: "#{Helper.InvestorHub.get_base_url(Ticker.resolve_market_listing_key(ticker))}/welcome"
end
