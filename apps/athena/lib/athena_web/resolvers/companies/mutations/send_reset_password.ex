defmodule AthenaWeb.Resolvers.Companies.SendResetPassword do
  @moduledoc """
  SendResetPassword Mutation Resolver
  """

  def resolve(_parent, %{email: email}, _resolution) do
    with %Gaia.Companies.User{} = user <- Gaia.Companies.get_user_by_email(email),
         false <- Gaia.Companies.is_user_sso_only?(user),
         {:ok, _} <- Gaia.Companies.deliver_user_reset_password_instructions(user) do
      {:ok, true}
    else
      true ->
        {:error, "Passwords are disabled for this domain, please use Google or Microsoft SSO instead."}
    end
  end
end
