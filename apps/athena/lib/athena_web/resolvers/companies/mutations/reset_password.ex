defmodule AthenaWeb.Resolvers.Companies.ResetPassword do
  @moduledoc """
  ResetPassword Mutation Resolver
  """

  def resolve(_parent, %{password: password, password_confirmation: password_confirmation, token: token}, _resolution) do
    with %Gaia.Companies.User{} = company_user <-
           Gaia.Companies.get_user_by_reset_password_token(token),
         {:ok, _} <-
           Gaia.Companies.reset_user_password(company_user, %{
             password: password,
             password_confirmation: password_confirmation
           }),
         {:ok, _} <-
           Gaia.Notifications.Email.deliver(EmailTransactional.Company, :password_reset_confirmation, [company_user]) do
      {:ok, true}
    else
      nil ->
        {:error, "Token is invalid."}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
