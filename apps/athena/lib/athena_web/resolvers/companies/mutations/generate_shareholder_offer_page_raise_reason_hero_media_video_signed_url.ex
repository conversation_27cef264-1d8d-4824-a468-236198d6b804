defmodule AthenaWeb.Resolvers.Companies.GenerateShareholderOfferPageRaiseReasonHeroMediaVideoSignedUrl do
  @moduledoc """
  GenerateShareholderOfferPageRaiseReasonHeroMediaVideoSignedUrl Mutation
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Repo

  @gettext_context "GenerateShareholderOfferPageRaiseReasonHeroMediaVideoSignedUrl mutation"

  def resolve(_parent, %{file_size: file_size, mime_type: mime_type, shareholder_offer_id: shareholder_offer_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: user_id}
          }
        }
      })
      when file_size <= 100_000_000 do
    with %ShareholderOffer{
           company_profile_id: company_profile_id
         } = shareholder_offer <-
           shareholder_offer_id
           |> Raises.get_shareholder_offer()
           |> Repo.preload(:company_shareholder_offer_page),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %ShareholderOfferPage{id: shareholder_offer_page_id} = shareholder_offer_page} <-
           Companies.get_shareholder_offer_page(shareholder_offer, user_id),
         %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{company_profile_id}/shareholder_offer_page/#{shareholder_offer_page_id}/raise_reason_hero_media_video/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ),
         {:ok, %URI{host: host, path: path, scheme: scheme}} <- URI.new(url),
         {:ok, _} <-
           Companies.update_shareholder_offer_page(shareholder_offer_page, %{
             raise_reason_hero_media_video_url: "#{scheme}://#{host}#{path}"
           }) do
      {:ok, url}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not create hero media video"
             )
         }}
    end
  end
end
