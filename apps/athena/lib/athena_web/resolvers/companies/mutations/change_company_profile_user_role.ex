defmodule AthenaWeb.Resolvers.Companies.ChangeCompanyProfileUserRole do
  @moduledoc """
    ChangeCompanyProfileUserRole Mutation Resolver
  """
  def resolve(_, %{profile_user_id: company_profile_user_id, role_id: companies_role_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:check_profile_user, %Gaia.Companies.ProfileUser{profile_id: company_profile_id} = company_profile_user} <-
           {:check_profile_user, Gaia.Companies.get_profile_user(company_profile_user_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:check_role_exist?, %Gaia.Companies.Role{}} <-
           {:check_role_exist?, Gaia.Companies.get_role(companies_role_id)},
         {:ok, updated_company_profile_user} <-
           Gaia.Companies.update_profile_user(company_profile_user, %{companies_role_id: companies_role_id}) do
      {:ok, updated_company_profile_user}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
