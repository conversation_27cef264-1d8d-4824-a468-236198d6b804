defmodule AthenaWeb.Resolvers.Companies.RemoveCustomDomain do
  @moduledoc """
    RemoveCustomDomain Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker

  def resolve(_, _, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile: %Profile{id: id, ticker: %Ticker{} = ticker}} = company_profile_user
        }
      }) do
    with %CustomDomain{} = existing <- Companies.get_custom_domain_by(%{company_profile_id: id}),
         {:ok, _} <-
           Companies.remove_custom_domain_from_vercel_and_invalidate(
             existing,
             ticker
           ) do
      send_confirmation_email(company_profile_user, existing)
      {:ok, nil}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Unable to remove custom domain")
         }}
    end
  end

  defp send_confirmation_email(%ProfileUser{} = company_profile_user, custom_domain) do
    Gaia.Notifications.Email.deliver_with_permission_check(
      EmailTransactional.Company,
      :custom_domain_removed,
      [company_profile_user, custom_domain],
      company_profile_user,
      "websites.admin",
      company_profile_user.profile.id,
      false
    )
  end
end
