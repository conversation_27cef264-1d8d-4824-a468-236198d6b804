defmodule AthenaWeb.Resolvers.Companies.SwitchCustomDomain do
  @moduledoc """
  SwitchCustomDomain Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Companies
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.CustomDomainSwap
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker

  @gettext_context "Athena - SwitchCustomDomain mutation"

  def resolve(_, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id, ticker: %Ticker{} = ticker}}}
      }) do
    with {:get_existing_domain,
          %CustomDomain{custom_domain: existing_custom_domain, root_domain: existing_root_domain} = existing_domain} <-
           {:get_existing_domain, Companies.get_custom_domain_by(%{company_profile_id: id})},
         {:get_existing_swap, %CustomDomainSwap{} = swap_domain} <-
           {:get_existing_swap, Companies.get_custom_domain_swap_by(%{company_profile_id: id})},
         {:is_new_domain_verified, true} <- {:is_new_domain_verified, Companies.is_custom_domain_verified?(swap_domain)},
         {:create_from_swap,
          {:ok,
           %CustomDomain{custom_domain: new_custom_domain, root_domain: new_root_domain, invalidated: true} = new_domain}} <-
           {:create_from_swap, Companies.create_custom_domain_from_swap(swap_domain)},
         {:handle_vercel_redirects, true} <-
           {:handle_vercel_redirects,
            handle_vercel_redirects(
              new_custom_domain,
              existing_custom_domain,
              ticker,
              new_custom_domain == new_root_domain,
              existing_custom_domain == existing_root_domain
            )},
         maybe_delete_custom_emails(existing_root_domain, new_root_domain, id),
         {:delete_swap, {:ok, _}} <- {:delete_swap, Companies.delete_custom_domain_swap(swap_domain)},
         {:delete_existing, {:ok, _}} <- {:delete_existing, Companies.delete_custom_domain(existing_domain)},
         {:restore_new, {:ok, _}} <- {:restore_new, Companies.restore_custom_domain(new_domain)} do
      {:ok, new_domain}
    else
      {:handle_vercel_redirects, {vercel_step, vercel_result, args}} ->
        revert_vercel_redirects(vercel_step, args)
        return_error(vercel_result, :handle_vercel_redirects)

      {step, error} ->
        return_error(error, step)
    end
  end

  defp handle_vercel_redirects(new_domain, existing_domain, ticker, new_domain_is_root, existing_domain_is_root) do
    market_listing_key = Ticker.resolve_market_listing_key(ticker)

    with {:ok, true} <- Vercel.Domain.get_is_configured(new_domain),
         {:remove_ticker_redirect, {:ok, _}} <-
           {:remove_ticker_redirect, Vercel.remove_ticker_redirect(market_listing_key)},
         {:remove_www_redirect, {:ok, _}} <-
           {:remove_www_redirect, Vercel.remove_www_redirect(existing_domain)},
         {:add_ticker_redirect, {:ok, _}} <-
           {:add_ticker_redirect, Vercel.redirect_ticker_to_domain(market_listing_key, new_domain)},
         {:add_www_redirect, {:ok, _}} <-
           {:add_www_redirect, Vercel.add_www_redirect(new_domain, new_domain_is_root)},
         {:add_old_domain_redirect, {:ok, _}} <-
           {:add_old_domain_redirect, Vercel.Domain.assign_redirect(existing_domain, new_domain)} do
      true
    else
      {step, result} ->
        {step, result, {new_domain, existing_domain, market_listing_key, existing_domain_is_root}}
    end
  end

  defp revert_vercel_redirects(:remove_ticker_redirect, _args) do
    # OK if it fails at this point, nothing to revert
    :ok
  end

  defp revert_vercel_redirects(:remove_www_redirect, {_new_domain, existing_domain, market_listing_key, _domain_is_root}) do
    Task.start(fn ->
      Vercel.redirect_ticker_to_domain(market_listing_key, existing_domain)
    end)
  end

  defp revert_vercel_redirects(:add_ticker_redirect, {_new_domain, existing_domain, market_listing_key, domain_is_root}) do
    Task.start(fn ->
      Vercel.add_www_redirect(existing_domain, domain_is_root)
      Vercel.redirect_ticker_to_domain(market_listing_key, existing_domain)
    end)
  end

  defp revert_vercel_redirects(:add_www_redirect, {_new_domain, existing_domain, market_listing_key, domain_is_root}) do
    Task.start(fn ->
      Vercel.remove_ticker_redirect(market_listing_key)
      Vercel.add_www_redirect(existing_domain, domain_is_root)
      Vercel.redirect_ticker_to_domain(market_listing_key, existing_domain)
    end)
  end

  defp revert_vercel_redirects(
         :add_old_domain_redirect,
         {new_domain, existing_domain, market_listing_key, domain_is_root}
       ) do
    Task.start(fn ->
      Vercel.remove_www_redirect(new_domain)
      Vercel.remove_ticker_redirect(market_listing_key)
      Vercel.add_www_redirect(existing_domain, domain_is_root)
      Vercel.redirect_ticker_to_domain(market_listing_key, existing_domain)
    end)
  end

  defp maybe_delete_custom_emails(existing_root_domain, new_root_domain, id) do
    # Keep the custom emails if the root domain is the same
    if new_root_domain != existing_root_domain do
      Comms.delete_all_custom_emails_by(company_profile_id: id)
    end
  end

  defp return_error({_, error}, step), do: return_error(error, step)

  defp return_error(error, step) do
    {:error,
     %Helper.AbsintheError{
       error: "Error on switch_custom_domain mutation at step #{step}: #{inspect(error)}",
       message:
         dpgettext(
           "errors",
           @gettext_context,
           "Unfortunately, we could not create your custom domain at this time, please try again later."
         )
     }}
  end
end
