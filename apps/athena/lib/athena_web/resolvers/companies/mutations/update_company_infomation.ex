defmodule AthenaWeb.Resolvers.Companies.UpdateCompanyInformation do
  @moduledoc """
    UpdateCompanyInformation Resolvers
  """
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_parents, %{profile: profile_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{} = current_company_profile
          }
        }
      }) do
    case Gaia.Companies.update_profile(current_company_profile, profile_input) do
      {:ok, %Gaia.Companies.Profile{} = profile} ->
        {:ok, profile}

      {:error, :profile} ->
        {:error, gettext("can not update company profile")}

      {:error, :invalidate_existing_ticker} ->
        {:error, gettext("can not invalidate existing ticker")}

      {:error, :ticker} ->
        {:error, gettext("can not create new ticker")}
    end
  end
end
