defmodule AthenaWeb.Resolvers.Companies.ConfirmCompanyProfileInformation do
  @moduledoc """
    ConfirmCompanyProfileInformation Resolvers
  """
  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_parents, %{listing_key: listing_key, name: name}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{information_confirmed_at: information_confirmed_at} = current_company_profile
          }
        }
      })
      when is_nil(information_confirmed_at) do
    Ecto.Multi.new()
    |> maybe_update_company_profile_name(current_company_profile, name)
    |> maybe_update_company_ticker(
      Gaia.Repo.preload(current_company_profile, [:ticker]),
      listing_key
    )
    |> update_company_confirmed_at(current_company_profile)
    |> Gaia.Repo.transaction()
    |> case do
      {:ok, %{profile: %Gaia.Companies.Profile{id: profile_id}}} ->
        {:ok, Gaia.Companies.get_profile(profile_id)}

      {:ok, _} ->
        {:ok, current_company_profile}

      {:error, :update_name, _, _} ->
        {:error, gettext("can not update company name")}

      {:error, key, _, _} when key in [:ticker, :invalidate_existing_ticker] ->
        {:error, gettext("can not update ticker")}
    end
  end

  def resolve(_, _, _), do: {:error, gettext("Oops! Something went wrong.")}

  defp update_company_confirmed_at(multi, %Gaia.Companies.Profile{} = company_profile),
    do:
      company_profile
      |> Gaia.Companies.Profile.changeset(%{information_confirmed_at: NaiveDateTime.utc_now(:second)})
      |> Ecto.Multi.update(multi, :profile, __)

  defp maybe_update_company_profile_name(
         multi,
         %Gaia.Companies.Profile{name: current_profile_name} = company_profile,
         name_input
       )
       when current_profile_name !== name_input do
    company_profile
    |> Gaia.Companies.Profile.changeset(%{
      name: name_input
    })
    |> Ecto.Multi.update(multi, :update_name, __)
  end

  defp maybe_update_company_profile_name(multi, _, _), do: multi

  # Users can only update the listing_key, the market_key is fixed
  defp maybe_update_company_ticker(
         multi,
         %Gaia.Companies.Profile{
           id: current_company_profile_id,
           ticker: %Gaia.Markets.Ticker{listing_key: current_listing_key, market_key: market_key} = current_ticker
         },
         listing_key_input
       )
       when current_listing_key !== listing_key_input do
    multi
    |> Ecto.Multi.update(
      :invalidate_existing_ticker,
      Gaia.Markets.Ticker.changeset(current_ticker, %{
        invalidated: true
      })
    )
    |> Ecto.Multi.insert(
      :ticker,
      Gaia.Markets.Ticker.changeset(%Gaia.Markets.Ticker{}, %{
        listing_key: listing_key_input,
        market_key: market_key,
        company_profile_id: current_company_profile_id
      })
    )
  end

  defp maybe_update_company_ticker(multi, _, _), do: multi
end
