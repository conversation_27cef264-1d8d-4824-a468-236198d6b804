defmodule AthenaWeb.Resolvers.Companiess.InvalidateCompanyProfileUser do
  @moduledoc """
    InvalidateCompanyProfileUser Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies

  def resolve(_, %{id: company_profile_user_id}, %{
        context: %{
          current_company_profile_user: %Companies.ProfileUser{
            id: current_company_profile_user_id,
            profile: %Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with false <- "#{current_company_profile_user_id}" === company_profile_user_id,
         %Companies.ProfileUser{
           profile_id: profile_id
         } = profile_user
         when profile_id === current_company_profile_id <-
           Companies.get_profile_user(company_profile_user_id),
         {:ok, %Companies.ProfileUser{} = updated_profile_user} <-
           Companies.invalidate_profile_user(profile_user) do
      {:ok, updated_profile_user}
    else
      true -> {:error, gettext("Can not remove yourself.")}
    end
  end
end
