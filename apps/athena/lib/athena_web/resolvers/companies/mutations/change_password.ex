defmodule AthenaWeb.Resolvers.Companies.ChangePassword do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_parent, %{old_password: old_password, password: password}, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{email: email}}}
      }) do
    with %Gaia.Companies.User{} = company_user <-
           Gaia.Companies.get_user_by_email_and_password(email, old_password),
         {:ok, %Gaia.Companies.User{}} <-
           Gaia.Companies.update_user_password(company_user, old_password, %{
             password: password
           }) do
      {:ok, true}
    else
      nil ->
        {:error, gettext("Invalid password.")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Oops! Something went wrong.")
         }}
    end
  end
end
