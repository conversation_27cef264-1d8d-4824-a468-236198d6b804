defmodule AthenaWeb.Resolvers.Companies.Mutations.ChangeShareholderOfferUsCitzenCheckResolver do
  @moduledoc """
  Resolver to change citizenship check for shareholder offer page
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Companies.User

  def resolve(_, %{updated_check: updated_check, shareholder_offer_page_id: id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: company_user_id}
          }
        }
      }) do
    with %ShareholderOfferPage{company_profile_id: company_profile_id} = shareholder_offer_page <-
           Companies.get_shareholder_offer_page(id),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, updated_shareholder_offer_page} <-
           Companies.update_shareholder_offer_page(shareholder_offer_page, %{
             us_citizen_page_enabled: updated_check,
             last_edited_by_user_id: company_user_id
           }) do
      {:ok, updated_shareholder_offer_page}
    else
      nil ->
        {:error, "Shareholder offer page not found"}

      {:check_ownership, false} ->
        {:error, gettext("You are unauthorized or don't have required permission!")}
    end
  end
end
