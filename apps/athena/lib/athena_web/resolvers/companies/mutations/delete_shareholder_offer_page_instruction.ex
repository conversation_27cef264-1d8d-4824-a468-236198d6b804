defmodule AthenaWeb.Resolvers.Companies.DeleteShareholderOfferPageInstruction do
  @moduledoc """
  DeleteShareholderOfferPageInstruction mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Companies.ShareholderOfferPages
  alias Gaia.Repo
  alias ShareholderOfferPages.Instruction

  @gettext_context "DeleteShareholderOfferPageInstruction mutation resolver"

  def resolve(_, %{instruction_id: instruction_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %Instruction{} = instruction <-
           Companies.get_instruction(instruction_id),
         %ShareholderOfferPage{company_profile_id: company_profile_id} <-
           instruction
           |> Repo.preload(:shareholder_offer_page)
           |> Map.get(:shareholder_offer_page),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok,
          %{
            delete_instruction: deleted_instruction,
            update_following_instructions_order: _update_following_instructions_order
          }} <-
           Companies.delete_instruction_and_update_orders(instruction) do
      {:ok, deleted_instruction}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to delete instruction"
             )
         }}
    end
  end
end
