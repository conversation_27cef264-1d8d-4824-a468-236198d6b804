defmodule AthenaWeb.Resolvers.Companies.CreateComputershareRegistryCredential do
  @moduledoc """
  CreateComputershareRegistryCredential Mutation Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser

  def resolve(_, %{computershare_registry_credential: %{username: username, password: password}}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: profile}}
      }) do
    case Gaia.Companies.add_registry_credential_computershare(
           %{username: username, password: password, security_alias: nil},
           profile
         ) do
      {:ok, true} ->
        {:ok, true}
    end
  end
end
