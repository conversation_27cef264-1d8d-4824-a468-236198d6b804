defmodule AthenaWeb.Resolvers.Companies.CreateCustomDomainSwap do
  @moduledoc """
  CreateCustomDomainSwap Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.CustomDomainSwap
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - CreateCustomDomainSwap mutation"

  def resolve(_, %{custom_domain: custom_domain}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}
      }) do
    with {:root_domain, {:ok, root_domain}} <-
           {:root_domain, Helper.Domain.get_root_domain(custom_domain)},
         {:aws_ses, {:ok, [_ | _]}} <-
           {:aws_ses, AmazonWebService.SES.create_domain_identity(root_domain)},
         {:aws_ses_mail_from, {:ok, _, _}} <-
           {:aws_ses_mail_from,
            AmazonWebService.SES.create_mail_from_for_identity(
              root_domain,
              "investorhub-email." <> root_domain
            )},
         {:vercel, {:ok, domain}} <-
           {:vercel, create_in_vercel(custom_domain)},
         {:created_domain, {:ok, %CustomDomainSwap{} = created}} <-
           {:created_domain,
            Gaia.Companies.find_or_create_custom_domain_swap(%{
              company_profile_id: id,
              custom_domain: custom_domain
            })},
         {:created_correctly, true} <- {:created_correctly, domain == custom_domain} do
      {:ok, created}
    else
      {step, error} ->
        return_error(error, step)
    end
  end

  defp create_in_vercel(custom_domain) do
    case Vercel.Domain.create(custom_domain) do
      {:ok, message} ->
        if String.match?(message, ~r/already in use/),
          do: {:error, message},
          else: {:ok, message}

      error ->
        {:error, error}
    end
  end

  defp return_error(error, step) do
    {:error,
     %Helper.AbsintheError{
       error: "Error on create_custom_domain_swap mutation at step #{step}: #{inspect(error)}",
       message:
         dpgettext(
           "errors",
           @gettext_context,
           "Unfortunately, we could not create your custom domain swap at this time, please try again later."
         )
     }}
  end
end
