defmodule AthenaWeb.Resolvers.Companies.Mutations.DisableCompanyProfileUserProductTour do
  @moduledoc false
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_, _, %{context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{} = current_profile_user}}) do
    current_profile_user
    |> Gaia.Companies.update_profile_user(%{
      enabled_product_tour: false
    })
    |> case do
      {:ok, %Gaia.Companies.ProfileUser{} = updated_profile_user} ->
        {:ok, updated_profile_user}
    end
  end
end
