defmodule AthenaWeb.Resolvers.Companies.ActivateCompanyProfileUser do
  @moduledoc """
    ActivateCompanyProfileUser Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{id: profile_id}} = current_company_profile_user
        }
      }) do
    case Gaia.Companies.update_profile_user(current_company_profile_user, %{
           status: :active,
           activated_at: NaiveDateTime.utc_now(:second)
         }) do
      {:ok, %ProfileUser{} = profile_user} ->
        # 1. Send InviteeAcceptedNotifier to all active admins in organisation
        # 2. Delete all invitation tokens belong to that activated profile user
        Task.start(fn ->
          send_invitee_activated_account_email_to_all_admins_in_organisation(%{
            invitee: profile_user,
            profile_id: profile_id
          })
        end)

        Companies.delete_all_profile_user_tokens_by_user(profile_user.id)

        {:ok, profile_user}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("can not activate user to this company")
         }}
    end
  end

  defp send_invitee_activated_account_email_to_all_admins_in_organisation(%{invitee: invitee, profile_id: profile_id}) do
    profile_id
    |> Companies.active_users_for_email()
    |> Enum.each(fn admin_profile_user ->
      EmailTransactional.Company
      |> Gaia.Notifications.Email.deliver_with_permission_check(
        :account_linked_to_company,
        [admin_profile_user, invitee],
        admin_profile_user,
        "companies_profile_users.admin",
        profile_id
      )
      |> case do
        {:ok, _} ->
          :ok

        error ->
          Helper.Error.Custom.ErrorHandler.handle(
            "send_invitee_activated_account_email_to_all_admins_in_organisation",
            %{
              profile_id: profile_id,
              invitee_id: invitee.id
            },
            error
          )

          :ok
      end
    end)
  end
end
