defmodule AthenaWeb.Resolvers.Companies.ConfirmCompanyUserInformationAndActiveCompanyProfileUser do
  @moduledoc """
  confirm_company_user_information_and_activate_company_profile_user Mutation
  1. Confirm Company Information if profile.information_confirmed_at is_nil
  2. Confirm company user information if user.information_confirmed_at is_nil
  3. Change company profile user status to :active
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies

  def resolve(_parents, %{job_title: job_title} = args, %{
        context: %{
          current_company_profile_user:
            %Companies.ProfileUser{
              user: %Companies.User{information_confirmed_at: information_confirmed_at} = current_company_user
            } = current_profile_user
        }
      }) do
    current_company_user
    |> Companies.update_user_and_profile_user_information(
      current_profile_user,
      args
      |> Map.delete(:job_title)
      |> maybe_put_information_confirmed_at(information_confirmed_at),
      %{job_title: job_title, status: :active, activated_at: NaiveDateTime.utc_now(:second)}
    )
    |> case do
      {:ok,
       %Companies.ProfileUser{
         id: profile_user_id,
         profile_id: profile_id,
         user: %Companies.User{}
       } = invitee} = result ->
        # 1. Send InviteeAcceptedNotifier to all admins in organisation
        # 2. Delete all invitation tokens belong to that activated profile user
        Task.start(fn ->
          send_invitee_activated_account_email_to_all_admins_in_organisation(%{invitee: invitee, profile_id: profile_id})
        end)

        Companies.delete_all_profile_user_tokens_by_user(profile_user_id)

        result

      {:error, :user} ->
        {:error, gettext("Can not confirm user details information!")}

      {:error, :profile_user} ->
        {:error, gettext("Can not activate user for this company")}
    end
  end

  # User should not confirm their information from this mutation if they already do so.
  # if get in here, means something wrong in frontend
  def resolve(_parents, _, _), do: {:error, gettext("Oops! Something went wrong.")}

  defp maybe_put_information_confirmed_at(args, information_confirmed_at) when is_nil(information_confirmed_at),
    do: Map.put(args, :information_confirmed_at, NaiveDateTime.utc_now(:second))

  defp maybe_put_information_confirmed_at(args, _information_confirmed_at), do: args

  def send_invitee_activated_account_email_to_all_admins_in_organisation(%{invitee: invitee, profile_id: profile_id}) do
    profile_id
    |> Companies.get_active_profile_users_for_permission("companies_profile_users.admin")
    |> Enum.each(fn admin_user ->
      EmailTransactional.Company
      |> Gaia.Notifications.Email.deliver_with_permission_check(
        :account_linked_to_company,
        [admin_user, invitee],
        admin_user.id,
        "companies_profile_users.admin",
        profile_id
      )
      |> case do
        {:ok, _} ->
          :ok

        error ->
          Sentry.capture_exception(error)
          :ok
      end
    end)
  end
end
