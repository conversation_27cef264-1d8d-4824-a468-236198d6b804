defmodule AthenaWeb.Resolvers.Companies.CreateCustomDomain do
  @moduledoc """
  CreateCustomDomain Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "Athena - CreateCustomDomain mutation"

  def resolve(_, %{custom_domain: custom_domain}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}
      }) do
    with {:root_domain, {:ok, root_domain}} <-
           {:root_domain, Helper.Domain.get_root_domain(custom_domain)},
         {:aws_ses, {:ok, [_ | _]}} <-
           {:aws_ses, AmazonWebService.SES.create_domain_identity(root_domain)},
         {:aws_ses_mail_from, {:ok, _, _}} <-
           {:aws_ses_mail_from,
            AmazonWebService.SES.create_mail_from_for_identity(
              root_domain,
              "investorhub-email." <> root_domain
            )},
         {:vercel, {:ok, domain}} <-
           {:vercel, vercel_create_domain(custom_domain, custom_domain == root_domain)},
         {:created_domain, {:ok, %CustomDomain{} = created}} <-
           {:created_domain,
            Gaia.Companies.find_or_create_custom_domain(%{
              company_profile_id: id,
              custom_domain: custom_domain
            })},
         {:created_correctly, true} <- {:created_correctly, domain == custom_domain} do
      Gaia.Jobs.DNSCheckAndRefresh.enqueue(%{company_profile_id: id}, schedule_in: {1, :hour})
      {:ok, created}
    else
      {:created_correctly, false} ->
        return_error("Unable to create custom domain")

      {_step, error} ->
        return_error(error)
    end
  end

  # When the custom domain is a root domain, we also create a www.root -> root redirect
  def vercel_create_domain(custom_domain, false), do: Vercel.Domain.create(custom_domain)

  def vercel_create_domain(custom_domain, true) do
    with {:ok, domain} <- Vercel.Domain.create(custom_domain),
         {:ok, _domain} <-
           Vercel.Domain.create_and_redirect("www.#{custom_domain}", custom_domain) do
      {:ok, domain}
    end
  end

  defp return_error(error) do
    {:error,
     %Helper.AbsintheError{
       error: error,
       message:
         dpgettext(
           "errors",
           @gettext_context,
           "Unfortunately, we could not create your custom domain at this time, please try again later."
         )
     }}
  end
end
