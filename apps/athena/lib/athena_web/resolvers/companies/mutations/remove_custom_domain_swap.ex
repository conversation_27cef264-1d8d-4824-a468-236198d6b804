defmodule AthenaWeb.Resolvers.Companies.RemoveCustomDomainSwap do
  @moduledoc """
    RemoveCustomDomainSwap Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.CustomDomainSwap
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}}) do
    with %CustomDomainSwap{} = existing <- Companies.get_custom_domain_swap_by(%{company_profile_id: id}),
         {:ok, _} <-
           Companies.remove_custom_domain_swap_from_vercel_and_invalidate(existing) do
      {:ok, nil}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Unable to cancel custom domain swap")
         }}
    end
  end
end
