defmodule AthenaWeb.Resolvers.Companies.UpdateCompanyShareholdingEmailSubscriptionSettings do
  @moduledoc """
    UpdateCompanyShareholdingEmailSubscriptionSettings Resolvers
  """

  def resolve(
        _parents,
        %{
          global_unsubscribe_on_registry_import: global_unsubscribe_on_registry_import,
          unsubscribe_scopes_on_registry_import: unsubscribe_scopes_on_registry_import
        },
        %{
          context: %{
            current_company_profile_user: %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{} = current_company_profile
            }
          }
        }
      ) do
    case Gaia.Companies.update_profile(current_company_profile, %{
           global_unsubscribe_on_registry_import: global_unsubscribe_on_registry_import,
           unsubscribe_scopes_on_registry_import: unsubscribe_scopes_on_registry_import
         }) do
      {:ok, %Gaia.Companies.Profile{} = profile} ->
        {:ok, profile}
    end
  end
end
