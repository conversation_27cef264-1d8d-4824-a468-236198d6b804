defmodule AthenaWeb.Resolvers.Companies.SetPasswordFromInvitationToken do
  @moduledoc """
    SetPasswordFromInvitationToken Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_, %{token: token, password_confirmation: password_confirmation, password: password}, %{context: context}) do
    # if user already logged in, should not use this mutation to setup password
    with false <-
           Map.get(context, :current_company_user) != nil or
             Map.get(context, :current_company_profile_user) != nil,
         true <- password_confirmation === password,
         #  same here, if use already had password, should not use this mutation to setup password
         %Gaia.Companies.ProfileUser{
           user:
             %Gaia.Companies.User{
               hashed_password: hashed_password
             } = company_user
         }
         when is_nil(hashed_password) <-
           Gaia.Companies.get_profile_user_by_invitation_token_preload_user(token),
         {:ok, %Gaia.Companies.User{}} <-
           Gaia.Companies.change_user_password(company_user, password) do
      {:ok, true}
    else
      %Gaia.Companies.ProfileUser{} ->
        {:error, gettext("Password already set, please login")}

      false ->
        {:error, gettext("Please enter the same password.")}

      true ->
        {:error, gettext("Your invitation token is invalid. Contact the person who invited you or InvestorHub")}
    end
  end
end
