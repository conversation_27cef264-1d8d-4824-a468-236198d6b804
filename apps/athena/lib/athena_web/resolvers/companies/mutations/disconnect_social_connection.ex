defmodule AthenaWeb.Resolvers.Companies.DisconnectSocialConnection do
  @moduledoc """
  UpdateSocialConnection Mutation Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection
  alias Gaia.Flows

  @gettext_context "Athena - DisconnectSocialConnection mutation"

  @doc """
  Disconnect social media account from company profile

  Returns nil if social_connection is already nil

  Returns the updated social connection or nil if completely disconnected
  """
  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{social_connection: nil}}}}) do
    {:ok, nil}
  end

  def resolve(_, %{social_platform: social_platform}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: company_profile_id, social_connection: %SocialConnection{} = social_connection}
          }
        }
      }) do
    with {:ok, disconnect_attrs} <- get_disconnect_attrs(social_platform),
         {:ok, updated_social_connection} <-
           Companies.update_social_connection(social_connection, disconnect_attrs),
         {:ok, further_updated_social_connection} <-
           then(updated_social_connection, &delete_if_no_social_account_connected/1),
         {:ok, _} <-
           Flows.delete_distribution_settings_by_channel_for_company(
             company_profile_id,
             social_platform
           ) do
      {:ok, further_updated_social_connection}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to disconnect your social media account"
             )
         }}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to disconnect")
     }}
  end

  defp get_disconnect_attrs(:linkedin) do
    {:ok,
     %{
       linkedin_access_token: nil,
       linkedin_access_token_expires_at: nil,
       linkedin_organisation_id: nil,
       linkedin_organisation_name: nil,
       linkedin_refresh_token: nil,
       linkedin_refresh_token_expires_at: nil
     }}
  end

  defp get_disconnect_attrs(:twitter) do
    {:ok,
     %{
       twitter_oauth_token: nil,
       twitter_oauth_token_secret: nil,
       twitter_username: nil,
       twitter_user_id: nil
     }}
  end

  defp get_disconnect_attrs(social_platform) do
    {:error, "Unable to get disconnect attrs for #{inspect(social_platform)}"}
  end

  # If both linkedin and twitter oauth secrets aren't present, delete the social connection row for the company
  defp delete_if_no_social_account_connected(
         %SocialConnection{linkedin_access_token: linkedin_access_token, twitter_oauth_token: twitter_oauth_token} =
           social_connection
       )
       when is_nil(twitter_oauth_token) and is_nil(linkedin_access_token) do
    case Companies.delete_social_connection(social_connection) do
      {:ok, _} ->
        {:ok, nil}

      {:error, error} ->
        {:error, error}
    end
  end

  defp delete_if_no_social_account_connected(social_connection), do: {:ok, social_connection}
end
