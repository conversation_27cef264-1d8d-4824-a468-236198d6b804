defmodule AthenaWeb.Resolvers.Companies.InviteAndCreateCompanyProfileUsers do
  @moduledoc """
    InviteAndCrateCompanyProfileUsers Resolvers
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Notifications

  def resolve(_parents, %{invite_company_profile_users: invite_company_profile_users_input}, %{
        context: %{
          current_company_profile_user: %Companies.ProfileUser{
            profile: %Companies.Profile{id: company_profile_id, is_sso_only: true}
          }
        }
      }),
      do: invite_and_create_sso_company_profile_user_exec(invite_company_profile_users_input, company_profile_id, [])

  def resolve(_parents, %{invite_company_profile_users: invite_company_profile_users_input}, %{
        context: %{
          current_company_profile_user: %Companies.ProfileUser{profile: %Companies.Profile{id: company_profile_id}}
        }
      }),
      do: invite_and_create_company_profile_user_exec(invite_company_profile_users_input, company_profile_id, [])

  defp invite_and_create_sso_company_profile_user_exec(
         [%{email: email, role_id: companies_role_id, job_title: job_title} | inputs],
         company_profile_id,
         results
       ) do
    with {:create_company_user, {:ok, %Companies.User{id: company_user_id}}} <-
           {:create_company_user, Companies.maybe_create_sso_only_user(email)},
         {:company_profile_user_exists?, false} <-
           {:company_profile_user_exists?,
            Companies.profile_user_exists?(%{
              profile_id: company_profile_id,
              user_id: company_user_id
            })},
         {:check_role_exist?, %Gaia.Companies.Role{}} <-
           {:check_role_exist?, Gaia.Companies.get_role(companies_role_id)},
         {:create_company_profile_user, {:ok, %Companies.ProfileUser{} = company_profile_user}} <-
           {:create_company_profile_user,
            Companies.create_profile_user(%{
              user_id: company_user_id,
              companies_role_id: companies_role_id,
              job_title: job_title,
              profile_id: company_profile_id
            })},
         {:ok, _} <-
           company_profile_user
           |> Gaia.Repo.preload([:user, :profile])
           |> send_sso_invitation_email() do
      invite_and_create_sso_company_profile_user_exec(
        inputs,
        company_profile_id,
        [%{success: company_profile_user}] ++ results
      )
    else
      {:create_company_user, _error} ->
        invite_and_create_sso_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Unable to create user at the moment, please try again.")
                }
              }
            ]
        )

      {:company_profile_user_exists?, true} ->
        invite_and_create_sso_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("User already exist in your company.")
                }
              }
            ]
        )

      {:create_company_profile_user, _error} ->
        invite_and_create_sso_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Fail to add user into your company.")
                }
              }
            ]
        )

      {:error, error} when is_binary(error) ->
        invite_and_create_sso_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: error
                }
              }
            ]
        )

      _error ->
        invite_and_create_sso_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Oops! Something went wrong.")
                }
              }
            ]
        )
    end
  end

  defp invite_and_create_sso_company_profile_user_exec(_, _, results), do: {:ok, results}

  def invite_and_create_company_profile_user_exec(
        [%{email: email, role_id: companies_role_id, job_title: job_title} | inputs],
        company_profile_id,
        results
      ) do
    with {:create_company_user, {:ok, %Companies.User{id: company_user_id}}} <-
           {:create_company_user, Companies.maybe_create_pending_access_user(email)},
         {:check_role_exist?, %Gaia.Companies.Role{}} <-
           {:check_role_exist?, Gaia.Companies.get_role(companies_role_id)},
         {:company_profile_user_exists?, false} <-
           {:company_profile_user_exists?,
            Companies.profile_user_exists?(%{
              profile_id: company_profile_id,
              user_id: company_user_id
            })},
         {:create_company_profile_user, {:ok, %Companies.ProfileUser{} = company_profile_user}} <-
           {:create_company_profile_user,
            Companies.create_profile_user(%{
              user_id: company_user_id,
              companies_role_id: companies_role_id,
              job_title: job_title,
              profile_id: company_profile_id
            })},
         {:ok, _} <-
           company_profile_user
           |> Gaia.Repo.preload([:user, :profile])
           |> send_invitation_email() do
      invite_and_create_company_profile_user_exec(
        inputs,
        company_profile_id,
        [%{success: company_profile_user}] ++ results
      )
    else
      {:create_company_user, _error} ->
        invite_and_create_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Unable to create user at the moment, please try again.")
                }
              }
            ]
        )

      {:company_profile_user_exists?, true} ->
        invite_and_create_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("User already exist in your company.")
                }
              }
            ]
        )

      {:create_company_profile_user, _error} ->
        invite_and_create_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Fail to add user into your company.")
                }
              }
            ]
        )

      _error ->
        invite_and_create_company_profile_user_exec(
          inputs,
          company_profile_id,
          results ++
            [
              %{
                error: %{
                  email: email,
                  reason: gettext("Oops! Something went wrong.")
                }
              }
            ]
        )
    end
  end

  def invite_and_create_company_profile_user_exec(_, _, results), do: {:ok, results}

  defp send_sso_invitation_email(company_profile_user) do
    company_profile_users =
      company_profile_user
      |> Map.get(:user)
      |> Gaia.Repo.preload(company_profile_users: [[profile: :ticker], :user])
      |> Map.get(:company_profile_users)

    if length(company_profile_users) === 1 do
      profile_user = List.first(company_profile_users)

      Notifications.Email.deliver(
        EmailTransactional.Company,
        :account_activate_sso_only,
        [profile_user],
        profile_user.profile.id
      )
    else
      {:error, "SSO Company User can only have one linked organisation"}
    end
  end

  defp send_invitation_email(company_profile_user) do
    encoded_token = Companies.create_email_token(company_profile_user.id)

    company_profile_users =
      company_profile_user
      |> Map.get(:user)
      |> Gaia.Repo.preload(company_profile_users: [[profile: :ticker], :user])
      |> Map.get(:company_profile_users)

    if length(company_profile_users) === 1 do
      # If user has only one linked organisation, send InviteNewUserNotifier
      # Otherwise, send InviteExistingUserNotifier
      profile_user = List.first(company_profile_users)

      Notifications.Email.deliver(
        EmailTransactional.Company,
        :account_activate_from_invite,
        [profile_user, encoded_token],
        profile_user.profile.id
      )
    else
      Notifications.Email.deliver(
        EmailTransactional.Company,
        :account_link_to_company,
        [Gaia.Repo.preload(company_profile_user, profile: :ticker), encoded_token],
        company_profile_user.profile.id
      )
    end
  end
end
