defmodule AthenaWeb.Resolvers.Companies.UpdateCompanyUserAndCompanyProfileUserInformation do
  @moduledoc """
    update_company_user_and_company_profile_user_information resolver
    1. Update first name, last name, mobile number to company user
    2. Update title to company profile user
  """
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_parents, %{job_title: job_title} = args, %{
        context: %{
          current_company_profile_user:
            %Gaia.Companies.ProfileUser{user: %Gaia.Companies.User{} = current_company_user} = current_profile_user
        }
      }) do
    current_company_user
    |> Gaia.Companies.update_user_and_profile_user_information(
      current_profile_user,
      Map.delete(args, :job_title),
      %{job_title: job_title}
    )
    |> case do
      {:ok, %Gaia.Companies.ProfileUser{}} = result ->
        result

      {:error, :user} ->
        {:error, gettext("Can not update user details information!")}

      {:error, :profile_user} ->
        {:error, gettext("Can not change title for this user")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Oops! Something went wrong.")
         }}
    end
  end
end
