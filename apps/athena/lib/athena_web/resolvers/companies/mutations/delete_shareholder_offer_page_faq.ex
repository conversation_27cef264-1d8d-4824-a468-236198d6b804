defmodule AthenaWeb.Resolvers.Companies.DeleteShareholderOfferPageFaq do
  @moduledoc """
  DeleteShareholderOfferPageFaq mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Companies.ShareholderOfferPages.Faq
  alias Gaia.Repo

  @gettext_context "DeleteShareholderOfferPageFaq mutation resolver"

  def resolve(_, %{faq_id: faq_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %Faq{} = faq <-
           Companies.get_faq(faq_id),
         %ShareholderOfferPage{company_profile_id: company_profile_id} <-
           faq |> Repo.preload(:shareholder_offer_page) |> Map.get(:shareholder_offer_page),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %{delete_faq: deleted_faq, update_following_faqs_order: _update_following_faqs_order}} <-
           Companies.delete_faq_and_update_orders(faq) do
      {:ok, deleted_faq}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to delete faq"
             )
         }}
    end
  end
end
