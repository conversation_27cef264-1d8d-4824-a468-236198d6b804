defmodule AthenaWeb.Resolvers.Companies.VerifyCustomDomainSwap do
  @moduledoc """
    VerifyCustomDomainSwap Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.CustomDomainSwap
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker

  def resolve(_, _, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile: %Profile{id: id, ticker: %Ticker{} = ticker}} = current_company_profile_user
        }
      }) do
    with %CustomDomainSwap{} = existing_swap <-
           Gaia.Companies.get_custom_domain_swap_by(%{company_profile_id: id}),
         {dkim_verified, dmarc_verified, mailfrom_verified, vercel_domain_verified} <-
           verify_domain_settings(existing_swap, ticker),
         {:ok, %CustomDomainSwap{custom_domain: custom_domain}} <-
           update_custom_domain(
             existing_swap,
             dkim_verified,
             dmarc_verified,
             mailfrom_verified,
             vercel_domain_verified
           ) do
      custom_domain_now_configured =
        vercel_domain_verified and dkim_verified and dmarc_verified and mailfrom_verified

      # If any are not configured, run the job to check and refresh
      # if the AWS domain has gone past the 72 hour mark, it will be recreated
      unless custom_domain_now_configured do
        Task.start(fn -> Gaia.Jobs.DNSCheckAndRefreshForSwap.enqueue(%{company_profile_id: id}) end)
      end

      # Only send email to user when all have been configured but previously not
      if custom_domain_now_configured and
           not Gaia.Companies.is_custom_domain_verified?(existing_swap) do
        Task.start(fn -> send_email(current_company_profile_user, custom_domain) end)
      end

      {:ok,
       %{
         is_vercel_domain_verified: vercel_domain_verified,
         is_dkim_verified: dkim_verified,
         is_dmarc_verified: dmarc_verified,
         is_mailfrom_verified: mailfrom_verified
       }}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Unfortunately we could not verify the connection at this time, please try again later.")
         }}
    end
  end

  defp verify_domain_settings(%CustomDomainSwap{} = existing_swap, %Ticker{} = _ticker) do
    %CustomDomainSwap{
      custom_domain: custom_domain,
      root_domain: root_domain,
      is_vercel_domain_verified: is_vercel_domain_verified,
      is_dkim_verified: is_dkim_verified,
      is_dmarc_verified: is_dmarc_verified,
      is_mailfrom_verified: is_mailfrom_verified
    } = existing_swap

    dkim_verified = is_dkim_verified or aws_dkim_verified?(root_domain)
    dmarc_verified = is_dmarc_verified or aws_dmarc_verified?(root_domain)
    mailfrom_verified = is_mailfrom_verified or aws_mail_from_verified?(root_domain)

    vercel_domain_verified =
      is_vercel_domain_verified or Vercel.Domain.get_is_configured(custom_domain) == {:ok, true}

    {dkim_verified, dmarc_verified, mailfrom_verified, vercel_domain_verified}
  end

  defp update_custom_domain(existing_swap, dkim_verified, dmarc_verified, mailfrom_verified, vercel_domain_verified) do
    Gaia.Companies.update_custom_domain_swap(existing_swap, %{
      is_vercel_domain_verified: vercel_domain_verified,
      is_dkim_verified: dkim_verified,
      is_dmarc_verified: dmarc_verified,
      is_mailfrom_verified: mailfrom_verified
    })
  end

  defp aws_dkim_verified?(root_domain) do
    case AmazonWebService.SES.is_dkim_verified(root_domain) do
      {:ok, true} -> true
      _ -> false
    end
  end

  defp aws_dmarc_verified?(root_domain) do
    AmazonWebService.SES.is_dmarc?(root_domain)
  end

  defp aws_mail_from_verified?(root_domain) do
    case AmazonWebService.SES.get_mail_from_identity(root_domain) do
      {:ok, true} -> true
      _ -> false
    end
  end

  defp send_email(%ProfileUser{} = company_profile_user, custom_domain) do
    Gaia.Notifications.Email.deliver(
      EmailTransactional.Company,
      :custom_domain_swap_ready,
      [company_profile_user, custom_domain],
      company_profile_user.profile.id
    )
  end
end
