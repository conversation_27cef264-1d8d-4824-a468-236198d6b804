defmodule AthenaWeb.Resolvers.Companies.UpdateSocialConnection do
  @moduledoc """
  UpdateSocialConnection Mutation Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection

  @gettext_context "Athena - UpdateSocialConnection mutation"

  def resolve(_parent, %{social_connection: attrs}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Companies.get_social_connection_by(%{company_profile_id: company_profile_id}) do
      %SocialConnection{} = exists ->
        Companies.update_social_connection(exists, attrs)

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not update social connection for company"
             )
         }}
    end
  end
end
