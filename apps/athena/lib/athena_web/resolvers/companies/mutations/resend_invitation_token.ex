defmodule AthenaWeb.Resolvers.Companies.ResendInvitationToken do
  @moduledoc """
  ResendInvitationToken Mutation Resolver

  This route is not permissioned because an invited person can request their link be resent if it is expired

  Only resend invitation if profile user is pending
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies

  def resolve(_, %{id: company_profile_user_id}, _) do
    encoded_token = Companies.create_email_token(company_profile_user_id)

    with %Companies.ProfileUser{status: :pending} = company_profile_user <-
           company_profile_user_id
           |> Companies.get_profile_user()
           |> Gaia.Repo.preload([:user, :profile]),
         {:ok, _} <-
           Gaia.Notifications.Email.deliver(
             EmailTransactional.Company,
             :account_activate_from_invite,
             [company_profile_user, encoded_token],
             company_profile_user.profile.id
           ) do
      {:ok, true}
    else
      %Companies.ProfileUser{} ->
        # No need to resend invitation email if not pending
        {:ok, true}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Oops! Something went wrong.")
         }}
    end
  end
end
