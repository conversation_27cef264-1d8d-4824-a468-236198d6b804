defmodule AthenaWeb.Resolvers.Companies.VerifyCustomDomainV2 do
  @moduledoc """
    VerifyCustomDomainV2 Mutation Resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.CustomDomain
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker

  def resolve(_, _, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile: %Profile{id: id, ticker: %Ticker{} = ticker}} = current_company_profile_user
        }
      }) do
    with %CustomDomain{} = existing_custom_domain <-
           Gaia.Companies.get_custom_domain_by(%{company_profile_id: id}),
         {dkim_verified, dmarc_verified, mailfrom_verified, vercel_domain_verified} <-
           verify_domain_settings(existing_custom_domain, ticker),
         {:ok, %CustomDomain{custom_domain: custom_domain}} <-
           update_custom_domain(
             existing_custom_domain,
             dkim_verified,
             dmarc_verified,
             mailfrom_verified,
             vercel_domain_verified
           ) do
      custom_domain_now_configured =
        vercel_domain_verified and dkim_verified and dmarc_verified and mailfrom_verified

      # If any are not configured, run the job to check and refresh
      # if the AWS domain has gone past the 72 hour mark, it will be recreated
      unless custom_domain_now_configured do
        Task.start(fn -> Gaia.Jobs.DNSCheckAndRefresh.enqueue(%{company_profile_id: id}) end)
      end

      # Only send email to user when all have been configured but previously not
      if custom_domain_now_configured and
           not Gaia.Companies.is_custom_domain_verified?(existing_custom_domain) do
        Task.start(fn -> send_email(current_company_profile_user, custom_domain) end)
      end

      {:ok,
       %{
         is_vercel_domain_verified: vercel_domain_verified,
         is_dkim_verified: dkim_verified,
         is_dmarc_verified: dmarc_verified,
         is_mailfrom_verified: mailfrom_verified
       }}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Unfortunately we could not verify the connection at this time, please try again later.")
         }}
    end
  end

  defp verify_domain_settings(%CustomDomain{} = existing_custom_domain, %Ticker{} = ticker) do
    %CustomDomain{
      custom_domain: custom_domain,
      root_domain: root_domain,
      is_vercel_domain_verified: is_vercel_domain_verified,
      is_dkim_verified: is_dkim_verified,
      is_dmarc_verified: is_dmarc_verified,
      is_mailfrom_verified: is_mailfrom_verified
    } = existing_custom_domain

    dkim_verified = is_dkim_verified or aws_dkim_verified?(root_domain)
    dmarc_verified = is_dmarc_verified or aws_dmarc_verified?(root_domain)
    mailfrom_verified = is_mailfrom_verified or aws_mail_from_verified?(root_domain)

    vercel_domain_verified =
      is_vercel_domain_verified or
        check_vercel_domain_and_maybe_assign_redirect(
          custom_domain,
          Ticker.resolve_market_listing_key(ticker),
          custom_domain == root_domain
        )

    {dkim_verified, dmarc_verified, mailfrom_verified, vercel_domain_verified}
  end

  defp update_custom_domain(
         existing_custom_domain,
         dkim_verified,
         dmarc_verified,
         mailfrom_verified,
         vercel_domain_verified
       ) do
    Gaia.Companies.update_custom_domain(existing_custom_domain, %{
      is_vercel_domain_verified: vercel_domain_verified,
      is_dkim_verified: dkim_verified,
      is_dmarc_verified: dmarc_verified,
      is_mailfrom_verified: mailfrom_verified
    })
  end

  defp aws_dkim_verified?(root_domain) do
    case AmazonWebService.SES.is_dkim_verified(root_domain) do
      {:ok, true} -> true
      _ -> false
    end
  end

  defp aws_dmarc_verified?(root_domain) do
    AmazonWebService.SES.is_dmarc?(root_domain)
  end

  defp aws_mail_from_verified?(root_domain) do
    case AmazonWebService.SES.get_mail_from_identity(root_domain) do
      {:ok, true} -> true
      _ -> false
    end
  end

  defp check_vercel_domain_and_maybe_assign_redirect(custom_domain, market_listing_key, domain_is_root) do
    with {:ok, true} <- Vercel.Domain.get_is_configured(custom_domain),
         {:ticker_redirect, {:ok, _}} <-
           {:ticker_redirect, Companies.assign_redirect(market_listing_key, custom_domain)},
         {:www_redirect, {:ok, _}} <-
           {:www_redirect, Vercel.add_www_redirect(custom_domain, domain_is_root)} do
      true
    else
      _ ->
        false
    end
  end

  defp send_email(%ProfileUser{} = company_profile_user, custom_domain) do
    Gaia.Notifications.Email.deliver(
      EmailTransactional.Company,
      :custom_domain_connected,
      [company_profile_user, custom_domain],
      company_profile_user.profile.id
    )
  end
end
