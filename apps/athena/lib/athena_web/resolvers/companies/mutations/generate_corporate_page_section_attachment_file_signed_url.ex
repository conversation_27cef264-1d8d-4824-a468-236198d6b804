defmodule AthenaWeb.Resolvers.Companies.GenerateCorporatePageSectionAttachmentFileSignedUrl do
  @moduledoc """
  GenerateCorporatePageSectionAttachmentFileSignedUrl Mutation
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @gettext_context "GenerateCorporatePageSectionAttachmentFileSignedUrl mutation"

  def resolve(_parent, %{file_size: file_size, mime_type: mime_type}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      })
      when file_size <= 100_000_000 do
    with %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{current_company_profile_id}/corporate_page/section_attachment/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ) do
      {:ok, url}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not create section attachment file"
             )
         }}
    end
  end
end
