defmodule AthenaWeb.Resolvers.Companies.UpsertShareholderOfferPage do
  @moduledoc """
  UpsertShareholderOfferPage mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Companies.User

  @gettext_context "UpsertShareholderOfferPage mutation resolver"

  def resolve(
        _,
        %{shareholder_offer_page: shareholder_offer_page_input, shareholder_offer_id: shareholder_offer_id} = args,
        %{
          context: %{
            current_company_profile_user: %ProfileUser{
              profile: %Profile{id: current_company_profile_id},
              user: %User{id: user_id}
            }
          }
        }
      ) do
    new_shareholder_offer_page_input =
      Map.merge(shareholder_offer_page_input, %{
        company_profile_id: current_company_profile_id,
        last_edited_by_user_id: user_id,
        shareholder_offer_id: shareholder_offer_id
      })

    with {:ok, %ShareholderOfferPage{id: shareholder_offer_page_id} = upserted_shareholder_offer_page} <-
           Companies.upsert_shareholder_offer_page(new_shareholder_offer_page_input),
         {:ok, _} <-
           args
           |> Map.get(:instructions)
           |> maybe_upsert_instructions(shareholder_offer_page_id, user_id),
         {:ok, _} <-
           args |> Map.get(:faqs) |> maybe_upsert_faqs(shareholder_offer_page_id, user_id) do
      {:ok, upserted_shareholder_offer_page}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not upsert shareholder offer page"
             )
         }}
    end
  end

  defp maybe_upsert_instructions(instructions, shareholder_offer_page_id, last_edited_by_user_id)
       when not is_nil(instructions) do
    Companies.upsert_instructions(instructions, shareholder_offer_page_id, last_edited_by_user_id)
  end

  defp maybe_upsert_instructions(_, _, _), do: {:ok, :skip}

  defp maybe_upsert_faqs(faqs, shareholder_offer_page_id, last_edited_by_user_id) when not is_nil(faqs) do
    Companies.upsert_faqs(faqs, shareholder_offer_page_id, last_edited_by_user_id)
  end

  defp maybe_upsert_faqs(_, _, _), do: {:ok, :skip}
end
