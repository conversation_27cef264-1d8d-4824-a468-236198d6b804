defmodule AthenaWeb.Resolvers.Companies.CreateShareholderOfferPageFaq do
  @moduledoc """
  CreateShareholderOfferPageFaq mutation resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.ShareholderOfferPages.Faq
  alias Gaia.Companies.User
  alias Gaia.Raises
  alias Gaia.Raises.ShareholderOffer
  alias Gaia.Repo

  def resolve(_, %{faq: faq_input, shareholder_offer_id: shareholder_offer_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: current_company_profile_id},
            user: %User{id: user_id}
          }
        }
      }) do
    with %ShareholderOffer{
           company_profile_id: company_profile_id
         } = shareholder_offer <-
           shareholder_offer_id
           |> Raises.get_shareholder_offer()
           |> Repo.preload(:company_shareholder_offer_page),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, shareholder_offer_page_id} <-
           Companies.get_shareholder_offer_page_id(shareholder_offer, user_id),
         total_faqs = Companies.get_total_faqs_by_shareholder_offer_page_id(shareholder_offer_page_id),
         {:ok, %Faq{} = created_faq} <-
           faq_input
           |> Map.merge(%{
             shareholder_offer_page_id: shareholder_offer_page_id,
             order_id: total_faqs + 1,
             last_edited_by_user_id: user_id
           })
           |> Companies.create_faq() do
      {:ok, created_faq}
    end
  end
end
