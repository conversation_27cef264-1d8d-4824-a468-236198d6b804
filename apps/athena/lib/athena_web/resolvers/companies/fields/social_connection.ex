defmodule AthenaWeb.Resolvers.Companies.SocialConnectionFields do
  @moduledoc """
  SocialConnectionFields Resolver
  """

  def get_is_linkedin_connected(social_connection, _args, _context) do
    {:ok, Gaia.Companies.get_is_linkedin_connected(social_connection)}
  end

  def get_is_linkedin_setup_completed(social_connection, _args, _context) do
    {:ok, Gaia.Companies.get_is_linkedin_setup_completed(social_connection)}
  end

  def get_is_twitter_setup_completed(social_connection, _args, _context) do
    {:ok, Gaia.Companies.get_is_twitter_setup_completed(social_connection)}
  end
end
