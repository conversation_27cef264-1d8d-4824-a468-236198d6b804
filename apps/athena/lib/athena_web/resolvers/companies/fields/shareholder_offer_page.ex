defmodule AthenaWeb.Resolvers.Companies.ShareholderOfferPageFields do
  @moduledoc """
  ShareholderOfferPageFields
  """
  alias Gaia.Companies.ShareholderOfferPage
  alias Gaia.Uploaders.Companies.ShareholderOfferPage.Banner
  alias Gaia.Uploaders.Companies.ShareholderOfferPage.InvestorPresentation
  alias Gaia.Uploaders.Companies.ShareholderOfferPage.OfferBooklet
  alias Gaia.Uploaders.Companies.ShareholderOfferPage.RaiseDefinitionTimelineDiagram
  alias Gaia.Uploaders.Companies.ShareholderOfferPage.RaiseReasonHeroMediaImage

  def banner_url(%ShareholderOfferPage{banner: banner} = parent, _args, _resolution) do
    {:ok, Banner.url({banner, parent})}
  end

  def investor_presentation_url(
        %ShareholderOfferPage{investor_presentation: investor_presentation} = parent,
        _args,
        _resolution
      ) do
    {:ok, InvestorPresentation.url({investor_presentation, parent})}
  end

  def offer_booklet_url(%ShareholderOfferPage{offer_booklet: offer_booklet} = parent, _args, _resolution) do
    {:ok, OfferBooklet.url({offer_booklet, parent})}
  end

  def raise_definition_timeline_diagram_url(
        %ShareholderOfferPage{raise_definition_timeline_diagram: raise_definition_timeline_diagram} = parent,
        _args,
        _resolution
      ) do
    {:ok, RaiseDefinitionTimelineDiagram.url({raise_definition_timeline_diagram, parent})}
  end

  def raise_reason_hero_media_image_url(
        %ShareholderOfferPage{raise_reason_hero_media_image: raise_reason_hero_media_image} = parent,
        _args,
        _resolution
      ) do
    {:ok, RaiseReasonHeroMediaImage.url({raise_reason_hero_media_image, parent})}
  end
end
