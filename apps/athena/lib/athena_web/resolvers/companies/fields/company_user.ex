defmodule AthenaWeb.Resolvers.Companies.CompanyUserFields do
  @moduledoc """
    Resolvers for object company_user
  """
  use Helper.Pipe

  def has_password?(%Gaia.Companies.User{hashed_password: hashed_password}, _query, _context)
      when is_binary(hashed_password),
      do: {:ok, true}

  def has_password?(_parent, _query, _context), do: {:ok, false}

  def notification_preferences(%Gaia.Companies.User{id: company_user_id, email: company_user_email}, _query, _context)
      when not is_nil(company_user_email) do
    {:ok, Gaia.Comms.CompanyUsers.list_notification_preferences_by(company_user_id: company_user_id)}
  end

  def notification_preferences(_parent, _query, _context), do: {:ok, []}

  def resolve(_parent, _args, _resolution) do
    {:ok, nil}
  end
end
