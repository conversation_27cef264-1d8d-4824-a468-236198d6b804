defmodule AthenaWeb.Resolvers.Companies.AnnouncementTypes do
  @moduledoc """
  Resolver for announcement types
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Markets.Ticker

  def resolve(_, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{ticker: %Ticker{market_key: market_key}}}}
      }) do
    announcement_rectype_values = MediaAnnouncement.get_announcement_rectype_values(market_key)
    announcement_subtypes_values = MediaAnnouncement.get_announcement_subtypes_values(market_key)

    {:ok,
     %{
       list: MediaAnnouncement.list_announcement_types(market_key),
       rectype_values: announcement_rectype_values,
       recommended_values: MediaAnnouncement.get_recommended_type_values(market_key),
       subtype_values: announcement_subtypes_values,
       all_values: announcement_rectype_values ++ announcement_subtypes_values
     }}
  end
end
