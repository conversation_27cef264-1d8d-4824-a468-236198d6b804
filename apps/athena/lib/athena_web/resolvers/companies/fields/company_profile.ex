defmodule AthenaWeb.Resolvers.Companies.CompanyProfileFields do
  @moduledoc """
  Resolvers for object company_profile
  """
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Uploaders.Companies.DNSIntegrationPDF
  alias Gaia.Uploaders.Companies.ProfileLogo
  alias Gaia.Websites

  def color_palette(%Profile{} = profile, _query, _context),
    do: {:ok, Companies.color_palette_for_company_profile(profile)}

  def dns_integration_pdf_url(
        %Gaia.Companies.Profile{dns_integration_pdf: dns_integration_pdf} = profile,
        _query,
        _context
      ),
      do:
        if(is_nil(dns_integration_pdf),
          do: {:ok, nil},
          else: {:ok, DNSIntegrationPDF.url({dns_integration_pdf, profile})}
        )

  def logo_url(%Gaia.Companies.Profile{logo: logo} = profile, _query, _context),
    do: {:ok, ProfileLogo.url({logo, profile})}

  def company_profile_has_had_spp(%{id: company_profile_id}, _query, _context),
    do: {:ok, Gaia.Registers.company_profile_has_had_spp(company_profile_id)}

  def company_profile_has_had_placement(%{id: company_profile_id}, _query, _context),
    do: {:ok, Gaia.Registers.company_profile_has_had_placement(company_profile_id)}

  def total_shareholder_offers(%{id: company_profile_id}, _query, _context),
    do: {:ok, Gaia.Raises.get_company_profile_total_shareholder_offers(company_profile_id)}

  def live_shareholder_offer_id(%{id: company_profile_id}, _query, _context),
    do: {:ok, Gaia.Raises.live_shareholder_offer_id(company_profile_id)}

  def has_company_shareholder_offer_permission(%{id: company_profile_id}, _query, _context),
    do: {:ok, Companies.has_company_shareholder_offer_permission(company_profile_id)}

  def live_hub_builder_website(%{id: company_profile_id}, _query, _context),
    do: {:ok, Websites.get_published_website(company_profile_id)}

  def hub(%Gaia.Companies.Profile{id: id}, _query, _context) do
    hub_id = Helper.Hashid.encode_id(id)

    if is_binary(hub_id) do
      {:ok, hub_id}
    else
      {:ok, ""}
    end
  end
end
