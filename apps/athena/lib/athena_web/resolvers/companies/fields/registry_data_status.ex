defmodule AthenaWeb.Resolvers.Companies.RegistryDataStatus do
  @moduledoc """
  Resolver for registry data status
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.RegistryDataStatus

  def resolve(%Profile{} = company_profile, _query, _context) do
    {:ok, RegistryDataStatus.get_status(company_profile)}
  end

  def resolve(_parent, _args, %{context: %{current_company_profile_user: %ProfileUser{profile: company_profile}}}) do
    now = Timex.format!(Helper.ExDay.now_date(), "%Y-%m-%d", :strftime)

    {:ok,
     company_profile
     |> RegistryDataStatus.get_status()
     |> Map.put(:id, "#{company_profile.id}-#{now}")}
  end
end
