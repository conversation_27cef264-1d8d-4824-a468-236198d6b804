defmodule AthenaWeb.Resolvers.Companies.WelcomePageFields do
  @moduledoc false

  alias Gaia.Companies.WelcomePage
  alias Gaia.Uploaders.Companies.WelcomePage.Banner
  alias Gaia.Uploaders.Companies.WelcomePage.ProfilePicture
  alias Gaia.Uploaders.Companies.WelcomePage.Signature

  def banner_url(%WelcomePage{banner: banner} = parent, _args, _resolution) do
    {:ok, Banner.url({banner, parent})}
  end

  def profile_picture_url(%WelcomePage{profile_picture: profile_picture} = parent, _args, _resolution) do
    {:ok, ProfilePicture.url({profile_picture, parent})}
  end

  def signature_url(%WelcomePage{signature: signature} = parent, _args, _resolution) do
    {:ok, Signature.url({signature, parent})}
  end
end
