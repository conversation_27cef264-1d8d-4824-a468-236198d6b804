defmodule AthenaWeb.Resolvers.Companies.CustomDomainSwap do
  @moduledoc """
    Resolvers for object custom domain swap
  """
  alias Gaia.Companies.CustomDomainSwap
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @existing_dns_types [{"a", :a}, {"txt", :txt}, {"mx", :mx}, {"cname", :cname}]

  def resolve(_parent, _args, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}}) do
    custom_domain_swap =
      profile
      |> Repo.preload(:custom_domain_swap)
      |> Map.get(:custom_domain_swap)

    {:ok, custom_domain_swap}
  end

  def resolve(_, _, _), do: {:ok, nil}

  def vercel_configuration(%CustomDomainSwap{custom_domain: domain, root_domain: root_domain}, _, _) do
    case Vercel.domain_dns_configuration(domain, root_domain) do
      {:ok, %{type: type, name: name, value: value} = config} ->
        {:ok, Map.put(config, :records, existing_records_for(root_domain, type, name, value))}

      _ ->
        {:ok, nil}
    end
  end

  def vercel_www_redirect_configuration(%CustomDomainSwap{custom_domain: domain, root_domain: root_domain}, _, _) do
    case Vercel.www_redirect_dns_configuration(domain, domain == root_domain) do
      {:ok, %{type: type, name: name, value: value} = config} ->
        {:ok, Map.put(config, :records, existing_records_for(root_domain, type, name, value))}

      _ ->
        {:ok, nil}
    end
  end

  def can_send_emails?(%CustomDomainSwap{} = custom_domain, _, _) do
    {:ok, Gaia.Companies.can_send_emails_from_custom_domain?(custom_domain)}
  end

  def ses_dkim_configurations(%CustomDomainSwap{root_domain: root}, _, _) do
    if use_live_api?(), do: get_ses_dkim_configurations(root), else: {:ok, [%{configured: true}]}
  end

  defp get_ses_dkim_configurations(root) do
    case AmazonWebService.SES.get_configured_and_recommended_dns_settings(root) do
      {:ok, [%{configured: configured} | _] = configs} ->
        {:ok,
         configurations_with_records(configs, root) ++
           [
             %{
               name: "@",
               value: "v=spf1 include:amazonses.com ~all",
               type: "TXT",
               configured: configured,
               records: existing_records_for(root, "TXT", "@", "v=spf1 include:amazonses.com ~all")
             }
           ]}

      _ ->
        {:ok, nil}
    end
  end

  defp configurations_with_records(configs, root_domain) do
    Enum.map(configs, fn %{name: name, value: value, type: type, configured: configured} ->
      %{
        name: name,
        value: value,
        type: type,
        configured: configured,
        records: existing_records_for(root_domain, type, name, value)
      }
    end)
  end

  def ses_mail_from_configurations(%CustomDomainSwap{root_domain: root}, _, _) do
    if use_live_api?(), do: get_ses_mail_from_configurations(root), else: {:ok, [%{configured: true}]}
  end

  defp get_ses_mail_from_configurations(root) do
    case AmazonWebService.SES.get_mail_from_identity(root) do
      {:ok, ses_mail_from_configured} ->
        {:ok,
         [
           %{
             name: "investorhub-email",
             value: "v=spf1 include:amazonses.com ~all",
             type: "TXT",
             configured: ses_mail_from_configured,
             records: existing_records_for(root, "TXT", "investorhub-email", "v=spf1 include:amazonses.com ~all")
           },
           %{
             name: "investorhub-email",
             value: "feedback-smtp.ap-southeast-2.amazonses.com",
             priority: 10,
             type: "MX",
             configured: ses_mail_from_configured,
             records:
               existing_records_for(root, "MX", "investorhub-email", "feedback-smtp.ap-southeast-2.amazonses.com", 10)
           }
         ]}

      _ ->
        {:ok, nil}
    end
  end

  def ses_dmarc_configurations(%CustomDomainSwap{root_domain: root}, _, _) do
    if use_live_api?(), do: get_ses_dmarc_configurations(root), else: {:ok, [%{configured: true}]}
  end

  defp get_ses_dmarc_configurations(root) do
    ses_dmarc_configured = AmazonWebService.SES.is_dmarc?(root)

    {:ok,
     configurations_with_records(
       [%{name: "_dmarc", value: "v=DMARC1; p=none", type: "TXT", configured: ses_dmarc_configured}],
       root
     )}
  end

  defp existing_records_for(domain, type, name, value, priority \\ nil) do
    domain = if name == "@", do: ~c"#{domain}", else: ~c"#{name}.#{domain}"
    type_atom = get_dns_type_atom(type)

    try do
      case :inet_res.lookup(domain, :in, type_atom) do
        [] ->
          %{matching: [], not_matching: []}

        records when is_list(records) ->
          matched_or_not_matched_records(name, records, value, priority)
      end
    rescue
      e ->
        Sentry.capture_exception(e)
        %{matching: [], not_matching: []}
    end
  end

  defp matched_or_not_matched_records(name, records, value, priority) do
    matching =
      records
      |> Enum.filter(&match_record(&1, value, priority))
      |> Enum.map(&format_record(&1, value))

    not_matching =
      records
      |> Enum.reject(&match_record(&1, value, priority))
      |> Enum.map(&format_record(&1, value, name))
      |> Enum.reject(&(&1 == :skip))

    %{matching: matching, not_matching: not_matching}
  end

  defp match_record({record_priority, record_value}, value, priority) do
    {record_priority, record_value} == {priority, ~c"#{value}"}
  end

  defp match_record(record, value, _p) when is_tuple(record) do
    record_value =
      record
      |> Tuple.to_list()
      |> Enum.join(".")

    "#{record_value}" == "#{value}"
  end

  defp match_record(record, value, p) when not is_binary(record) do
    # record might be a charlist like ~c"v=spf1 include:amazonses.com ~all" or [~c"v=spf1 include:amazonses.com ~all"]
    # convert it to a string first
    record = "#{record}"

    if String.starts_with?(record, "v=spf1"),
      do: match_spf_record(record, value),
      else: match_record("#{record}", "#{value}", p)
  end

  defp match_record(record, value, _p) do
    (String.starts_with?(record, "v=DMARC1;") and String.starts_with?(value, "v=DMARC1;")) or "#{record}" == "#{value}"
  end

  defp match_spf_record(record, value) do
    is_spf_record(record) and is_spf_record(value)
  end

  defp is_spf_record(record) do
    String.starts_with?(record, "v=spf1") and
      (String.ends_with?(record, "~all") or String.ends_with?(record, "-all")) and
      String.contains?(record, "include:amazonses.com")
  end

  defp format_record(_, _, name \\ nil)

  defp format_record({record_priority, record_value}, _v, _n) do
    "#{record_value} (#{record_priority})"
  end

  defp format_record(record, _v, _n) when is_tuple(record) do
    # IP Addresses come through as tuples like {76, 76, 21, 21}
    record
    |> Tuple.to_list()
    |> Enum.join(".")
  end

  defp format_record(record, _v, nil) do
    "#{record}"
  end

  defp format_record(record, value, name) do
    value = "#{value}"
    record = "#{record}"

    # if the record has nothing to do with the one we are looking for, skip it (only for "@" records)
    # take the first 4 characters of the value, lowercase them, and compare them to the first 4 characters of the record
    # if they match, then we can assume that the record is relevant
    first_4_match = String.downcase(String.slice(value, 0, 4)) == String.downcase(String.slice(record, 0, 4))
    if first_4_match and "#{name}" != "@", do: record, else: :skip
  end

  defp get_dns_type_atom(type) do
    type = String.downcase(type)

    case Enum.find(@existing_dns_types, fn {str, _atom} -> str == type end) do
      {_, atom} -> atom
      nil -> String.to_atom(type)
    end
  end

  defp use_live_api? do
    runtime_env = Application.get_env(:helper, :runtime_env)
    allow_domain_testing = System.get_env("ALLOW_DOMAIN_TESTING")
    runtime_env != "development" or (allow_domain_testing != nil and allow_domain_testing == "true")
  end
end
