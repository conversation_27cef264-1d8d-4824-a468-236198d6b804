defmodule AthenaWeb.Resolvers.Editors.GenerateAssetSignedUrl do
  @moduledoc false

  def resolve(_, %{mime_type: mime_type}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{company_profile_id}/editors/assets/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ) do
      {:ok, url}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Unable to generate signed url."
         }}
    end
  end
end
