defmodule AthenaWeb.Resolvers.Analysis.BenchmarkAnalytics do
  @moduledoc """
  BenchmarkAnalytics Query Resolvers
  """

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id} = profile}}
      }) do
    id_template =
      "#{company_profile_id}-#{Timex.format!(start_date, "{YYYY}{0M}{0D}")}-#{Timex.format!(end_date, "{YYYY}{0M}{0D}")}"

    data = Analysis.get_benchmark_data(profile, start_date, end_date)

    current_company_data = Enum.find(data, &(&1.company_profile_id == company_profile_id))

    {:ok,
     %{
       id: id_template,
       current_company_stats:
         Map.merge(current_company_data, %{
           id: "current-#{id_template}",
           hub_actions_top_five: is_top_five?(data, current_company_data, :hub_actions_count),
           signups_top_five: is_top_five?(data, current_company_data, :signups_count),
           views_top_five: is_top_five?(data, current_company_data, :views_count)
         }),
       peer_companies_stats: %{
         id: "peer-#{id_template}",
         hub_actions_count: average(data, :hub_actions_count),
         hub_actions_top_five: false,
         likes_count: average(data, :likes_count),
         nominated_shareholders_count: average(data, :nominated_shareholders_count),
         questions_count: average(data, :questions_count),
         signups_count: average(data, :signups_count),
         signups_top_five: false,
         unique_visitors_count: average(data, :unique_visitors_count),
         views_count: average(data, :views_count),
         views_top_five: false
       }
     }}
  end

  def average(benchmark_data, field) when is_atom(field) do
    count = Enum.count(benchmark_data)
    sum = Enum.reduce(benchmark_data, 0, &(Map.get(&1, field, 0) + &2))
    round(sum / count)
  end

  @doc """
  How to rank multiple of same values:

  Rank  value
  1     100
  2     50
  2     50
  4     30
  4     30
  4     30
  7     20

  In this case there would be 6 companies flagged as top 5.
  """
  def is_top_five?(benchmark_data, company_data, field) when is_atom(field) do
    sorted_benchmark_data = Enum.sort_by(benchmark_data, &Map.get(&1, field, 0), :desc)
    index = Enum.find_index(sorted_benchmark_data, &(Map.get(&1, field, 0) == Map.get(company_data, field, 0)))

    index + 1 <= 5
  end
end
