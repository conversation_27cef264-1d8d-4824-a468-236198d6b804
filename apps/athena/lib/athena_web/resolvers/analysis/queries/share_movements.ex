defmodule AthenaWeb.Resolvers.Analysis.ShareMovements do
  @moduledoc """
    ShareMovement Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with {:ok, formatted_start_date} <-
           Timex.format(start_date, "{YYYY}-{0M}-{0D}"),
         {:ok, formatted_end_date} <- Timex.format(end_date, "{YYYY}-{0M}-{0D}") do
      {:ok,
       company_profile_id
       |> Dashboard.get_share_movements_data(start_date, end_date)
       |> Map.put(:id, "#{formatted_start_date}->#{formatted_end_date}")}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Could not format date"
         }}
    end
  end
end
