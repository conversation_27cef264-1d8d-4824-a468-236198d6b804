defmodule AthenaWeb.Resolvers.Analysis.InvestorHubAnalytics do
  @moduledoc """
    InvestorhubAnalytics Query Resolvers
  """
  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    duration = Timex.diff(start_date, end_date, :days)
    last_period_start_date = Timex.shift(start_date, days: duration)

    total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    last_period_total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    total_signups =
      Analysis.get_total_signups(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    last_period_total_signups =
      Analysis.get_total_signups(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    total_views =
      Analysis.get_total_views(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    last_period_total_views =
      Analysis.get_total_views(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    {:ok,
     %{
       total_signups: total_signups,
       total_unique_visitors: total_unique_visitors,
       total_views: total_views,
       signups_difference: total_signups - last_period_total_signups,
       unique_visitors_difference: total_unique_visitors - last_period_total_unique_visitors,
       total_views_difference: total_views - last_period_total_views
     }}
  end
end
