defmodule AthenaWeb.Resolvers.Analysis.InvestorHubEngagement do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    engagement =
      Analysis.get_engagement(%{
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: end_date,
        can_remove_welcome_email?: true
      })

    {:ok, engagement}
  end
end
