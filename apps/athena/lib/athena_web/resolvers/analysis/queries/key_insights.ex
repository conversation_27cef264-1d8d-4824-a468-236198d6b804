defmodule AthenaWeb.Resolvers.Analysis.KeyInsights do
  @moduledoc """
    KeyInsight Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with {:ok, formatted_start_date} <-
           Timex.format(start_date, "{YYYY}-{0M}-{0D}"),
         {:ok, formatted_end_date} <- Timex.format(end_date, "{YYYY}-{0M}-{0D}"),
         %{new: _} = key_insights <-
           Dashboard.get_key_insights_data(company_profile_id, start_date, end_date) do
      {:ok, Map.put(key_insights, :id, "#{formatted_start_date}->#{formatted_end_date}")}
    else
      nil ->
        {:ok, nil}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Could not format date"
         }}
    end
  end
end
