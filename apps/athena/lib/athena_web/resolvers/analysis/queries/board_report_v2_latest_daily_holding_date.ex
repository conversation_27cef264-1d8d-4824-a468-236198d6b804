defmodule AthenaWeb.Resolvers.Analysis.BoardReportV2.LatestDailyHoldingDate do
  @moduledoc """
    BoardReportV2.LatestDailyHoldingDate Query Resolver
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Registers

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    {:ok, Registers.get_latest_daily_holding_date_by_company_profile(current_company_profile_id)}
  end
end
