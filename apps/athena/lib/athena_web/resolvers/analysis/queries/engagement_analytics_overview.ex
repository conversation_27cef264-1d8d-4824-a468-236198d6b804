defmodule AthenaWeb.Resolvers.Analysis.EngagementAnalyticsOverview do
  @moduledoc """
    EngagementAnalyticsOverview Query Resolvers
  """
  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  @seven %{
    total_unique_visitors_offset: 98,
    total_leads_offset: 85,
    total_converted_shareholders_offset: 64,
    total_nominated_shareholders_offset: 59,
    unique_visitors_difference_offset: 87,
    leads_difference_offset: 24,
    converted_shareholders_difference_offset: 16
  }

  @thirty %{
    total_unique_visitors_offset: 276,
    total_leads_offset: 165,
    total_converted_shareholders_offset: 122,
    total_nominated_shareholders_offset: 85,
    unique_visitors_difference_offset: 96,
    leads_difference_offset: 52,
    converted_shareholders_difference_offset: 35
  }

  @ninety %{
    total_unique_visitors_offset: 828,
    total_leads_offset: 487,
    total_converted_shareholders_offset: 366,
    total_nominated_shareholders_offset: 255,
    unique_visitors_difference_offset: 288,
    leads_difference_offset: 156,
    converted_shareholders_difference_offset: 105
  }

  @oneeighty %{
    total_unique_visitors_offset: 1543,
    total_leads_offset: 891,
    total_converted_shareholders_offset: 627,
    total_nominated_shareholders_offset: 411,
    unique_visitors_difference_offset: 317,
    leads_difference_offset: 187,
    converted_shareholders_difference_offset: 103
  }

  defp get_random_offsets(days) do
    %{
      total_unique_visitors_offset: 8 * days + :rand.uniform(12),
      total_leads_offset: 5 * days + :rand.uniform(10),
      total_converted_shareholders_offset: 3 * days + :rand.uniform(7),
      total_nominated_shareholders_offset: 2 * days + :rand.uniform(13),
      unique_visitors_difference_offset: 2 * days + :rand.uniform(5),
      leads_difference_offset: days + :rand.uniform(10),
      converted_shareholders_difference_offset: days + :rand.uniform(5)
    }
  end

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile: %Profile{id: company_profile_id, is_demo: true}} = profile_user
        }
      }) do
    duration = Timex.diff(start_date, end_date, :days)
    last_period_start_date = Timex.shift(start_date, days: duration)

    offset =
      case duration do
        -7 -> @seven
        -30 -> @thirty
        -90 -> @ninety
        -180 -> @oneeighty
        _ -> get_random_offsets(-duration)
      end

    shareholders_and_leads_list =
      Analysis.get_engagement_analysis_overview_audience_breakdown(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    media_announcements =
      Analysis.get_media_announcements_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: profile_user,
        required_permissions: ["interactions_media_announcements.admin", "interactions_media_announcements.editor"]
      })

    media_updates =
      Analysis.get_media_updates_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: profile_user,
        required_permission: "interactions_media_updates.admin"
      })

    emails_list =
      Analysis.get_emails_for_date_range_with_permission_check(
        start_date,
        end_date,
        company_profile_id,
        profile_user,
        "comms_emails.admin"
      )

    last_period_shareholders_and_leads_list =
      Analysis.get_engagement_analysis_overview_audience_breakdown(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    last_period_total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    trailing_average =
      Analysis.get_conversion_rate_sma(
        start_date,
        end_date,
        company_profile_id
      )

    unique_visitors_by_date = Analysis.unique_visitors_by_date(start_date, company_profile_id)

    with %{
           total_leads: total_leads,
           total_converted_shareholders: total_converted_shareholders,
           total_nominated_shareholders: total_nominated_shareholders
         } <- Analysis.get_total_audiences_breakdown(shareholders_and_leads_list),
         %{
           total_leads: last_period_total_leads,
           total_converted_shareholders: last_period_total_converted_shareholders
         } <- Analysis.get_total_audiences_breakdown(last_period_shareholders_and_leads_list) do
      {:ok,
       %{
         total_unique_visitors: total_unique_visitors + offset.total_unique_visitors_offset,
         total_leads: total_leads + offset.total_leads_offset,
         total_converted_shareholders: total_converted_shareholders + offset.total_converted_shareholders_offset,
         total_nominated_shareholders: total_nominated_shareholders + offset.total_nominated_shareholders_offset,
         unique_visitors_difference:
           total_unique_visitors - last_period_total_unique_visitors + offset.unique_visitors_difference_offset,
         leads_difference: total_leads - last_period_total_leads + offset.leads_difference_offset,
         converted_shareholders_difference:
           total_converted_shareholders - last_period_total_converted_shareholders +
             offset.converted_shareholders_difference_offset,
         audiences_breakdown:
           Analysis.merge_media_announcements_and_updates_is_demo(
             start_date,
             end_date,
             shareholders_and_leads_list,
             trailing_average,
             unique_visitors_by_date,
             media_announcements,
             media_updates,
             emails_list
           )
       }}
    end
  end

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}} = profile_user}
      }) do
    duration = Timex.diff(start_date, end_date, :days)
    last_period_start_date = Timex.shift(start_date, days: duration)

    shareholders_and_leads_list =
      Analysis.get_engagement_analysis_overview_audience_breakdown(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    media_announcements =
      Analysis.get_media_announcements_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: profile_user,
        required_permissions: ["interactions_media_announcements.admin", "interactions_media_announcements.editor"]
      })

    media_updates =
      Analysis.get_media_updates_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: profile_user,
        required_permission: "interactions_media_updates.admin"
      })

    emails_list =
      Analysis.get_emails_for_date_range_with_permission_check(
        start_date,
        end_date,
        company_profile_id,
        profile_user,
        "comms_emails.admin"
      )

    last_period_shareholders_and_leads_list =
      Analysis.get_engagement_analysis_overview_audience_breakdown(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    last_period_total_unique_visitors =
      Analysis.get_total_unique_visitors(%{
        start_date: last_period_start_date,
        end_date: start_date,
        company_profile_id: company_profile_id
      })

    trailing_average =
      Analysis.get_conversion_rate_sma(
        start_date,
        end_date,
        company_profile_id
      )

    unique_visitors_by_date = Analysis.unique_visitors_by_date(start_date, company_profile_id)

    with %{
           total_leads: total_leads,
           total_converted_shareholders: total_converted_shareholders,
           total_nominated_shareholders: total_nominated_shareholders
         } <- Analysis.get_total_audiences_breakdown(shareholders_and_leads_list),
         %{
           total_leads: last_period_total_leads,
           total_converted_shareholders: last_period_total_converted_shareholders
         } <- Analysis.get_total_audiences_breakdown(last_period_shareholders_and_leads_list) do
      {:ok,
       %{
         total_unique_visitors: total_unique_visitors,
         total_leads: total_leads,
         total_converted_shareholders: total_converted_shareholders,
         total_nominated_shareholders: total_nominated_shareholders,
         unique_visitors_difference: total_unique_visitors - last_period_total_unique_visitors,
         leads_difference: total_leads - last_period_total_leads,
         converted_shareholders_difference: total_converted_shareholders - last_period_total_converted_shareholders,
         audiences_breakdown:
           Analysis.merge_media_announcements_and_updates(
             start_date,
             end_date,
             shareholders_and_leads_list,
             trailing_average,
             unique_visitors_by_date,
             media_announcements,
             media_updates,
             emails_list
           )
       }}
    end
  end
end
