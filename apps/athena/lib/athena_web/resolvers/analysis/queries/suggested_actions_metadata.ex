defmodule AthenaWeb.Resolvers.Analysis.SuggestedActionsMetadata do
  @moduledoc """
  A resolver to collect the data required to render the Suggested Actions section in Athena appropriately.
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Webinars

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{id: company_profile_id, ai_summaries_enabled: ai_summaries_enabled}
          }
        }
      }) do
    media_ids =
      company_profile_id
      |> Interactions.list_media_by_company_profile_id()
      |> Enum.map(& &1.id)

    active_question_counts = Interactions.get_total_active_question_count_of_media(media_ids)

    distribution_settings = Flows.get_active_distribution_settings_for_company(company_profile_id)

    maybe_media_update = Interactions.get_latest_media_update_for_company(company_profile_id)

    maybe_announcement_without_video = Interactions.get_latest_announcement_without_video_for_company(company_profile_id)

    maybe_webinar = Webinars.get_latest_webinar_for_company(company_profile_id)

    (
      last_update_posted_date =
        case maybe_media_update do
          %MediaUpdate{posted_at: posted_at} -> posted_at
          _ -> nil
        end

      %{announcements: is_automated_distribution_switched_on} = Flows.map_to_active_flows(distribution_settings)

      active_question_count = Enum.reduce(active_question_counts, 0, fn %{count: q_count}, sum -> sum + q_count end)

      {:ok,
       %{
         id: company_profile_id,
         active_question_count: active_question_count,
         ai_summaries_enabled: ai_summaries_enabled,
         is_automated_distribution_switched_on: is_automated_distribution_switched_on,
         last_update_posted_date: last_update_posted_date,
         last_webinar_scheduled_date: maybe_webinar && maybe_webinar.start_time,
         latest_announcement_without_video: maybe_announcement_without_video && maybe_announcement_without_video.id
       }}
    )
  end

  def resolve(_parent, _args, _resolution), do: {:ok, nil}
end
