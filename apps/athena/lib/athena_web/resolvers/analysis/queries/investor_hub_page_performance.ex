defmodule AthenaWeb.Resolvers.Analysis.InvestorHubPagePerformance do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    page_performance =
      Analysis.get_page_performance(%{
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: end_date
      })

    {:ok, page_performance}
  end
end
