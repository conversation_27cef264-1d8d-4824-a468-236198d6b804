defmodule AthenaWeb.Resolvers.Analysis.ContactsGraphData do
  @moduledoc """
    KeyInsight Query Resolvers
  """

  import Ecto.Query

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    start_date = Timex.to_naive_datetime(start_date)
    end_date = Timex.to_naive_datetime(end_date)

    day_difference = Timex.diff(end_date, start_date, :days)

    date_range =
      Enum.map(0..day_difference, fn offset ->
        Timex.shift(start_date, days: offset)
      end)

    # only consider them a contact if they arent invalidated
    # this is means the graph isn't technically true because all contacts should go down if emails are deleted
    # however this is due to a bigger problem that we dont record when a contact has been invalidated
    # (updated_at isnt enough info because it couldve just been an edit)
    # good luck !
    contacts_breakdown =
      Enum.map(date_range, fn date ->
        %{
          date: date,
          total_emails:
            Gaia.Repo.one(
              from(c in Gaia.Contacts.Contact,
                where: c.inserted_at <= ^date and c.company_profile_id == ^company_profile_id and c.invalidated == false,
                select: count(c.id)
              )
            ),
          hub_emails:
            Gaia.Repo.one(
              from(c in Gaia.Investors.User,
                where: c.inserted_at <= ^date and c.company_profile_id == ^company_profile_id,
                select: count(c.id)
              )
            )
        }
      end)

    {:ok, contacts_breakdown}
  end
end
