defmodule AthenaWeb.Resolvers.Analysis.CurrentCompanyOverview do
  @moduledoc """
    CurrentCompanyOverview Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Markets.Ticker
  alias Gaia.Repo

  def get_current_company_overview(_parent, %{start_date: %Date{} = start_date, end_date: %Date{} = end_date}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{
              id: company_profile_id,
              ticker: %Ticker{listing_key: listing_key, market_key: market_key} = ticker,
              timezone: timezone
            }
          }
        }
      }) do
    daily_holding_timeseries = Dashboard.get_shareholder_counts(company_profile_id, start_date, end_date)

    announcements_mapping =
      %{
        filters: [
          %{key: "listing_key", value: String.upcase(listing_key)},
          %{key: "market_key", value: String.upcase("#{market_key}")},
          %{
            key: "posted_at_greater_than",
            value:
              start_date
              |> Timex.to_datetime(timezone)
              |> Timex.to_naive_datetime()
          },
          %{
            key: "posted_at_less_than",
            value:
              end_date
              |> Timex.to_datetime(timezone)
              |> Timex.end_of_day()
              |> Timex.to_naive_datetime()
          }
        ],
        orders: [
          %{key: "posted_at", value: "asc"},
          %{key: "id", value: "asc"}
        ]
      }
      |> Interactions.media_announcements_query()
      |> Repo.all()
      |> Enum.group_by(&get_posted_date(&1, timezone))

    case Gaia.MarketData.get_timeseries(
           ticker,
           start_date,
           end_date
         ) do
      {:ok, interday_timeseries} ->
        # A temp solution to fix volume data missing because ASX:WSI is also listed on ChiX/Cboe. Refer to function definition for more info
        interday_timeseries =
          Gaia.MarketData.YahooFinance.combine_refinitiv_timeseries_with_chix(
            interday_timeseries,
            ticker,
            start_date,
            end_date
          )

        # Convert list into map to optimise access performance when running Enum.map below
        daily_holding_mapping = Enum.reduce(daily_holding_timeseries, %{}, &Map.put(&2, &1.date, &1))

        {:ok,
         Enum.map(interday_timeseries, fn %{date: date} = ele ->
           daily_holding = Map.get(daily_holding_mapping, date, %{})

           Enum.into(
             %{
               id: "#{company_profile_id}:#{date}",
               announcements: Map.get(announcements_mapping, date, []),
               shareholdings_count: Map.get(daily_holding, :shareholdings_count)
             },
             ele
           )
         end)}

      _ ->
        {:ok, []}
    end
  end

  def get_current_company_overview(_parent, _args, _resolution), do: {:ok, []}

  defp get_posted_date(%MediaAnnouncement{posted_at: posted_at}, timezone) do
    posted_at
    |> Timex.to_datetime("UTC")
    |> Timex.to_datetime(timezone)
    |> Timex.to_date()
  end
end
