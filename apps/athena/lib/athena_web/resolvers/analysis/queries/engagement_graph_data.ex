defmodule AthenaWeb.Resolvers.Analysis.EngagementGraphData do
  @moduledoc """
    KeyInsight Query Resolvers
  """

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    start_date = Timex.to_date(start_date)
    end_date = Timex.to_date(end_date)

    day_difference = Timex.diff(end_date, start_date, :days)

    date_range =
      Enum.map(0..day_difference, fn offset ->
        Timex.shift(start_date, days: offset)
      end)

    ans =
      Analysis.get_engaged_visitors_from_investor_user_actions_and_tracking_tokens(%{
        company_profile_id: company_profile_id,
        date_range: date_range
      })

    {:ok, ans}
  end
end
