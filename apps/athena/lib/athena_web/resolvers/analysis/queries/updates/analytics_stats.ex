defmodule AthenaWeb.Resolvers.Analysis.Updates.AnalyticsStats do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    duration = Timex.diff(start_date, end_date, :days)
    last_period_start_date = Timex.shift(start_date, days: duration)

    analytics_stats =
      Analysis.get_analytics_stats_for_media_type(
        %{
          start_date: start_date,
          end_date: end_date,
          last_period_start_date: last_period_start_date,
          company_profile_id: company_profile_id
        },
        :update
      )

    {:ok, analytics_stats}
  end
end
