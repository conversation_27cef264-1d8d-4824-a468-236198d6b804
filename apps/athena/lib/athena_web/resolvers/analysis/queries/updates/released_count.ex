defmodule AthenaWeb.Resolvers.Analysis.Updates.ReleasedCount do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    released_count =
      Analysis.get_updates_released_in_date_range(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    {:ok, released_count}
  end
end
