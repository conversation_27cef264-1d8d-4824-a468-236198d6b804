defmodule AthenaWeb.Resolvers.Analysis.AnnouncementEngagement do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{id: announcement_id, date_range: date_range}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    utc_now = NaiveDateTime.utc_now(:second)
    start_date = Analysis.get_start_date_from_date_range(utc_now, date_range)

    engagement =
      Analysis.get_media_engagement(%{
        media_id: announcement_id,
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: utc_now,
        media_type: "MediaAnnouncement"
      })

    {:ok, engagement}
  end
end
