defmodule AthenaWeb.Resolvers.Analysis.MediaEngagement do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{id: media_id, date_range: date_range, media_type: media_type} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id, timezone: timezone}}
        }
      }) do
    start_date = Map.get(args, :start_date) || DateTime.now!(timezone)
    end_date = Analysis.get_end_date_from_date_range(start_date, date_range)

    engagement_by_date =
      Analysis.get_media_engagement(%{
        media_id: media_id,
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: end_date,
        media_type: media_type
      })

    # Get total views and total unique visitors
    total_views = Enum.sum(Enum.map(engagement_by_date, & &1.total_views))
    total_unique_visitors = Enum.sum(Enum.map(engagement_by_date, & &1.total_unique_visitors))

    {:ok, %{total_views: total_views, total_unique_visitors: total_unique_visitors, engagement: engagement_by_date}}
  end
end
