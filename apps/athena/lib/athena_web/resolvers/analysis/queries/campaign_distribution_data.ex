defmodule AthenaWeb.Resolvers.Analysis.CampaignDistributionGraphData do
  @moduledoc """
    KeyInsight Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Comms.Helper.EmailMergeTags
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{last_number_of_email_campaigns: last_number_of_email_campaigns}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok, get_campaign_distribution_data(last_number_of_email_campaigns, company_profile_id)}
  end

  defp get_campaign_distribution_data(last_number_of_email_campaigns, company_profile_id) do
    emails =
      %{
        filters: [
          %{key: "company_profile_id", value: company_profile_id},
          %{key: "is_completed", value: "true"}
        ],
        orders: [
          %{key: "limit", value: last_number_of_email_campaigns},
          %{key: "updated_at", value: "desc"}
        ]
      }
      |> Gaia.Comms.emails_query_by_company_profile_id(company_profile_id)
      |> Gaia.Repo.all()
      |> Gaia.Repo.preload(media: [:media_announcement, :media_update])

    emails
    |> Enum.map(fn email ->
      total = Gaia.Comms.count_total_recipient_events_by_email(email.id, :Total)

      click_through_rate =
        case total do
          0 ->
            0

          _ ->
            :erlang.round(Gaia.Comms.count_total_recipient_events_by_email(email.id, :Click) / total * 100)
        end

      open_rate =
        case total do
          0 ->
            0

          _ ->
            :erlang.round(Gaia.Comms.count_total_recipient_events_by_email(email.id, :Open) / total * 100)
        end

      preloaded_email = Gaia.Repo.preload(email, media: [:media_announcement, :media_update])

      merge_tags =
        preloaded_email
        |> Map.get(:media)
        |> Gaia.Comms.build_subject_merge_tags_for_email_media()

      %{
        date: Timex.to_date(email.sent_at),
        campaign_name:
          EmailMergeTags.replace_merge_tag(
            struct(%EmailMergeTags{}, merge_tags),
            email.campaign_name
            |> String.replace(~r/{{[\n| ]*announcement_title[\n| ]*}}/, "{{ interactive_media_title }}")
            |> String.replace(~r/{{[\n| ]*update_title[\n| ]*}}/, "{{ interactive_media_title }}")
          ),
        click_through_rate: click_through_rate,
        open_rate: open_rate,
        total_sent: total,
        type: Gaia.Comms.get_email_type(preloaded_email)
      }
    end)
    |> Enum.sort(&Date.after?(&1.date, &2.date))
    |> Enum.take(last_number_of_email_campaigns)
    |> Enum.reverse()
  end
end
