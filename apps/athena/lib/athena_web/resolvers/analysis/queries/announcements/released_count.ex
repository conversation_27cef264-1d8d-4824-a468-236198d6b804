defmodule AthenaWeb.Resolvers.Analysis.Announcements.ReleasedCount do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{ticker: %{listing_key: listing_key, market_key: market_key}}
          }
        }
      }) do
    released_count =
      Analysis.get_announcements_released_in_date_range(%{
        start_date: start_date,
        end_date: end_date,
        listing_key: listing_key,
        market_key: Atom.to_string(market_key)
      })

    {:ok, released_count}
  end
end
