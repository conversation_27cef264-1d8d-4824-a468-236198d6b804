defmodule AthenaWeb.Resolvers.Analysis.Announcements.TopThreeViewed do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    top_three_viewed =
      Analysis.get_top_three_viewed_announcements(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    {:ok, top_three_viewed}
  end
end
