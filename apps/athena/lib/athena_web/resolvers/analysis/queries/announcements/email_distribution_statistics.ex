defmodule AthenaWeb.Resolvers.Analysis.Announcements.EmailDistributionStatistics do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    {:ok,
     Analysis.get_email_distribution_statistics_for_media_type(
       %{
         start_date: start_date,
         end_date: end_date,
         company_profile_id: company_profile_id
       },
       :announcement
     )}
  end
end
