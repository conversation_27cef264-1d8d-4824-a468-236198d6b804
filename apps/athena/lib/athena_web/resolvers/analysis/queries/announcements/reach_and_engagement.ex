defmodule AthenaWeb.Resolvers.Analysis.Announcements.ReachAndEngagement do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    reach_and_engagement =
      Analysis.get_announcements_reach_and_engagement(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    {:ok, reach_and_engagement}
  end
end
