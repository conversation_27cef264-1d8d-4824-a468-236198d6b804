defmodule AthenaWeb.Resolvers.Analysis.InvestorHubSignupBreakdown do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    signup_breakdown =
      Analysis.get_signup_breakdown(%{
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: end_date
      })

    {:ok, signup_breakdown}
  end
end
