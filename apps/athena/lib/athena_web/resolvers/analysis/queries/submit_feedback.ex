defmodule AthenaWeb.Resolvers.Analysis.SubmitFeedback do
  @moduledoc """
    KeyInsight Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User

  def resolve(_parent, %{message: message}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{name: company_name},
            user: %User{first_name: first_name, last_name: last_name}
          }
        }
      }) do
    Gaia.Notifications.Email.deliver(
      EmailTransactional.Operations,
      :notify_devs,
      [
        "#{first_name} #{last_name} from #{company_name} has given feedback for key metrics that #{message}"
      ]
    )

    {:ok, true}
  end
end
