defmodule AthenaWeb.Resolvers.Analysis.InvestorHubMostEngagedInvestors do
  @moduledoc false

  alias Gaia.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    most_engaged_investors =
      Analysis.get_most_engaged_investors(%{
        company_profile_id: company_profile_id,
        start_date: start_date,
        end_date: end_date
      })

    {:ok, most_engaged_investors}
  end
end
