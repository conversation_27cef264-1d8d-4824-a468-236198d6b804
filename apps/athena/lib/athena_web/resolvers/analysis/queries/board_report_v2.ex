defmodule AthenaWeb.Resolvers.Analysis.BoardReportV2 do
  @moduledoc """
    BoardReportV2 Query Resolver
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Jobs.GenerateBoardReportV2

  def resolve(_, %{end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{id: id, profile_id: current_company_profile_id}}
      }) do
    GenerateBoardReportV2.enqueue(%{
      company_profile_id: current_company_profile_id,
      end_date: end_date,
      profile_user_id: id
    })

    {:ok, true}
  end
end
