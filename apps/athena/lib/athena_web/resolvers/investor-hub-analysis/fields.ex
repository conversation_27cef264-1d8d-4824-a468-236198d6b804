defmodule AthenaWeb.Resolvers.InvestorHubAnalysis.Fields do
  @moduledoc false
  use Timex

  def current_month_investor_hub_stats(%{company_profile_id: company_profile_id}, _, _) do
    now = Helper.ExDay.now()

    signups_this_month = Gaia.Investors.company_investor_sign_ups_by_month(company_profile_id, now)

    views = Gaia.Tracking.count_all_investor_hub_page_views_for_month(company_profile_id, now)

    visitors =
      Gaia.Tracking.count_unique_all_investor_hub_page_views_for_month(
        company_profile_id,
        now
      )

    {:ok,
     %{
       title: "#{Timex.format!(now, "%B", :strftime)} (Current)",
       signups: signups_this_month,
       views: views,
       visitors: visitors
     }}
  end

  def previous_month_investor_hub_stats(%{company_profile_id: company_profile_id}, _, _) do
    now = Helper.ExDay.now()
    last_month = Timex.shift(now, months: -1)

    signups_last_month = Gaia.Investors.company_investor_sign_ups_by_month(company_profile_id, last_month)

    views =
      Gaia.Tracking.count_all_investor_hub_page_views_for_month(
        company_profile_id,
        last_month
      )

    visitors =
      Gaia.Tracking.count_unique_all_investor_hub_page_views_for_month(
        company_profile_id,
        last_month
      )

    {:ok,
     %{
       title: Timex.format!(last_month, "%B", :strftime),
       signups: signups_last_month,
       views: views,
       visitors: visitors
     }}
  end

  def overall_investor_hub_stats(%{company_profile_id: company_profile_id}, _, _) do
    overall_sign_ups = Gaia.Investors.company_investor_sign_ups(company_profile_id)

    views = Gaia.Tracking.count_all_investor_hub_page_views(company_profile_id)

    visitors = Gaia.Tracking.count_unique_all_investor_hub_page_views(company_profile_id)

    {:ok,
     %{
       title: "Overall",
       signups: overall_sign_ups,
       views: views,
       visitors: visitors
     }}
  end

  # Announcements
  def count_interactive_announcement_views(%{id: announcement_id}, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }),
      do: {:ok, Gaia.Tracking.count_interactive_announcement_views(announcement_id, company_profile_id)}

  def count_interactive_announcement_views(_, _, _), do: {:error, "Unable to get total announcement views"}
end
