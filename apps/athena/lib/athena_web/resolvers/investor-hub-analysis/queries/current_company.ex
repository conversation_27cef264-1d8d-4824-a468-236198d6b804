defmodule AthenaWeb.Resolvers.InvestorHubAnalysis.CurrentCompany do
  @moduledoc false
  use Gettext, backend: AthenaWeb.Gettext

  def resolve(_, _, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }),
      do: {:ok, %{company_profile_id: id}}

  def resolve(_, _, _), do: {:error, gettext("Unable to get current company")}
end
