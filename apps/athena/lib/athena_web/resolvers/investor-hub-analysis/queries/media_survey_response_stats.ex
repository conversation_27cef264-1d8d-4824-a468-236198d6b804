defmodule AthenaWeb.Resolvers.InvestorHubAnalysis.MediaSurveyResponseStats do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  @gettext_context "Media survey response stats query"

  def resolve(_parent, %{media_id: media_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %Media{company_profile_id: company_profile_id} <- Interactions.get_media(media_id),
         true <- company_profile_id == current_company_profile_id do
      {:ok, Interactions.get_media_survey_response_stats(media_id)}
    else
      false ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are unauthorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Oops! Something went wrong."
             )
         }}
    end
  end
end
