defmodule AthenaWeb.Resolvers.InvestorHubAnalysis.MediaStats do
  @moduledoc false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Tracking

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     %{
       comments: Interactions.count_total_media_comments_for_company(current_company_profile_id),
       reactions: Interactions.count_total_media_likes_for_company(current_company_profile_id),
       survey_responses: Interactions.count_total_distinct_survey_responses_for_company(current_company_profile_id),
       views: Tracking.count_total_interactive_announcements_views_for_company(current_company_profile_id),
       visitors: Tracking.count_total_unique_interactive_announcements_views_for_company(current_company_profile_id)
     }}
  end
end
