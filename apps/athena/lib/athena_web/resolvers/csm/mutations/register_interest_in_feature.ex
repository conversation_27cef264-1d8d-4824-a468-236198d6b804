defmodule AthenaWeb.Resolvers.Webinars.RegisterInterestInFeature do
  @moduledoc false
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Slack.Message

  def resolve(_parent, %{feature_id: feature_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: company_profile, user: current_user}}
      }) do
    feature_messages = %{
      reports:
        ":man-raising-hand: #{current_user.first_name} #{current_user.last_name} (#{current_user.email}) from #{company_profile.name} just requested access to reports.",
      webinars:
        ":man-raising-hand: #{current_user.first_name} #{current_user.last_name} (#{current_user.email}) from #{company_profile.name} just requested access to webinars."
    }

    case Map.get(feature_messages, String.to_existing_atom(feature_id)) do
      nil ->
        {:error, %Helper.AbsintheError{message: gettext("Feature not found"), error: :not_found}}

      message ->
        send_slack_notification(message)
        {:ok, true}
    end
  end

  defp send_slack_notification(message) do
    if Application.get_env(:helper, :runtime_env) == "production" do
      Message.send(%{text: message}, :csm_channel_slack_webhook_url)
    end
  end
end
