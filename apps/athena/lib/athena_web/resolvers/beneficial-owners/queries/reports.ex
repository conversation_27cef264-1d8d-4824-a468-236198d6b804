defmodule AthenaWeb.Resolvers.BeneficialOwners.Reports do
  @moduledoc false

  use Helper.Pipe

  alias Absinthe.Relay.Connection
  alias Gaia.BeneficialOwners
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    case options
         |> BeneficialOwners.beneficial_owners_reports_query(current_company_profile_id)
         |> Connection.from_query(&Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  def total(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    {:ok,
     options
     |> BeneficialOwners.beneficial_owners_reports_query(current_company_profile_id)
     |> Repo.aggregate(:count)}
  end

  def total(_, _, _) do
    {:error, %Helper.AbsintheError{error: "Pattern match not found", message: "Error retrieving total"}}
  end
end
