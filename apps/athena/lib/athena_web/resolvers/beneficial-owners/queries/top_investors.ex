defmodule AthenaWeb.Resolvers.BeneficialOwners.TopInvestors do
  @moduledoc false

  alias Gaia.BeneficialOwners
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{report_id: report_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    report =
      BeneficialOwners.get_report_by!(
        company_profile_id: current_company_profile_id,
        id: report_id
      )

    details = BeneficialOwners.report_details_for_report(report.id, Map.get(args, :query, nil))

    all_investors =
      details
      |> BeneficialOwners.ReportDetail.to_investors_detail()
      |> Enum.with_index()
      |> Enum.map(fn {investor, index} -> Map.put(investor, :rank, index + 1) end)

    investors_summary = BeneficialOwners.ReportDetail.to_summary(all_investors)

    investors_grouped_by_registered_holder_name =
      all_investors
      |> Enum.group_by(& &1.registered_holder_name)
      |> Enum.map(fn {registered_holder_name, investors} ->
        grouped_holdings_sum = Enum.sum(Enum.map(investors, &check_is_safe_number(&1.beneficial_owner_holdings)))

        grouped_holdings_change = Enum.sum(Enum.map(investors, &check_is_safe_number(&1.holdings_change)))

        grouped_holdings_percentage = Enum.sum(Enum.map(investors, &check_is_safe_number(&1.percentange_of_holdings)))

        %{
          registered_holder_name: registered_holder_name,
          grouped_holdings_sum: grouped_holdings_sum,
          grouped_holdings_change: grouped_holdings_change,
          grouped_holdings_percentage: grouped_holdings_percentage,
          unmasked_investors: investors
        }
      end)
      |> Enum.sort_by(& &1.grouped_holdings_sum, :desc)
      |> Enum.with_index()
      |> Enum.map(fn {group, index} -> Map.put(group, :grouped_rank, index + 1) end)
      |> Enum.reduce([], fn group, acc ->
        temp_list =
          Enum.map(group.unmasked_investors, fn row ->
            Map.merge(row, %{
              grouped_rank: group.grouped_rank,
              grouped_holdings_sum: group.grouped_holdings_sum,
              grouped_holdings_change: group.grouped_holdings_change,
              grouped_holdings_percentage: group.grouped_holdings_percentage
            })
          end)

        acc ++ temp_list
      end)

    top_investors =
      all_investors
      |> BeneficialOwners.ReportDetail.dedup_by_investor_manager()
      |> Enum.sort_by(& &1.beneficial_owner_holdings, :desc)
      |> Enum.with_index()
      |> Enum.map(fn {investor, index} -> Map.put(investor, :rank, index + 1) end)

    top_summary = BeneficialOwners.ReportDetail.to_summary(top_investors)

    {:ok,
     %{
       top_investors: top_investors,
       top_summary: top_summary,
       all_summary: investors_summary,
       investors_grouped_by_holders: investors_grouped_by_registered_holder_name
     }}
  end

  defp check_is_safe_number(number) do
    if is_number(number) do
      number
    else
      0
    end
  end
end
