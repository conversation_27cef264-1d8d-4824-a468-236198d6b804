defmodule AthenaWeb.Resolvers.BeneficialOwners.Queries.AccountsByHoldingIdsWithLatestHolding do
  @moduledoc false

  alias Gaia.BeneficialOwners.Account
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{holding_ids: holding_ids}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id}}
      }) do
    {:ok, Gaia.BeneficialOwners.get_accounts_and_latest_holding_by_holding_ids(holding_ids, profile_id)}
  end

  def batch_resolve_latest_holding(%Account{id: id, company_profile_id: company_profile_id}, _, _) do
    Absinthe.Resolution.Helpers.batch(
      {Gaia.BeneficialOwners, :batch_get_account_latest_holding, company_profile_id},
      id,
      fn batch_results ->
        {:ok, Map.get(batch_results, id)}
      end
    )
  end
end
