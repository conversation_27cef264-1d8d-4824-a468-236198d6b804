defmodule AthenaWeb.Resolvers.BeneficialOwners.Report do
  @moduledoc false

  alias Gaia.BeneficialOwners
  alias Gaia.Companies.ProfileUser
  alias Gaia.Registers

  def resolve(_, %{report_id: report_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    {:ok,
     BeneficialOwners.get_report_by!(
       company_profile_id: current_company_profile_id,
       id: report_id
     )}
  end

  def metadata(%{id: report_id, report_date: report_date}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    report =
      BeneficialOwners.get_report_by!(
        company_profile_id: current_company_profile_id,
        id: report_id
      ) || %{}

    previous = BeneficialOwners.get_previous_report!(report) || %{}
    next = BeneficialOwners.get_next_report!(report) || %{}

    total_shares =
      Map.get(report, :total_shares) ||
        Registers.total_shares_for_company_by_date_or_most_recent(
          current_company_profile_id,
          report_date
        ) || 0

    {:ok,
     %{
       id: report_id,
       previous_report_id: Map.get(previous, :id),
       previous_report_date: Map.get(previous, :report_date),
       next_report_id: Map.get(next, :id),
       company_total_shares: total_shares
     }}
  end
end
