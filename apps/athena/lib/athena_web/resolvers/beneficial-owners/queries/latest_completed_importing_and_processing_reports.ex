defmodule AthenaWeb.Resolvers.BeneficialOwners.LatestCompletedImportingAndProcessingBenficialOwnersReports do
  @moduledoc false

  use Helper.Pipe

  alias Gaia.BeneficialOwners
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    latest_completed_report =
      BeneficialOwners.get_latest_completed_beneficial_owners_report_details(current_company_profile_id)

    current_beneficial_owners_report_request_importing =
      BeneficialOwners.get_current_beneficial_owners_report_request_importing(current_company_profile_id)

    current_beneficial_owners_report_upload_processing =
      BeneficialOwners.get_current_beneficial_owners_report_upload_processing(current_company_profile_id)

    has_current_importing_report = if current_beneficial_owners_report_request_importing, do: true, else: false

    has_current_processing_report = if current_beneficial_owners_report_upload_processing, do: true, else: false

    {
      :ok,
      %{
        latest_completed_report: latest_completed_report,
        has_current_processing_report: has_current_processing_report,
        has_current_importing_report: has_current_importing_report,
        current_processing_report: current_beneficial_owners_report_upload_processing,
        current_importing_report: current_beneficial_owners_report_request_importing
      }
    }
  end
end
