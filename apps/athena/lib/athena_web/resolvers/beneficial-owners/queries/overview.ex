defmodule AthenaWeb.Resolvers.BeneficialOwners.Overview do
  @moduledoc false

  alias Gaia.BeneficialOwners
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  @uk "United Kingdom"
  @aus "Australia"

  def resolve(_, %{report_id: report_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            profile: %Profile{ticker: %{market_key: market_key}}
          }
        }
      }) do
    current_report =
      BeneficialOwners.get_report_by!(
        company_profile_id: current_company_profile_id,
        id: report_id
      )

    previous_report = BeneficialOwners.get_previous_completed_report!(current_report)
    next_report = BeneficialOwners.get_next_completed_report!(current_report)

    %{report_details: details} = preloaded_report = Repo.preload(current_report, :report_details)

    investors =
      details
      |> BeneficialOwners.ReportDetail.to_investors_detail()
      |> BeneficialOwners.ReportDetail.dedup_by_investor_manager()

    top_20_investors =
      investors
      |> Enum.sort_by(& &1.beneficial_owner_holdings, :desc)
      |> Enum.take(20)
      |> Enum.with_index()
      |> Enum.map(fn {investor, index} -> Map.put(investor, :rank, index + 1) end)

    top_movers =
      investors
      |> Enum.filter(&(&1.absolute_change > 0))
      |> Enum.sort_by(& &1.absolute_change, :desc)
      |> Enum.with_index()
      |> Enum.map(fn {investor, index} -> Map.put(investor, :rank, index + 1) end)

    count_countries = count_rows_per_type(investors)

    is_uk_market = is_uk?(market_key)

    count_states =
      investors
      |> Enum.filter(&(&1.investment_manager_country == if(is_uk_market, do: @uk, else: @aus)))
      |> count_rows_per_type(:investment_manager_state)

    report_details =
      BeneficialOwners.beneficial_owners_report_details_by_report(preloaded_report)

    {:ok,
     %{
       report_date: current_report.report_date,
       previous_report: previous_report,
       next_report: next_report,
       report_info: report_details,
       top_investors: top_20_investors,
       top_movers: top_movers,
       count_countries: count_countries,
       count_states: count_states
     }}
  end

  defp count_rows_per_type(rows, field \\ :investment_manager_country) do
    rows
    |> Enum.group_by(&Map.get(&1, field))
    |> Enum.reduce([], fn
      {nil, rows}, acc -> [Map.put(%{count: length(rows)}, field, "Not Available") | acc]
      {"", rows}, acc -> [Map.put(%{count: length(rows)}, field, "Not Available") | acc]
      {item, rows}, acc -> [Map.put(%{count: length(rows)}, field, item) | acc]
    end)
    |> Enum.sort_by(& &1.count, :desc)
  end

  defp is_uk?(market_key) when market_key in [:aqse, :lse], do: true
  defp is_uk?(_), do: false
end
