defmodule AthenaWeb.Resolvers.BeneficialOwners.GenerateDisclosedInterestDocumentSignedUrl do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.Report
  alias Gaia.Companies.ProfileUser

  @gettext_context "Generate beneficial owners disclosed interest document signed url mutation"

  def resolve(_parent, %{report_id: report_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Report{company_profile_id: company_profile_id, disclosed_interest_document_filename: filename} = report <-
           BeneficialOwners.get_report_by!(
             company_profile_id: current_company_profile_id,
             id: report_id
           ),
         true <- company_profile_id == current_company_profile_id,
         %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             URI.encode(
               "uploads/company_profile/#{company_profile_id}/beneficial_owners/reports/#{report_id}/disclosed_interest_documents/#{filename}"
             ),
             expires: 1800,
             headers: [],
             verb: "GET"
           ),
         {:ok, _} <-
           BeneficialOwners.upsert_report(report, %{
             disclosed_interest_document_url: url
           }) do
      {:ok, url}
    else
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Report does not exist."
         )}

      false ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are unauthorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to generate signed url."
             )
         }}
    end
  end
end
