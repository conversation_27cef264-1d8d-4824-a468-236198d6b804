defmodule AthenaWeb.Resolvers.BeneficialOwners.NotifyOfBeneficialOwnersReportRequest do
  @moduledoc false

  use Helper.Pipe

  alias Gaia.BeneficialOwners.Report

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{name: company_name},
            profile_id: current_company_profile_id,
            user: %Gaia.Companies.User{first_name: first_name, last_name: last_name, email: email}
          }
        }
      }) do
    with {:ok, %Report{}} <-
           Gaia.BeneficialOwners.create_report(%{type: :importing, company_profile_id: current_company_profile_id}),
         :ok <-
           notify_via_slack(current_company_profile_id, company_name, first_name, last_name, email) do
      {:ok, true}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  defp notify_via_slack(company_profile_id, company_name, first_name, last_name, email) do
    # comment out this and the env in secrets.yml to test in local
    Slack.Message.send(
      %{
        text:
          "#{first_name} #{last_name} (#{email}) from #{company_name} (#{company_profile_id}) has requested a beneficial owners report."
      },
      :beneficial_owners_report_requests_slack_webhook_url
    )
  end
end
