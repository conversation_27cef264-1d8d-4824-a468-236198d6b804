defmodule AthenaWeb.Resolvers.BeneficialOwners.UploadBeneficialOwnersReport do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.BeneficialOwners.Report

  @gettext_context "UploadBeneficialOwnersReport mutation resolver"

  def resolve(_, %{file: file}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{name: company_name},
            profile_id: current_company_profile_id,
            user: %Gaia.Companies.User{first_name: first_name, last_name: last_name, email: email}
          }
        }
      }) do
    filename =
      "#{current_company_profile_id}_#{String.replace(company_name, " ", "_")}_#{Date.to_string(Date.utc_today())}"

    with {:ok, %GoogleApi.Drive.V3.Model.File{id: file_id}} <-
           GoogleAPI.Drive.create_simple(filename, file.path, "text/csv"),
         {:ok, _} <-
           Gaia.Jobs.SetGoogleDriveFilePermissions.enqueue(%{
             "file_id" => file_id,
             "emails" => [
               "<EMAIL>",
               "<EMAIL>",
               "<EMAIL>",
               "<EMAIL>",
               "<EMAIL>"
             ],
             "role" => "writer"
           }),
         {:ok, %Report{}} <-
           Gaia.BeneficialOwners.create_report(%{
             type: :processing,
             company_profile_id: current_company_profile_id,
             is_user_uploaded: true
           }),
         :ok <- notify_via_slack(current_company_profile_id, company_name, first_name, last_name, email, file_id) do
      {:ok, true}
    else
      {:error, _} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Could not upload file."
         )}

      _ ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Unexpected error."
         )}
    end
  end

  defp notify_via_slack(company_profile_id, company_name, first_name, last_name, email, file_id) do
    # comment out this and the env in secrets.yml to test in local
    Slack.Message.send(
      %{
        text:
          "#{first_name} #{last_name} (#{email}) from #{company_name} (#{company_profile_id}) has uploaded a beneficial owners report to be processed: https://drive.google.com/file/d/#{file_id}"
      },
      :beneficial_owners_report_requests_slack_webhook_url
    )
  end
end
