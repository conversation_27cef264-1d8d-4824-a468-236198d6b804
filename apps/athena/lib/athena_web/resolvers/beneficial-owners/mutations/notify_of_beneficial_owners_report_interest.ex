defmodule AthenaWeb.Resolvers.BeneficialOwners.NotifyOfBeneficialOwnersReportInterest do
  @moduledoc false

  use Helper.Pipe

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{name: company_name},
            profile_id: current_company_profile_id,
            user: %Gaia.Companies.User{first_name: first_name, last_name: last_name, email: email}
          }
        }
      }) do
    {notify_via_slack(current_company_profile_id, company_name, first_name, last_name, email), true}
  end

  defp notify_via_slack(company_profile_id, company_name, first_name, last_name, email) do
    # comment out the env in secrets.yml to test in local
    Slack.Message.send(
      %{
        text:
          "#{first_name} #{last_name} (#{email}) from #{company_name} (#{company_profile_id}) has declared interest in the beneficial owners reports feature."
      },
      :beneficial_owners_report_requests_slack_webhook_url
    )
  end
end
