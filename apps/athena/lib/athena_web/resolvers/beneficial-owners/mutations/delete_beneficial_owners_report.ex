defmodule AthenaWeb.Resolvers.BeneficialOwners.DeleteReport do
  @moduledoc """
  Report goes to sleep forever. Can only delete user uploaded reports that are still being processed.
  """

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.Report
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Report{company_profile_id: company_profile_id} = existing_report <-
           BeneficialOwners.get_beneficial_owners_report_by(%{id: id}),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %Report{} = _deleted_report} <- BeneficialOwners.delete_beneficial_owners_report(existing_report) do
      {:ok, true}
    else
      nil ->
        {:error, "Report does not exist."}

      false ->
        {:error, "You are unauthorised."}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
