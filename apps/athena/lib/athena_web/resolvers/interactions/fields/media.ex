defmodule AthenaWeb.Resolvers.Interactions.MediaFields do
  @moduledoc """
  Resolvers for Media fields
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions.Media
  alias Gaia.Tracking

  @doc """
  Calculates the total impressions across all distribution channels for a media item.
  This includes:
  - Announcement views
  - Update views
  - Social media views (Twitter/X, LinkedIn)
  - Email views
  """
  def total_impressions(%Media{} = media, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    # Calculate total impressions from all sources
    # We're now relying on the associations being preloaded in the main query
    total = calculate_total_impressions(media, current_company_profile_id)

    {:ok, total}
  end

  def total_impressions(_, _, _), do: {:ok, 0}

  # Private helper functions to calculate impressions from different sources
  defp calculate_total_impressions(media, company_profile_id) do
    announcement_views = calculate_announcement_views(media, company_profile_id)
    update_views = calculate_update_views(media, company_profile_id)
    email_views = calculate_email_views(media)
    linkedin_views = calculate_linkedin_views(media)
    twitter_views = calculate_twitter_views(media)

    announcement_views + update_views + email_views + linkedin_views + twitter_views
  end

  # Get LinkedIn impressions from cached analytics_data
  defp calculate_linkedin_views(%{linkedin_social_post: %{analytics_data: %{"impression_count" => count}}})
       when is_integer(count),
       do: count

  defp calculate_linkedin_views(%{linkedin_social_post: %{analytics_data: %{impression_count: count}}})
       when is_integer(count),
       do: count

  defp calculate_linkedin_views(_), do: 0

  # Get Twitter impressions from cached analytics_data
  defp calculate_twitter_views(%{twitter_social_post: %{analytics_data: %{"impression_count" => count}}})
       when is_integer(count),
       do: count

  defp calculate_twitter_views(%{twitter_social_post: %{analytics_data: %{impression_count: count}}})
       when is_integer(count),
       do: count

  defp calculate_twitter_views(_), do: 0

  defp calculate_announcement_views(%{media_announcement: %{id: announcement_id}}, company_profile_id) do
    Tracking.count_interactive_announcement_views(
      announcement_id,
      company_profile_id
    )
  rescue
    _ -> 0
  end

  defp calculate_announcement_views(_, _), do: 0

  defp calculate_update_views(%{media_update: %{id: update_id, slug: slug}}, company_profile_id) do
    Tracking.count_activity_update_views_with_post_id_and_slug(
      update_id,
      URI.encode(slug),
      company_profile_id
    )
  rescue
    _ -> 0
  end

  defp calculate_update_views(_, _), do: 0

  defp calculate_email_views(%{email: %{id: email_id}}) do
    # Get email opens from stats
    Gaia.Comms.count_total_recipient_events_by_email(email_id, :Open) || 0
  rescue
    _ -> 0
  end

  defp calculate_email_views(_), do: 0
end
