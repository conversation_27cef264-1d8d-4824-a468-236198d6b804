defmodule AthenaWeb.Resolvers.Interactions.MediaSharedFields do
  @moduledoc false

  alias G<PERSON>.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.InvestorMediaInteractions

  @media_structs [MediaAnnouncement, MediaUpdate]

  def total_company_comment_count(%{media_id: media_id} = entity, _args, _resolution)
      when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_company_comment_count_of_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_company_comment_count(_, _, _), do: {:ok, 0}

  def total_parent_company_comment_count(%{media_id: media_id} = entity, _args, _resolution)
      when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_parent_company_comment_count_for_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_parent_company_comment_count(_, _, _), do: {:ok, 0}

  def likes(%{media_id: media_id} = entity, _args, _resolution) when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_media_likes},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def likes(_, _, _), do: {:ok, 0}

  def total_active_comment_count(%{media_id: media_id} = entity, _args, _resolution)
      when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_active_comment_count_of_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_active_comment_count(_, _, _), do: {:ok, 0}

  def total_active_question_count(%{media_id: media_id} = entity, _args, _resolution)
      when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_active_question_count_of_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_active_question_count(_, _, _), do: {:ok, 0}

  def total_comment_count(%{media_id: media_id} = entity, _args, _resolution) when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_comment_count_of_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_comment_count(_, _, _), do: {:ok, 0}

  def total_question_count(%{media_id: media_id} = entity, _args, _resolution) when entity.__struct__ in @media_structs do
    Absinthe.Resolution.Helpers.batch(
      {Interactions, :batch_get_total_question_count_of_media},
      media_id,
      fn batch_results ->
        batch_results
        |> Enum.find(&(&1.media_id == media_id))
        |> case do
          nil -> {:ok, 0}
          %{count: count} -> {:ok, count}
        end
      end
    )
  end

  def total_question_count(_, _, _), do: {:ok, 0}

  def total_survey_responses(%{media_id: media_id} = entity, _args, _resolution) when entity.__struct__ in @media_structs,
    do: {:ok, Interactions.count_total_distinct_survey_responses_for_media(media_id)}

  def total_survey_responses(_, _, _), do: {:ok, 0}

  def total_signups_count(%MediaUpdate{id: id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }),
      do:
        {:ok,
         InvestorMediaInteractions.count_total_signups_after_media_view(id, current_company_profile_id, "activity_update")}

  def total_signups_count(%MediaAnnouncement{id: id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }),
      do:
        {:ok,
         InvestorMediaInteractions.count_total_signups_after_media_view(id, current_company_profile_id, "announcement")}

  def total_signups_count(_, _, _), do: {:ok, 0}
end
