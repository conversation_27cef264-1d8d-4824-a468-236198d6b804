defmodule AthenaWeb.Resolvers.Interactions.MediaCommentFields do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaComment

  def user_starred(%MediaComment{id: media_comment_id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{user: %{id: company_user_id}}}
      }) do
    {:ok, Interactions.get_media_comment_star_for_profile_user(media_comment_id, company_user_id)}
  end

  def user_read(%MediaComment{id: media_comment_id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{user: %{id: company_user_id}}}
      }) do
    {:ok, Interactions.get_media_comment_read_for_profile_user(media_comment_id, company_user_id)}
  end
end
