defmodule AthenaWeb.Resolvers.Interactions.MediaUpdateFields do
  @moduledoc false

  alias G<PERSON>.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Tracking

  def total_unique_visitors_count(%MediaUpdate{id: id, slug: slug}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     Tracking.count_unique_activity_update_views_with_post_id_and_slug(
       id,
       URI.encode(slug),
       current_company_profile_id
     )}
  end

  def total_unique_visitors_count(_, _, _), do: {:ok, 0}

  def total_view_count(%MediaUpdate{id: id, slug: slug}, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     Tracking.count_activity_update_views_with_post_id_and_slug(
       id,
       URI.encode(slug),
       current_company_profile_id
     )}
  end

  def total_view_count(_, _, _), do: {:ok, 0}

  def thumbnail_attachment(%MediaUpdate{id: id}, _args, _resolution) do
    {:ok, Interactions.get_thumbnail_attachment_by_media_update_id(id)}
  end

  def thumbnail_attachment(_, _, _), do: {:ok, nil}

  def slug(%MediaUpdate{} = media_update, _, _) do
    {:ok, MediaUpdate.generate_slug(media_update)}
  end
end
