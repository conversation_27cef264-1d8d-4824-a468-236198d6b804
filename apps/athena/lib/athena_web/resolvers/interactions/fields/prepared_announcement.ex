defmodule AthenaWeb.Resolvers.Interactions.PreparedAnnouncementFields do
  @moduledoc """
  PreparedAnnouncementFields
  """

  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.PreparedAnnouncement

  def hash_id(%PreparedAnnouncement{id: id}, _args, _context) do
    {:ok, Helper.Hashid.encode_id(id)}
  end

  def announcement_id(%PreparedAnnouncement{media_id: media_id}, _args, _context) when not is_nil(media_id) do
    media_id
    |> Gaia.Interactions.get_media!()
    |> Gaia.Repo.preload(:media_announcement)
    |> case do
      %Media{media_announcement: %MediaAnnouncement{id: announcement_id}} ->
        {:ok, announcement_id}

      _ ->
        {:ok, nil}
    end
  end

  def announcement_id(_, _, _), do: {:ok, nil}
end
