defmodule AthenaWeb.Resolvers.Interactions.MediaAnnouncementFields do
  @moduledoc false

  alias G<PERSON>.Analysis
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Tracking

  def thumbnail_url(%MediaAnnouncement{thumbnail: thumbnail} = media_announcement, _args, _resolution)
      when not is_nil(thumbnail) do
    {:ok, Gaia.Uploaders.Announcements.AnnouncementThumbnail.url({thumbnail, media_announcement})}
  end

  # On homepage, any type of announcements could be displayed with its thumbnails
  # Generate thumbnail if there isn't any
  def thumbnail_url(%MediaAnnouncement{id: id, url: url}, _args, _resolution) do
    unless MediaAnnouncement.url_is_invalid?(url) do
      Gaia.Jobs.CreateAnnouncementThumbnail.enqueue(%{announcement_id: id, url: url})
    end

    {:ok, nil}
  end

  def total_view_count(%MediaAnnouncement{id: id}, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     Tracking.count_interactive_announcement_views(
       id,
       current_company_profile_id
     )}
  end

  def total_view_count(_, _, _), do: {:ok, 0}

  def total_view_count_from_time_period(%MediaAnnouncement{id: id}, %{start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     Tracking.count_interactive_announcement_views_from_time_period(
       id,
       current_company_profile_id,
       start_date,
       end_date
     )}
  end

  def total_view_count_from_time_period(_, _, _), do: {:ok, 0}

  def total_unique_visitors_count(%MediaAnnouncement{id: id}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok,
     Tracking.count_unique_interactive_announcement_views(
       id,
       current_company_profile_id
     )}
  end

  def total_unique_visitors_count(_, _, _), do: {:ok, 0}

  def total_signups_last_week_count(%MediaAnnouncement{posted_at: posted_at}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    end_date = Timex.shift(posted_at, weeks: +1)

    {:ok,
     Analysis.get_total_signups(%{
       start_date: posted_at,
       end_date: end_date,
       company_profile_id: current_company_profile_id
     })}
  end

  def total_signups_last_week_count(_, _, _), do: {:ok, 0}
end
