defmodule AthenaWeb.Resolvers.Interactions.MediaUpdateAttachmentFields do
  @moduledoc false

  alias Gaia.Interactions.MediaUpdateAttachment

  def thumbnail(%MediaUpdateAttachment{thumbnail: thumbnail} = attachment, _args, _resolution)
      when not is_nil(thumbnail) do
    {:ok, Gaia.Uploaders.Media.MediaAttachmentThumbnail.url({thumbnail, attachment})}
  end

  # Generate thumbnail if there isn't one yet
  # Only pdf types should get a thumbnail generated (until we update the job to handle other types)
  def thumbnail(%MediaUpdateAttachment{id: id, url: url, type: :pdf}, _args, _resolution) do
    Gaia.Jobs.CreatePDFAttachmentThumbnail.enqueue(%{attachment_id: id, url: url})

    {:ok, nil}
  end

  def thumbnail(_, _, _), do: {:ok, nil}
end
