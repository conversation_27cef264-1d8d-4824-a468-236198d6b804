defmodule AthenaWeb.Resolvers.Interactions.UpdateTag do
  @moduledoc """
  UpdateTag Mutation Resolvers
  """

  alias Gaia.Interactions
  alias Gaia.Interactions.MediaTag

  def resolve(_, %{id: tag_id, tag: tag_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %MediaTag{} = tag <-
           Interactions.get_media_tag_by(%{company_profile_id: company_profile_id, id: tag_id}),
         {:ok, %MediaTag{} = updated_tag} <- Interactions.update_media_tag(tag, tag_input) do
      {:ok, updated_tag}
    else
      nil ->
        {:error, "Tag not found"}

      {:error, _error} ->
        {:error, "Cannot update tag"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot update tag"}
  end
end
