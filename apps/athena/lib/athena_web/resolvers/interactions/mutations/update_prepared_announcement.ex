defmodule AthenaWeb.Resolvers.Interactions.UpdatePreparedAnnouncement do
  @moduledoc """
  UpdatePreparedAnnouncement Mutation
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.PreparedAnnouncement

  def resolve(_, %{id: id, prepared_announcement: prepared_announcement_input}, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id, user_id: current_user_id}
        }
      }) do
    with %PreparedAnnouncement{company_profile_id: company_profile_id} = existing_prepared_announcement <-
           Interactions.get_prepared_announcement_by(%{id: id}),
         true <- company_profile_id == current_company_profile_id,
         new_prepared_announcement_input =
           maybe_add_comment_authored_by_into_prepared_announcement_input(
             prepared_announcement_input,
             current_user_id
           ),
         {:ok, %PreparedAnnouncement{} = updated_prepared_announcement} <-
           Interactions.update_prepared_announcement(existing_prepared_announcement, new_prepared_announcement_input) do
      {:ok, updated_prepared_announcement}
    else
      nil ->
        {:error, "Prepared announcement does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end

  defp maybe_add_comment_authored_by_into_prepared_announcement_input(%{comment_content: comment_content} = args, user_id)
       when not is_nil(comment_content) do
    Map.put(args, :comment_authored_by, user_id)
  end

  defp maybe_add_comment_authored_by_into_prepared_announcement_input(args, _user_id) do
    args
  end
end
