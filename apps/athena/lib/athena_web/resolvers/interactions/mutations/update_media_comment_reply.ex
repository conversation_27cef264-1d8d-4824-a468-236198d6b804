defmodule AthenaWeb.Resolvers.Interactions.UpdateMediaCommentReply do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment

  def resolve(_, %{media_comment_id: media_comment_id, content: content} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_profile_id, user_id: current_user_id}}
      }) do
    with {:get_existing_media_comment,
          %MediaComment{
            media: %Media{
              company_profile_id: company_profile_id
            },
            company_author_id: company_author_id
          } = existing_media_comment} <-
           {:get_existing_media_comment, Interactions.get_media_comment_with_media(media_comment_id)},
         {:check_the_comment_was_created_by_the_company, false} <-
           {:check_the_comment_was_created_by_the_company, is_nil(company_author_id)},
         {:check_authorisation, true} <-
           {:check_authorisation, company_profile_id == current_profile_id},
         {:ok, updated_media_comment} <-
           Interactions.update_media_comment(existing_media_comment, %{
             content: content,
             last_edited_by: current_user_id,
             use_company_as_username:
               Map.get(args, :use_company_as_username, existing_media_comment.use_company_as_username)
           }) do
      {:ok, updated_media_comment}
    else
      {:get_existing_media_comment, nil} ->
        {:error, "Media comment does not exist."}

      {:check_the_comment_was_created_by_the_company, true} ->
        {:error, "Media comment cannot be updated since it was not created by the company."}

      {:check_authorisation, false} ->
        {:error, "You are unauthorised."}

      _error ->
        {:error,
         "Unfortunately, your comment cannot be updated at this time, please check your input and try again later."}
    end
  end
end
