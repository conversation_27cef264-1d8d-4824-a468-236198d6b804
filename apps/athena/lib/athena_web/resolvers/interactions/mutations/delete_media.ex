defmodule AthenaWeb.Resolvers.Interactions.DeleteMedia do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Media{} = media <- Interactions.get_media(id),
         true <- media.company_profile_id == current_company_profile_id,
         {:ok, deleted_media} <- Interactions.delete_media(media) do
      {:ok, deleted_media}
    else
      false ->
        {:error, "You don't have permission to delete this media"}

      nil ->
        {:error, "Media not found"}

      _error ->
        {:error, "Oops! Something went wrong. Please try again!"}
    end
  end
end
