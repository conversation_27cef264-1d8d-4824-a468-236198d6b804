defmodule AthenaWeb.Resolvers.Interactions.GenerateMediaUpdateAttachmentSignedUrl do
  @moduledoc false

  alias Gaia.Companies.ProfileUser

  def resolve(_parent, %{mime_type: mime_type}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{current_company_profile_id}/interactions_media_update_attachments/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ) do
      {:ok, url}
    else
      _error ->
        {:error, "Unable to generate signed url."}
    end
  end
end
