defmodule AthenaWeb.Resolvers.Interactions.PublishMediaSelectedDistributionChannels do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{media_id: media_id, selected_distribution_channels: selected_distribution_channels}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with %Media{} = media <- Interactions.get_media(media_id),
         true <- media_permitted?(media, current_company_profile_id),
         :ok <-
           maybe_publish_announcement(
             media,
             is_distribution_channel_selected?("announcement", selected_distribution_channels)
           ),
         :ok <-
           maybe_publish_update(
             media,
             is_distribution_channel_selected?("update", selected_distribution_channels),
             current_company_user_id
           ),
         :ok <-
           maybe_publish_linkedin(media, is_distribution_channel_selected?("linkedin", selected_distribution_channels)),
         :ok <-
           maybe_publish_twitter(media, is_distribution_channel_selected?("twitter", selected_distribution_channels)),
         :ok <-
           maybe_publish_email(media, is_distribution_channel_selected?("email", selected_distribution_channels)),
         {:ok, updated_media} <-
           Interactions.update_media(media, %{state: :published}) do
      {:ok, updated_media}
    else
      nil ->
        {:error, "Media not found."}

      false ->
        {:error, "You are not authorised to publish this media."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp media_permitted?(%Media{company_profile_id: company_profile_id}, current_company_profile_id) do
    company_profile_id == current_company_profile_id
  end

  defp maybe_publish_announcement(
         %Media{id: media_id, distribution_announcement_enabled: true},
         true = _is_distribution_channel_selected
       ) do
    Gaia.Jobs.PublishMediaPreparedAnnouncement.enqueue(%{"media_id" => media_id})
  end

  defp maybe_publish_announcement(_, _), do: :ok

  defp maybe_publish_update(
         %Media{id: media_id, distribution_update_enabled: true},
         true = _is_distribution_channel_selected,
         current_company_user_id
       ) do
    Gaia.Jobs.PublishMediaUpdate.enqueue(%{"media_id" => media_id, "posted_by_id" => current_company_user_id})
  end

  defp maybe_publish_update(_, _, _), do: :ok

  defp maybe_publish_linkedin(
         %Media{id: media_id, distribution_linkedin_enabled: true},
         true = _is_distribution_channel_selected
       ) do
    Gaia.Jobs.PublishMediaLinkedin.enqueue(%{"media_id" => media_id})
  end

  defp maybe_publish_linkedin(_, _), do: :ok

  defp maybe_publish_twitter(
         %Media{id: media_id, distribution_twitter_enabled: true},
         true = _is_distribution_channel_selected
       ) do
    Gaia.Jobs.PublishMediaTwitter.enqueue(%{"media_id" => media_id})
  end

  defp maybe_publish_twitter(_, _), do: :ok

  defp maybe_publish_email(
         %Media{id: media_id, distribution_email_enabled: true},
         true = _is_distribution_channel_selected
       ) do
    Gaia.Jobs.PublishMediaEmail.enqueue(%{"media_id" => media_id})
  end

  defp maybe_publish_email(_, _), do: :ok

  defp is_distribution_channel_selected?(channel, selected_distribution_channels) do
    Enum.member?(selected_distribution_channels, channel)
  end
end
