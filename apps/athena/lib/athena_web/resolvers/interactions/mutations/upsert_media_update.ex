defmodule AthenaWeb.Resolvers.Interactions.UpsertMediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaUpdate

  def resolve(_parent, %{id: id, media_update: media_update}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with %MediaUpdate{company_profile_id: company_profile_id} = existing_media_update <-
           Interactions.get_media_update(id),
         {:check_ownership, true} <-
           {:check_ownership, current_company_profile_id == company_profile_id},
         true <- Map.get(media_update, :title, "") != "",
         title = Map.get(media_update, :title),
         {:ok, %{update_media_update: updated_media_update}} <-
           Interactions.upsert_media_update_with_content_and_attachments_and_comment_content_and_title(
             %{
               attachments: Map.get(media_update, :attachments, []),
               content: Map.get(media_update, :content, ""),
               comment_content: Map.get(media_update, :comment_content, ""),
               comment_use_company_as_username: Map.get(media_update, :comment_use_company_as_username, true),
               company_profile_id: company_profile_id,
               company_user_id: current_company_user_id,
               title: title,
               slug: Slug.slugify(title)
             },
             existing_media_update
           ) do
      {:ok, updated_media_update}
    else
      {:error, :update_media_update, %Ecto.Changeset{errors: [{:slug, _}]}, _changes_so_far} ->
        {:error, "Update titles need to be unique. An update with this title already exists, please try another."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
