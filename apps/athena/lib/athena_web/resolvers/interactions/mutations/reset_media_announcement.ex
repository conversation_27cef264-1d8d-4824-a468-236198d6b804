defmodule AthenaWeb.Resolvers.Interactions.ResetMediaAnnouncement do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaAnnouncement{media: %Media{company_profile_id: company_profile_id}} = media_announcement <-
           Interactions.get_media_announcement(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %{updated_media_announcement: updated_media_announcement}} <-
           Interactions.reset_media_announcement(media_announcement) do
      {:ok, updated_media_announcement}
    else
      nil ->
        {:error, "Media announcement does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
