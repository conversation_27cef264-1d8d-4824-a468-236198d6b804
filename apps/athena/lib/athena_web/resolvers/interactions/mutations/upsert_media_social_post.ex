defmodule AthenaWeb.Resolvers.Interactions.UpsertMediaSocialPost do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.SocialPost
  alias Gaia.Repo

  def resolve(_parent, %{media_id: media_id, social_post_input: social_post_input}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with {:get_media, %Media{company_profile_id: company_profile_id} = media} <-
           {:get_media, Interactions.get_media(media_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:upsert_social_post, {:ok, %SocialPost{} = upserted_social_post}} <-
           {:upsert_social_post,
            media
            |> Repo.preload([:linkedin_social_post, :twitter_social_post])
            |> upsert_media_social_post(social_post_input)} do
      {:ok, upserted_social_post}
    else
      {:get_media, nil} ->
        {:error, "Media does not exist."}

      {:check_ownership, false} ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp upsert_media_social_post(
         %Media{id: media_id, linkedin_social_post: linkedin_social_post, twitter_social_post: twitter_social_post},
         %{platform: platform} = social_post_input
       )
       when platform in [:linkedin, :twitter] do
    social_post_input =
      Map.put(social_post_input, :media_id, media_id)

    social_post =
      case platform do
        :linkedin -> linkedin_social_post
        :twitter -> twitter_social_post
      end

    Interactions.upsert_social_post(social_post, social_post_input)
  end

  defp upsert_media_social_post(_, _), do: {:error, "Cannot upsert social post"}
end
