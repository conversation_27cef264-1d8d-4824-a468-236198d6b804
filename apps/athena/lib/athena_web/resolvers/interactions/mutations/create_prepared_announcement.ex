defmodule AthenaWeb.Resolvers.Interactions.CreatePreparedAnnouncement do
  @moduledoc """
  CreatePreparedAnnouncement Mutation
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.PreparedAnnouncement

  @gettext_context "Create prepared announcement mutation"

  def resolve(_, %{title: title}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    %{title: title, company_profile_id: company_profile_id}
    |> Interactions.create_prepared_announcement()
    |> case do
      {:ok, %PreparedAnnouncement{} = prepared_announcement} ->
        {:ok, prepared_announcement}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Cannot create a prepared announcement"
             )
         }}
    end
  end
end
