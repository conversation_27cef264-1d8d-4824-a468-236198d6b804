defmodule AthenaWeb.Resolvers.Interactions.CreateMediaTag do
  @moduledoc """
  CreateMediaTag Mutation Resolvers
  """

  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaTag

  def resolve(_, %{media_id: media_id, tag: tag_input}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    with %Media{} <-
           Interactions.get_media_by(%{id: media_id, company_profile_id: company_profile_id}),
         {:ok, %MediaTag{} = created_tag} <-
           %{media_id: media_id, company_profile_id: company_profile_id}
           |> Enum.into(tag_input)
           |> Interactions.create_media_tag() do
      {:ok, created_tag}
    else
      nil ->
        {:error, "Media not found"}

      {:error,
       %Ecto.Changeset{
         changes: %{name: tag_name},
         errors: [
           media_id: {"has already been taken", _}
         ]
       }} ->
        {:error, "Tag \"#{tag_name}\" has already been added to this media"}

      {:error, _error} ->
        {:error, "Cannot create tag"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Cannot create tag"}
  end
end
