defmodule AthenaWeb.Resolvers.Interactions.UpdateMedia do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{id: id, media: media_attrs}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Media{company_profile_id: company_profile_id} = media <-
           Interactions.get_media(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, updated_media} <-
           Interactions.update_media(media, media_attrs) do
      {:ok, updated_media}
    else
      nil ->
        {:error, "Media does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
