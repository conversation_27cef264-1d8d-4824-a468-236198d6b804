defmodule AthenaWeb.Resolvers.Interactions.InvalidateMediaComment do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment

  def resolve(_parent, %{media_comment_id: media_comment_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaComment{media: %Media{company_profile_id: company_profile_id}} = media_comment <-
           Interactions.get_media_comment_with_media(media_comment_id),
         {:is_authorised, true} <-
           {:is_authorised, company_profile_id == current_company_profile_id},
         {:ok, %{media_comment: media_comment}} <-
           Interactions.invalidate_media_comment_and_maybe_children(media_comment) do
      {:ok, media_comment}
    else
      nil ->
        {:error, "Media comment does not exist"}

      {:is_authorised, false} ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
