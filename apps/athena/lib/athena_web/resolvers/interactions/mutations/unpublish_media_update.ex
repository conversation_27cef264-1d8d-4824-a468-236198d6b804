defmodule AthenaWeb.Resolvers.Interactions.UnpublishMediaUpdate do
  @moduledoc """
  Resolvers for UnpublishMediaUpdate

  Unpublishes a previously published media update by setting is_draft to true
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate

  @gettext_context "Unpublish media update mutation"

  def resolve(_parent, %{id: id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id, user_id: current_user_id}
        }
      }) do
    with %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = mu <-
           Interactions.get_media_update(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, updated_mu} <-
           Interactions.update_media_update(mu, %{
             is_draft: true,
             last_updated_by_id: current_user_id
           }) do
      {:ok, updated_mu}
    else
      nil ->
        {:error, dpgettext("errors", @gettext_context, "Update does not exist.")}

      false ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: dpgettext("errors", @gettext_context, "Unable to unpublish update.")
         }}
    end
  end
end
