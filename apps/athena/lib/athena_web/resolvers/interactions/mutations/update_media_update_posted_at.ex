defmodule AthenaWeb.Resolvers.Interactions.UpdateMediaUpdatePostedAt do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate

  @gettext_context "Update media update posted_at mutation"

  def resolve(_parent, %{id: id, posted_at: posted_at}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with {:get_media_update, %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = existing_media_update} <-
           {:get_media_update, Interactions.get_media_update(id)},
         {:check_ownership, true} <- {:check_ownership, company_profile_id == current_company_profile_id},
         {:update_media_update, {:ok, %MediaUpdate{} = updated_media_update}} <-
           {:update_media_update,
            Interactions.update_media_update(existing_media_update, %{
              posted_at: posted_at,
              last_updated_by_id: current_company_user_id
            })} do
      {:ok, updated_media_update}
    else
      {:get_media_update, nil} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Media update does not exist."
         )}

      {:check_ownership, false} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are unauthorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Oops! Something went wrong."
             )
         }}
    end
  end
end
