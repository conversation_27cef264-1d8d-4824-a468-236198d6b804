defmodule AthenaWeb.Resolvers.Interactions.CreatePreparedAnnouncementForMedia do
  @moduledoc """
  CreatePreparedAnnouncement Mutation
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.PreparedAnnouncement

  def resolve(_, %{media_id: media_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    # First check if the media exists
    case Interactions.get_media(media_id) do
      %Media{title: title} ->
        %{title: title, company_profile_id: company_profile_id, media_id: media_id}
        |> Interactions.create_prepared_announcement()
        |> case do
          {:ok, %PreparedAnnouncement{} = prepared_announcement} ->
            {:ok, prepared_announcement}

          _error ->
            {:error, "Cannot create a prepared announcement"}
        end

      nil ->
        {:error, "Media not found"}
    end
  end
end
