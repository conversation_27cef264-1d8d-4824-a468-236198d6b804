defmodule AthenaWeb.Resolvers.Interactions.GeneratePreparedAnnouncementVideoSignedUrl do
  @moduledoc """
  GeneratePreparedAnnouncementVideoSignedUrl Mutation
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.PreparedAnnouncement

  @gettext_context "Generate prepared announcement video signed url mutation"

  def resolve(
        _parent,
        %{file_size: file_size, prepared_announcement_id: prepared_announcement_id, mime_type: mime_type},
        %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}
      )
      when file_size <= 250_000_000 do
    with %PreparedAnnouncement{company_profile_id: company_profile_id} = prepared_announcement <-
           Interactions.get_prepared_announcement(prepared_announcement_id),
         true <- company_profile_id == current_company_profile_id,
         %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/company_profile/#{current_company_profile_id}/interactions_prepared_announcements/#{prepared_announcement_id}/videos/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ),
         {:ok, %URI{host: host, path: path, scheme: scheme}} <- URI.new(url),
         {:ok, _} <-
           Interactions.update_prepared_announcement(prepared_announcement, %{
             video_url: "#{scheme}://#{host}#{path}"
           }) do
      {:ok, url}
    else
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Announcement does not exist."
         )}

      false ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are unauthorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to generate signed url."
             )
         }}
    end
  end
end
