defmodule AthenaWeb.Resolvers.Interactions.LinkPreparedAnnouncementToAnnouncement do
  @moduledoc """
  Resolver for linking prepared announcement to announcement
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Ecto.Changeset
  alias Ecto.Multi
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaComment
  alias Gaia.Interactions.PreparedAnnouncement
  alias Gaia.Repo

  @gettext_context "Link prepared announcement to announcement"

  def resolve(_, %{announcement_media_id: announcement_media_id, prepared_announcement_id: prepared_announcement_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    with {:media_announcement,
          %Media{
            media_announcement: announcement
          }} <-
           {:media_announcement,
            %{
              id: announcement_media_id,
              company_profile_id: company_profile_id
            }
            |> Interactions.get_media_by()
            |> Repo.preload(:media_announcement)},
         {:has_media_announcement, false} <- {:has_media_announcement, is_nil(announcement)},
         {:prepared_announcement, %PreparedAnnouncement{media_id: nil} = prepared_announcement} <-
           {:prepared_announcement,
            Interactions.get_prepared_announcement_by(
              id: prepared_announcement_id,
              is_draft: false,
              company_profile_id: company_profile_id
            )},
         {:link_announcement, {:ok, %{announcement: updated_announcement}}} <-
           {:link_announcement, link_announcement(prepared_announcement, announcement)} do
      {:ok, updated_announcement}
    else
      {:media_announcement, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get announcement to link prepared announcement to"
             )
         }}

      {:has_media_announcement, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get announcement for media to link prepared announcement to"
             )
         }}

      {:prepared_announcement, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get prepared announcement to link announcement to"
             )
         }}

      {:link_announcement, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "There was an error linking the prepared announcement to the announcement, please contact support"
             )
         }}
    end
  end

  # Link announcement and create comment
  defp link_announcement(
         %PreparedAnnouncement{
           comment_content: comment,
           comment_authored_by: comment_authored_by,
           summary: summary,
           video_url: video_url,
           social_video_url: social_video_url,
           comment_use_company_as_username: comment_use_company_as_username
         } = prepared_announcement,
         %MediaAnnouncement{media_id: media_id} = announcement
       )
       when not is_nil(comment_authored_by) and not is_nil(comment) do
    Multi.new()
    |> Multi.update(
      :prepared_announcement,
      Changeset.change(prepared_announcement, media_id: media_id)
    )
    |> Multi.update(
      :announcement,
      Changeset.change(announcement,
        summary: summary,
        video_url: video_url,
        social_video_url: social_video_url
      )
    )
    |> Multi.insert(:media_comment, %MediaComment{
      content: comment,
      company_author_id: comment_authored_by,
      media_id: media_id,
      private: false,
      use_company_as_username: comment_use_company_as_username
    })
    |> Repo.transaction()
  end

  # Link announcement with no comment
  defp link_announcement(
         %PreparedAnnouncement{summary: summary, video_url: video_url, social_video_url: social_video_url} =
           prepared_announcement,
         %MediaAnnouncement{media_id: media_id} = announcement
       ) do
    Multi.new()
    |> Multi.update(
      :prepared_announcement,
      Changeset.change(prepared_announcement, media_id: media_id)
    )
    |> Multi.update(
      :announcement,
      Changeset.change(announcement,
        summary: summary,
        video_url: video_url,
        social_video_url: social_video_url
      )
    )
    |> Repo.transaction()
  end
end
