defmodule AthenaWeb.Resolvers.Interactions.CreateMediaCommentFromOtherSource do
  @moduledoc false

  alias G<PERSON>.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Hubs
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(
        _parent,
        %{media_id: media_id, comment_source: comment_source, reply_content: _, content: _content} = args,
        %{
          context: %{
            current_company_profile_user:
              %ProfileUser{profile_id: current_company_profile_id, user: %User{id: current_user_id}} =
                _company_profile_user
          }
        }
      )
      when not is_nil(comment_source) do
    with %Media{company_profile_id: company_profile_id} <- Interactions.get_media(media_id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %{question: media_comment_with_source, reply: reply}} <-
           create_media_comment_from_other_source_maybe_with_reply(args, current_user_id),
         :ok <-
           Hubs.create_followed_question_notifications_for_all_investors(%{
             media_comment: media_comment_with_source,
             reply_id: reply.id
           }) do
      {:ok, media_comment_with_source}
    else
      nil ->
        {:error, "Media does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end

  defp create_media_comment_from_other_source_maybe_with_reply(%{reply_content: reply_content} = args, current_user_id)
       when not is_nil(reply_content) do
    reply_args = generate_reply_args(args, current_user_id)

    args
    |> Map.put(:private, false)
    |> Interactions.create_media_comment_with_reply(reply_args)
    |> case do
      {:ok, %{question: question, reply: reply}} ->
        {:ok, %{question: question, reply: reply}}

      _ ->
        {:error, "Error creating media comment with reply"}
    end
  end

  defp create_media_comment_from_other_source_maybe_with_reply(args, current_user_id) do
    args
    |> Map.put(:private, false)
    |> Map.put(:done, true)
    |> Map.put(:last_edited_by, current_user_id)
    |> Interactions.create_media_comment()
    |> case do
      {:ok, question} ->
        {:ok, %{question: question, reply: nil}}

      _ ->
        {:error, "Error creating media comment with reply"}
    end
  end

  defp generate_reply_args(%{media_id: media_id, reply_content: reply_content} = args, current_user_id),
    do: %{
      media_id: media_id,
      content: reply_content,
      company_author_id: current_user_id,
      private: false,
      use_company_as_username: Map.get(args, :reply_use_company_as_username, true)
    }
end
