defmodule AthenaWeb.Resolvers.Interactions.CreateNewMediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    current_date = Helper.ExDay.now_date() |> Timex.format("{0D}/{0M}/{YYYY}") |> elem(1)
    random_int = 0..1_000 |> Enum.random() |> Integer.to_string()

    title = "Update " <> current_date <> " " <> random_int

    %{
      title: title,
      company_profile_id: current_company_profile_id,
      last_updated_by_id: current_company_user_id,
      slug: Slug.slugify(title),
      attachments: [],
      content: ""
    }
    |> Interactions.create_new_media_update()
    |> case do
      {:ok, %{media_update: media_update}} ->
        {:ok, media_update}

      _error ->
        {:error, "Oops! Something went wrong. Please try again!"}
    end
  end
end
