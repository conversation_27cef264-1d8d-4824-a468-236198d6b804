defmodule AthenaWeb.Resolvers.Interactions.UpdateMediaCommentDone do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment

  def resolve(_, %{media_comment_id: media_comment_id, done: done}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_profile_id}}
      }) do
    with %MediaComment{media: %Media{company_profile_id: company_profile_id}} = mc <-
           Interactions.get_media_comment_with_media(media_comment_id),
         true <- company_profile_id == current_profile_id,
         {:ok, media_comment} <-
           Interactions.update_media_comment(mc, %{done: done}) do
      {:ok, media_comment}
    else
      nil ->
        {:error, "Media comment does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
