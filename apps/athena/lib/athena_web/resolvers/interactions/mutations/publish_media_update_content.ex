defmodule AthenaWeb.Resolvers.Interactions.PublishMediaUpdateContent do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Repo

  def resolve(_parent, %{id: id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with %MediaUpdate{} = media_update <- Interactions.get_media_update(id),
         true <- media_update_permitted?(media_update, current_company_profile_id),
         {:ok, updated_media_update} <- publish_content(media_update, current_company_user_id) do
      {:ok, updated_media_update}
    else
      nil ->
        {:error, "Media update not found."}

      false ->
        {:error, "You are not authorised to publish this media update content."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp media_update_permitted?(%MediaUpdate{media: %{company_profile_id: company_profile_id}}, current_company_profile_id) do
    company_profile_id == current_company_profile_id
  end

  defp media_update_permitted?(media_update, current_company_profile_id) do
    media_update = Repo.preload(media_update, :media)
    media_update_permitted?(media_update, current_company_profile_id)
  end

  defp publish_content(media_update, current_company_user_id) do
    attrs = %{
      content_published: media_update.content_draft,
      last_updated_by_id: current_company_user_id
    }

    Interactions.update_media_update(media_update, attrs)
  end
end
