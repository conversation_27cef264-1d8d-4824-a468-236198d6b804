defmodule AthenaWeb.Resolvers.Interactions.PublishMediaSocialPost do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.SocialPost
  alias Gaia.Repo

  def resolve(_parent, %{media_id: media_id, platform: platform}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with {:get_media, %Media{company_profile_id: company_profile_id} = media} <-
           {:get_media, Interactions.get_media(media_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:publish_social_post, {:ok, %SocialPost{} = published_social_post}} <-
           {:publish_social_post,
            media
            |> Repo.preload([
              :linkedin_social_post,
              :twitter_social_post,
              company_profile: [:social_connection]
            ])
            |> publish_media_social_post(platform)} do
      {:ok, published_social_post}
    else
      {:get_media, nil} ->
        {:error, "Media does not exist."}

      {:check_ownership, false} ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp publish_media_social_post(
         %Media{linkedin_social_post: linkedin_social_post, twitter_social_post: twitter_social_post} = media,
         platform
       ) do
    social_post =
      case platform do
        :linkedin -> linkedin_social_post
        :twitter -> twitter_social_post
      end

    Interactions.publish_social_post(media, social_post)
  end

  defp publish_media_social_post(_, _), do: {:error, "Cannot publish social post"}
end
