defmodule AthenaWeb.Resolvers.Interactions.CreateMediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{media_update: media_update}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with {:ok, media} <- media_permitted(media_update.media_id, current_company_profile_id),
         {:ok, media_update} <-
           Interactions.create_newsflow_media_update(%{
             company_profile_id: current_company_profile_id,
             media_id: media.id,
             last_updated_by_id: current_company_user_id,
             content_draft: maybe_generate_body_with_ai(media_update[:ai_generate_body], media),
             title: media.title,
             slug: Slug.slugify(media.title)
           }) do
      {:ok, media_update}
    else
      {:error, :unauthorised} ->
        {:error, "You are not authorised to create a media update for this media."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp media_permitted(media_id, current_company_profile_id) do
    case Interactions.get_media(media_id) do
      %Media{company_profile_id: ^current_company_profile_id} = media -> {:ok, media}
      _ -> {:error, :unauthorised}
    end
  end

  defp maybe_generate_body_with_ai(true, media) do
    case Gaia.Interactions.AiContentConverter.generate_content(media.draft_content,
           title: media.title,
           type: :update
         ) do
      {:ok, content} -> content
      {:error, _} -> media.draft_content
    end
  end

  defp maybe_generate_body_with_ai(false, _media), do: nil
end
