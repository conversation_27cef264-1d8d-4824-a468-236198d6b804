defmodule AthenaWeb.Resolvers.Interactions.ToggleMediaCommentPrivacy do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment
  alias Gaia.Investors
  alias Gaia.Investors.User
  alias Gaia.Repo

  def resolve(_parent, %{id: id, private: private}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaComment{media: %Media{company_profile_id: company_profile_id}} = media_comment <-
           Interactions.get_media_comment_with_media(id),
         {:is_authorised, true} <-
           {:is_authorised, company_profile_id == current_company_profile_id},
         true <- id |> Interactions.toggle_media_comment_privacy(private) |> elem(0) > 0 do
      Task.start(fn ->
        media_comment
        |> Repo.preload(investor_user: [:company_profile])
        |> Repo.preload(media: [:media_announcement, :media_update])
        |> send_email(private)
      end)

      {:ok, true}
    else
      nil ->
        {:error, "Media comment does not exist"}

      false ->
        {:ok, false}

      {:is_authorised, false} ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end

  defp send_email(%MediaComment{investor_user: %User{} = investor_user, media: %Media{}} = media_comment, is_made_private) do
    investor_user = Investors.investor_user_for_email(investor_user)

    if Gaia.Comms.is_investor_subscribed(%{investor_user: investor_user, scope: :qa}) do
      media_comment = Map.put(media_comment, :investor_user, investor_user)
      send_email_exec(media_comment, is_made_private)
    else
      :skip
    end
  end

  defp send_email(_, _), do: :skip

  defp send_email_exec(
         %MediaComment{
           investor_user: %User{} = investor_user,
           media: %Media{company_profile_id: company_profile_id, media_announcement: media_announcement}
         },
         is_made_private
       )
       when not is_nil(media_announcement) do
    if is_made_private do
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :announcement_comment_made_private,
        [investor_user, media_announcement],
        company_profile_id
      )
    else
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :announcement_comment_made_public,
        [investor_user, media_announcement],
        company_profile_id
      )
    end
  end

  defp send_email_exec(
         %MediaComment{
           investor_user: %User{} = investor_user,
           media: %Media{company_profile_id: company_profile_id, media_update: media_update}
         },
         is_made_private
       )
       when not is_nil(media_update) do
    if is_made_private do
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :update_comment_made_private,
        [investor_user, media_update],
        company_profile_id
      )
    else
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :update_comment_made_public,
        [investor_user, media_update],
        company_profile_id
      )
    end
  end
end
