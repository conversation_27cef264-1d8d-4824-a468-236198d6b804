defmodule AthenaWeb.Resolvers.Interactions.ReplyToMediaComment do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Hubs
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment
  alias Gaia.Investors
  alias Gaia.Investors.User
  alias Gaia.Repo

  def resolve(_parent, %{content: content, parent_id: parent_id} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id, user_id: current_user_id}
        }
      }) do
    with %MediaComment{
           id: media_comment_id,
           media: %Media{company_profile_id: company_profile_id},
           media_id: media_id,
           private: private,
           investor_user_id: investor_user_id
         } = parent_comment <-
           Interactions.get_media_comment_with_media(parent_id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %{insert: %{id: reply_id} = media_comment}} <-
           Interactions.reply_to_media_comment(
             parent_id,
             %{
               company_author_id: current_user_id,
               content: content,
               media_id: media_id,
               parent_id: parent_id,
               private: private,
               use_company_as_username: Map.get(args, :use_company_as_username, true)
             }
           ) do
      Task.start(fn ->
        Hubs.create_reply_notifications_for_investor(media_comment_id, investor_user_id, reply_id)
        Hubs.create_followed_reply_notifications_for_all_investors(media_comment_id, reply_id)
        send_email_notification(parent_comment)
      end)

      {:ok, media_comment}
    else
      nil ->
        {:error, "Media comment does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  def send_email_notification(%MediaComment{} = parent_comment) do
    parent_comment
    |> Repo.preload(investor_user: [:company_profile])
    |> Repo.preload(media: [:media_announcement, :media_update])
    |> send_email()
  end

  defp send_email(%MediaComment{
         investor_user: %User{} = investor_user,
         media: %Media{company_profile_id: company_profile_id, media_announcement: media_announcement}
       })
       when not is_nil(media_announcement) do
    investor_user = Investors.investor_user_for_email(investor_user)

    if Gaia.Comms.is_investor_subscribed(%{investor_user: investor_user, scope: :qa}) do
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :announcement_comment_new_reply,
        [investor_user, media_announcement],
        company_profile_id
      )
    else
      :skip
    end
  end

  defp send_email(%MediaComment{
         investor_user: %User{} = investor_user,
         media: %Media{company_profile_id: company_profile_id, media_update: media_update}
       })
       when not is_nil(media_update) do
    investor_user = Investors.investor_user_for_email(investor_user)

    if Gaia.Comms.is_investor_subscribed(%{investor_user: investor_user, scope: :qa}) do
      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :update_comment_new_reply,
        [investor_user, media_update],
        company_profile_id
      )
    else
      :skip
    end
  end

  defp send_email(_), do: :skip
end
