defmodule AthenaWeb.Resolvers.Interactions.GenerateMediaAnnouncementAiSummary do
  @moduledoc false

  alias Gaia.Interactions
  alias Gaia.Interactions.MediaAnnouncement

  def resolve(_parent, %{media_announcement_id: media_announcement_id}, _context) do
    with %MediaAnnouncement{} = media_announcement <- Interactions.get_media_announcement(media_announcement_id),
         {:ok, reply} <- generate_summary(media_announcement) do
      {:ok, reply}
    else
      nil ->
        {:error, "Announcement does not exist."}

      _error ->
        {:error, "Unable to generate AI summary."}
    end
  end

  defp generate_summary(media_announcement) do
    data = %Gaia.Interactions.MediaAnnouncement{
      pdf_as_text: media_announcement.pdf_as_text,
      listing_key: media_announcement.listing_key,
      market_key: media_announcement.market_key
    }

    Gaia.GPT.generate_ai_summary_as_string(data)
  end
end
