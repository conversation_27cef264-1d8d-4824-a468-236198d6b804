defmodule AthenaWeb.Resolvers.Interactions.PublishMedia do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Repo

  def resolve(_parent, %{id: id} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    distributions = Map.get(args, :distributions)

    with %Media{} = media <- Interactions.get_media(id),
         true <- media_permitted?(media, current_company_profile_id),
         {:ok, updated_media} <- publish_media(media, current_company_user_id, distributions) do
      {:ok, updated_media}
    else
      nil ->
        {:error, "Media not found."}

      false ->
        {:error, "You are not authorised to publish this media."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp media_permitted?(%Media{company_profile_id: company_profile_id}, current_company_profile_id) do
    company_profile_id == current_company_profile_id
  end

  defp publish_media(media, current_company_user_id, distributions) do
    Repo.transaction(fn ->
      case publish_media_update(media, current_company_user_id, distributions) do
        :ok -> media
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  defp publish_media_update(media, current_company_user_id, distributions) do
    if should_publish_distribution?("update", distributions) do
      do_publish_media_update(media, current_company_user_id)
    else
      :ok
    end
  end

  defp do_publish_media_update(media, current_company_user_id) do
    case Repo.get_by(MediaUpdate, media_id: media.id) do
      %MediaUpdate{} = media_update ->
        update_attrs = %{
          content_published: media_update.content_draft,
          is_draft: false,
          posted_at: DateTime.utc_now(),
          last_updated_by_id: current_company_user_id
        }

        case Interactions.update_media_update(media_update, update_attrs) do
          {:ok, _updated_media_update} -> :ok
          {:error, changeset} -> {:error, changeset}
        end

      nil ->
        :ok
    end
  end

  defp should_publish_distribution?(_distribution_type, nil), do: true

  defp should_publish_distribution?(distribution_type, distributions) when is_list(distributions) do
    Enum.member?(distributions, distribution_type)
  end
end
