defmodule AthenaWeb.Resolvers.Interactions.UpdateMediaAnnouncement do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement

  def resolve(_parent, %{id: id, input: input}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaAnnouncement{media: %Media{company_profile_id: company_profile_id}} = media_announcement <-
           Interactions.get_media_announcement(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, updated_media_announcement} <-
           Interactions.update_media_announcement(media_announcement, input) do
      {:ok, updated_media_announcement}
    else
      nil ->
        {:error, "Media announcement does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
