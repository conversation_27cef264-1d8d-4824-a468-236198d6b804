defmodule AthenaWeb.Resolvers.Interactions.UpdatePinnedMediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.MediaUpdate

  def resolve(_, %{id: id, value: true}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaUpdate{company_profile_id: company_profile_id} = media_update <-
           Interactions.get_media_update(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, updated_pinned_media_update} <- Interactions.update_pinned_media_update(media_update) do
      {:ok, updated_pinned_media_update}
    else
      nil ->
        {:error, "Media does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  def resolve(_, %{id: id, value: false}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaUpdate{company_profile_id: company_profile_id} = media_update <-
           Interactions.get_media_update(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, media_update_with_pin_removed} <- Interactions.update_media_update(media_update, %{is_pinned: false}) do
      {:ok, media_update_with_pin_removed}
    else
      nil ->
        {:error, "Media does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
