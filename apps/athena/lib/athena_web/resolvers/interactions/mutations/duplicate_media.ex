defmodule AthenaWeb.Resolvers.Interactions.DuplicateMedia do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Media{} = media <- Interactions.get_media(id),
         true <- media.company_profile_id == current_company_profile_id,
         {:ok, duplicated_media} <- Interactions.duplicate_media(media) do
      {:ok, duplicated_media}
    else
      false ->
        {:error, "You don't have permission to duplicate this media"}

      nil ->
        {:error, "Media not found"}

      _error ->
        {:error, "Oops! Something went wrong. Please try again!"}
    end
  end
end
