defmodule AthenaWeb.Resolvers.Interactions.SortContentCalendar do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(
        _parent,
        %{
          source_year: source_year,
          source_month: source_month,
          source_media_ids: source_media_ids,
          target_year: target_year,
          target_month: target_month,
          target_media_ids: target_media_ids
        },
        %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}
      ) do
    case Interactions.sort_content_calendar(
           current_company_profile_id,
           source_year,
           source_month,
           source_media_ids,
           target_year,
           target_month,
           target_media_ids
         ) do
      {:ok, _} ->
        {:ok, true}

      nil ->
        {:error, "Media does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
