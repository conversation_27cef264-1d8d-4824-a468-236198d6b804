defmodule AthenaWeb.Resolvers.Interactions.ParsePdfWithAi do
  @moduledoc """
  Resolver for parsing PDFs with AI using <PERSON><PERSON><PERSON><PERSON>
  """

  alias Lang<PERSON>hain.Chains.LLMChain
  alias LangChain.ChatModels.ChatOpenAI
  alias LangChain.Message

  require Logger

  @doc """
  Parse a PDF file using AI and generate content based on requested distributions
  """
  def resolve(_parent, %{pdf: pdf, distributions: distributions}, _) do
    with {:ok, _filename, binary_data} <- extract_upload_data(pdf),
         {:ok, text_content} <- extract_text_from_pdf(binary_data),
         {:ok, ai_content} <- generate_ai_content(text_content, distributions) do
      {:ok, ai_content}
    else
      {:error, reason} -> {:error, format_error_message(reason)}
    end
  end

  # Format error messages for consistent output
  defp format_error_message(reason) when is_binary(reason), do: reason
  defp format_error_message(reason), do: "Error: #{inspect(reason)}"

  # Extract filename and binary data from the uploaded PDF
  defp extract_upload_data(upload) do
    case upload do
      %Plug.Upload{filename: filename, path: path} ->
        case File.read(path) do
          {:ok, binary_data} ->
            {:ok, filename, binary_data}

          {:error, reason} ->
            {:error, "Failed to read uploaded file: #{inspect(reason)}"}
        end

      _ ->
        {:error, "Invalid upload format"}
    end
  end

  # Extract text content from the PDF binary data
  defp extract_text_from_pdf(binary_data) do
    # Use a temporary file to store the PDF
    temp_path = Path.join(System.tmp_dir(), "#{:rand.uniform(1_000_000)}.pdf")

    try do
      File.write!(temp_path, binary_data)

      # Use convert_announcement_to_text which is designed to handle large PDFs
      case pdf_to_text(temp_path) do
        {:ok, text_content} when is_binary(text_content) and text_content != "" ->
          # Truncate the text to fit within GPT-4o's context window (roughly 6000 tokens to leave room for prompts)
          # ~6000 tokens
          truncated_text = String.slice(text_content, 0, 24_000)
          {:ok, truncated_text}

        {:ok, _} ->
          {:error, "Failed to extract meaningful text from PDF"}

        {:error, reason} ->
          {:error, "Failed to extract text from PDF: #{inspect(reason)}"}
      end
    rescue
      e ->
        {:error, "Error processing PDF: #{inspect(e)}"}
    after
      # Clean up the temporary file
      File.rm(temp_path)
    end
  end

  defp pdf_to_text(path) do
    case System.cmd("pdftotext", [
           path,
           "-",
           "-nodiag"
         ]) do
      {text, 0} -> {:ok, text}
      {_, status} -> {:error, "PDF conversion failed with status #{status}"}
    end
  end

  # Generate AI content based on the extracted text and requested distributions
  defp generate_ai_content(text_content, distributions) do
    # Initialize the result map with nil values for all possible distributions
    result = %{
      update: nil,
      announcement: nil,
      x: nil,
      linkedin: nil
    }

    # Process each requested distribution
    Enum.reduce_while(distributions, {:ok, result}, fn distribution, {:ok, acc} ->
      case process_distribution(text_content, distribution) do
        {:ok, content} ->
          {:cont, {:ok, Map.put(acc, String.to_atom(distribution), content)}}

        {:error, reason} ->
          {:halt, {:error, reason}}
      end
    end)
  end

  # Process each type of distribution
  defp process_distribution(text_content, "update") do
    # For update, we need to generate a blog post in Tiptap format
    system_prompt = """
    You are an expert content creator. Create a well-structured blog post based on the PDF content provided.
    The blog post should be informative, engaging, and maintain the key points from the original document.

    You must return your response in Tiptap JSON format, which is a structured format for rich text content.

    The response should be a valid JSON object with this structure:
    {
      "type": "doc",
      "content": [
        // Array of content nodes
      ]
    }

    Use these node types:
    1. For headings:
       { "type": "heading", "attrs": { "level": 1 to 6 }, "content": [{ "type": "text", "text": "Heading text" }] }

    2. For paragraphs:
       { "type": "paragraph", "content": [{ "type": "text", "text": "Paragraph text" }] }

    3. For bullet lists:
       {
         "type": "bulletList",
         "content": [
           {
             "type": "listItem",
             "content": [
               { "type": "paragraph", "content": [{ "type": "text", "text": "List item text" }] }
             ]
           },
           // More list items...
         ]
       }

    4. For ordered lists:
       {
         "type": "orderedList",
         "content": [
           {
             "type": "listItem",
             "content": [
               { "type": "paragraph", "content": [{ "type": "text", "text": "List item text" }] }
             ]
           },
           // More list items...
         ]
       }

    Return ONLY the JSON object, with no additional text or explanation.
    """

    user_prompt = """
    Create a blog post based on the following content. Include appropriate headings, paragraphs, and formatting.
    Return the content in Tiptap JSON format as specified in the system instructions:

    #{text_content}
    """

    # Use LLMChain to interact with the language model
    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(Message.new_user!(user_prompt))
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %LangChain.Message{content: content} when not is_nil(content) ->
            case Jason.decode(content) do
              {:ok, tiptap_content} ->
                {:ok, tiptap_content}

              {:error, _} ->
                {:error, "Failed to parse AI-generated Tiptap JSON. The response was not valid JSON."}
            end

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        Logger.error("Failed to generate blog post: #{inspect(error)}")
        {:error, "Failed to generate blog post: #{inspect(error)}"}
    end
  end

  defp process_distribution(text_content, "announcement") do
    # For announcement, we need a summary under 150 words
    system_prompt = """
    You are an expert summarizer. Create a concise summary of the PDF content in under 150 words.
    The summary should capture the key points and main message of the document.
    """

    user_prompt = """
    Summarize the following content in under 150 words:

    #{text_content}
    """

    # Use LLMChain to interact with the language model
    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(Message.new_user!(user_prompt))
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %LangChain.Message{content: content} when not is_nil(content) ->
            {:ok, content}

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        Logger.error("Failed to generate summary: #{inspect(error)}")
        {:error, "Failed to generate summary: #{inspect(error)}"}
    end
  end

  defp process_distribution(text_content, "x") do
    # For X (Twitter), we need a tweet
    system_prompt = """
    You are a social media expert. Create an engaging tweet based on the PDF content.
    The tweet should be concise, attention-grabbing, and convey the main point of the document.
    """

    user_prompt = """
    Create a tweet based on the following content:

    #{text_content}
    """

    # Use LLMChain to interact with the language model
    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(Message.new_user!(user_prompt))
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %LangChain.Message{content: content} when not is_nil(content) ->
            {:ok, content}

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        Logger.error("Failed to generate tweet: #{inspect(error)}")
        {:error, "Failed to generate tweet: #{inspect(error)}"}
    end
  end

  defp process_distribution(text_content, "linkedin") do
    # For LinkedIn, we need a professional post
    system_prompt = """
    You are a professional content creator. Create a LinkedIn post based on the PDF content.
    The post should be professional, informative, and suitable for a business audience.
    """

    user_prompt = """
    Create a LinkedIn post based on the following content:

    #{text_content}
    """

    # Use LLMChain to interact with the language model
    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(Message.new_user!(user_prompt))
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %LangChain.Message{content: content} when not is_nil(content) ->
            {:ok, content}

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        Logger.error("Failed to generate LinkedIn post: #{inspect(error)}")
        {:error, "Failed to generate LinkedIn post: #{inspect(error)}"}
    end
  end

  defp process_distribution(_text_content, unknown_distribution) do
    {:error, "Unknown distribution type: #{unknown_distribution}"}
  end
end
