defmodule AthenaWeb.Resolvers.Interactions.CreateMediaComment do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{media_id: media_id} = args, %{
        context: %{
          current_company_profile_user:
            %ProfileUser{profile_id: current_company_profile_id, user: %User{id: current_user_id}} = _company_profile_user
        }
      }) do
    with %Media{company_profile_id: company_profile_id} <- Interactions.get_media(media_id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, media_comment} <-
           args
           |> Map.put(:company_author_id, current_user_id)
           |> Map.put(:private, false)
           |> Interactions.create_media_comment() do
      {:ok, media_comment}
    else
      nil ->
        {:error, "Media does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
