defmodule AthenaWeb.Resolvers.Interactions.UpdateMediaUpdateTitle do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate

  def resolve(_parent, %{id: id, title: title}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with {:get_media_update, %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = existing_media_update} <-
           {:get_media_update, Interactions.get_media_update(id)},
         {:check_ownership, true} <- {:check_ownership, company_profile_id == current_company_profile_id},
         {:update_media_update, {:ok, %MediaUpdate{} = updated_media_update}} <-
           {:update_media_update,
            Interactions.update_media_update(existing_media_update, %{
              title: title,
              slug: Slug.slugify(title),
              last_updated_by_id: current_company_user_id
            })} do
      {:ok, updated_media_update}
    else
      {:get_media_update, nil} ->
        {:error, "Media update does not exist."}

      {:check_ownership, false} ->
        {:error, "You are unauthorised."}

      {:update_media_update,
       {:error,
        %Ecto.Changeset{
          errors: [
            slug: {"has already been taken", _}
          ]
        }}} ->
        {:error, "Update titles need to be unique. An update with this title already exists, please try another."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
