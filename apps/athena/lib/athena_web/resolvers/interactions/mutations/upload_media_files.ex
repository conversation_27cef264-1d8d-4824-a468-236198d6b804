defmodule AthenaWeb.Resolvers.Interactions.UploadMediaFiles do
  @moduledoc """
  Resolver for uploading multiple media files to be analysed by AI
  """

  alias Gaia.AI.MediaConversions
  alias Gaia.Interactions
  alias Gaia.Jobs.ConvertMediaToMarkdown

  @doc """
  Upload multiple media files to be analysed by AI
  """
  def resolve(_parent, %{media_id: media_id, files: files}, %{context: %{current_company_profile: company_profile}}) do
    with {:ok, media} <- get_media(media_id, company_profile.id),
         {:ok, conversions} <- create_media_conversions(media, company_profile, files) do
      {:ok, conversions}
    else
      {:error, reason} -> {:error, format_error_message(reason)}
    end
  end

  # Get the media record and verify it belongs to the current company profile
  defp get_media(media_id, company_profile_id) do
    case Interactions.get_media(media_id) do
      nil ->
        {:error, "Media not found"}

      media ->
        if media.company_profile_id == company_profile_id do
          {:ok, media}
        else
          {:error, "Media does not belong to current company profile"}
        end
    end
  end

  # Create multiple media conversion records
  defp create_media_conversions(media, company_profile, files) do
    results =
      Enum.map(files, fn file_input ->
        create_and_enqueue_conversion(media, company_profile, file_input)
      end)

    # Check if any conversions failed
    if Enum.any?(results, fn {status, _} -> status == :error end) do
      # Get the first error
      {_, error} = Enum.find(results, fn {status, _} -> status == :error end)
      {:error, error}
    else
      # Extract all successful conversions
      conversions = Enum.map(results, fn {:ok, conversion} -> conversion end)
      {:ok, conversions}
    end
  end

  # Create a single media conversion and enqueue analysis job
  defp create_and_enqueue_conversion(media, company_profile, %{cloudinary_id: cloudinary_id, file_type: file_type}) do
    # Extract filename from cloudinary_id
    filename = extract_filename_from_cloudinary_id(cloudinary_id)

    with {:ok, conversion} <-
           MediaConversions.create_media_conversion(%{
             media_id: media.id,
             company_profile_id: company_profile.id,
             file_name: filename,
             file_type: file_type,
             cloudinary_id: cloudinary_id
           }),
         {:ok, _job} <- enqueue_analysis_job(conversion) do
      {:ok, conversion}
    else
      {:error, reason} -> {:error, format_error_message(reason)}
    end
  end

  # Extract filename from cloudinary_id
  defp extract_filename_from_cloudinary_id(cloudinary_id) do
    # If cloudinary_id contains a path, extract just the filename
    case String.split(cloudinary_id, "/") do
      [filename] -> filename
      parts -> List.last(parts)
    end
  end

  # Enqueue the analysis job
  defp enqueue_analysis_job(conversion) do
    ConvertMediaToMarkdown.enqueue(%{
      "conversion_id" => conversion.id
    })
  end

  # Format error messages for consistent output
  defp format_error_message(reason) when is_binary(reason), do: reason
  defp format_error_message(reason), do: "Error: #{inspect(reason)}"
end
