defmodule AthenaWeb.Resolvers.Interactions.DeletePreparedAnnouncement do
  @moduledoc """
  DeletePreparedAnnouncement Mutation
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.PreparedAnnouncement

  def resolve(_, %{id: id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id} = _company_profile_user
        }
      }) do
    with %PreparedAnnouncement{company_profile_id: company_profile_id} = existing_prepared_announcement <-
           Interactions.get_prepared_announcement_by(%{id: id}),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %PreparedAnnouncement{} = _deleted_prepared_announcement} <-
           Interactions.delete_prepared_announcement(existing_prepared_announcement) do
      {:ok, true}
    else
      nil ->
        {:error, "Prepared announcement does not exist"}

      false ->
        {:error, "You are unauthorised"}

      _error ->
        {:error, "Oops! Something went wrong"}
    end
  end
end
