defmodule AthenaWeb.Resolvers.Interactions.UpsertMediaCommentRead do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaComment

  def resolve(_, %{media_comment_id: media_comment_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_profile_id, user_id: current_user_id}}
      }) do
    with %MediaComment{media: %Media{company_profile_id: company_profile_id}} <-
           Interactions.get_media_comment_with_media(media_comment_id),
         true <- company_profile_id == current_profile_id,
         {:ok, mcr} <-
           args
           |> Map.put(:company_user_id, current_user_id)
           |> Interactions.upsert_media_comment_read() do
      {:ok, mcr}
    else
      nil ->
        {:error, "Media comment does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
