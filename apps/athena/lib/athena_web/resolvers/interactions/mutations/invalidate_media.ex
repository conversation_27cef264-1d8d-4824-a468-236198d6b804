defmodule AthenaWeb.Resolvers.Interactions.InvalidateMedia do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Media{company_profile_id: company_profile_id} = media <-
           Interactions.get_media(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, _} <- Interactions.invalidate_media(media) do
      {:ok, media}
    else
      nil ->
        {:error, "Media does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
