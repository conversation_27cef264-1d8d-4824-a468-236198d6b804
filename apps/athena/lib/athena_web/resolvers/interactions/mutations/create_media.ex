defmodule AthenaWeb.Resolvers.Interactions.CreateMedia do
  @moduledoc """
  Create a new media record
  """

  alias Gaia.AI.MediaConversions
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Jobs.ConvertMediaToMarkdown
  alias Gaia.Websites.CloudinaryUploader

  require Logger

  @doc """
  Create a new media record with optional media processing
  """
  def resolve(_, %{media: attrs}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: current_company_profile_user_id,
            profile_id: current_company_profile_id
          }
        }
      }) do
    attrs_for_creation =
      attrs
      |> Map.delete(:pdf)
      |> Map.delete(:image)
      |> Map.delete(:audio)
      |> Map.delete(:file_type)
      |> Map.put(:company_profile_id, current_company_profile_id)
      |> Map.put(:state, :draft)
      |> Map.put(:created_by_profile_user_id, current_company_profile_user_id)

    with {:ok, media} <- Interactions.create_media(attrs_for_creation),
         {:ok, processed_media} <- maybe_process_uploaded_file(media, attrs) do
      {:ok, processed_media}
    else
      {:error, _changeset} ->
        handle_media_creation_error()
    end
  end

  # Try to infer file type from the provided attributes
  defp infer_file_type(%{file_type: type}) when type in ["pdf", "image", "audio"], do: {:ok, String.to_atom(type)}
  defp infer_file_type(%{pdf: pdf}) when not is_nil(pdf), do: {:ok, :pdf}
  defp infer_file_type(%{image: image}) when not is_nil(image), do: {:ok, :image}
  defp infer_file_type(%{audio: audio}) when not is_nil(audio), do: {:ok, :audio}
  defp infer_file_type(_), do: {:error, :invalid_file_type}

  defp validate_media_upload(attrs, :pdf) when is_map_key(attrs, :pdf), do: {:ok, attrs.pdf}
  defp validate_media_upload(attrs, :image) when is_map_key(attrs, :image), do: {:ok, attrs.image}
  defp validate_media_upload(attrs, :audio) when is_map_key(attrs, :audio), do: {:ok, attrs.audio}
  defp validate_media_upload(%{pdf: pdf}, _) when not is_nil(pdf), do: {:ok, {:pdf, pdf}}
  defp validate_media_upload(_, _), do: {:error, :invalid_upload}

  defp maybe_process_uploaded_file(media, attrs) do
    with {:ok, file_type} <- infer_file_type(attrs),
         {:ok, media_upload} <- validate_media_upload(attrs, file_type),
         {:ok, _result} <- create_oban_job_for_media_processing(media_upload, media.id, file_type) do
      {:ok, media}
    else
      {:error, :invalid_file_type} ->
        {:ok, media}

      {:error, :invalid_upload} ->
        {:ok, media}

      {:error, reason} ->
        Logger.error("Error processing media: #{inspect(reason)}")
        {:ok, media}
    end
  end

  defp handle_media_creation_error do
    {:error, "Media creation failed"}
  end

  @doc """
  Asynchronously process a media file using AI capabilities.
  Creates an Oban job to handle the processing in the background.

  ## Parameters

  - media_upload: The media file upload (Plug.Upload or similar)
  - media_id: The ID of the media record to update with the results
  - file_type: The type of media file (pdf, image, audio)

  ## Returns

  - {:ok, %{job_id: job_id}} on success
  - {:error, reason} on failure
  """
  def create_oban_job_for_media_processing(media_upload, media_id, file_type) do
    with {:ok, upload_path} <- validate_media_upload(media_upload),
         {:ok, file_size} <- get_file_size(upload_path),
         {:ok, media} <- get_media(media_id),
         {:ok, conversion} <-
           create_media_conversion(media_upload, file_type, file_size, media_id, media.company_profile_id),
         {:ok, cloudinary_result} <- upload_to_cloudinary(upload_path, conversion, media.company_profile_id, file_type),
         {:ok, updated_conversion} <- update_conversion_with_cloudinary(conversion, cloudinary_result, file_type),
         {:ok, job_id} <- enqueue_analysis_job(updated_conversion) do
      {:ok, %{job_id: job_id, page_count: Map.get(cloudinary_result, "pages", nil)}}
    else
      {:error, reason} -> {:error, reason}
      nil -> {:error, "Invalid media upload: Media file must be provided"}
    end
  end

  defp validate_media_upload(media_upload) do
    case get_upload_path(media_upload) do
      nil -> {:error, "Invalid media upload format"}
      path -> {:ok, path}
    end
  end

  defp get_file_size(path) do
    case File.stat(path) do
      {:ok, %{size: size}} -> {:ok, size}
      {:error, reason} -> {:error, "Failed to get file size: #{inspect(reason)}"}
    end
  end

  defp get_media(media_id) do
    {:ok, Gaia.Interactions.get_media!(media_id)}
  rescue
    _ -> {:error, "Media not found"}
  end

  defp create_media_conversion(media_upload, file_type, file_size, media_id, company_profile_id) do
    MediaConversions.create_media_conversion(%{
      file_name: media_upload.filename,
      file_type: file_type,
      size: file_size,
      media_id: media_id,
      company_profile_id: company_profile_id
    })
  end

  defp upload_to_cloudinary(upload_path, conversion, company_profile_id, file_type) do
    cloud_name = Application.get_env(:cloudex, :root_folder)
    folder = "#{cloud_name}/#{company_profile_id}/ai_media_conversions/#{conversion.id}"
    resource_type = get_cloudinary_resource_type(file_type)

    CloudinaryUploader.upload(upload_path, %{
      folder: folder,
      resource_type: resource_type,
      type: "private"
    })
  end

  defp update_conversion_with_cloudinary(conversion, result, file_type) do
    page_count = if file_type == :pdf, do: Map.get(result, "pages", nil)

    MediaConversions.update_media_conversion(conversion, %{
      cloudinary_id: result["public_id"],
      page_count: page_count
    })
  end

  defp enqueue_analysis_job(conversion) do
    case ConvertMediaToMarkdown.enqueue(%{conversion_id: conversion.id}) do
      {:ok, %{id: job_id}} ->
        {:ok, _updated_conversion} =
          MediaConversions.update_media_conversion(conversion, %{
            oban_job_id: "#{job_id}"
          })

        {:ok, job_id}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # Helper function to determine Cloudinary resource_type based on file_type
  defp get_cloudinary_resource_type(:pdf), do: "image"
  defp get_cloudinary_resource_type(:image), do: "image"
  # Cloudinary uses "video" for audio files
  defp get_cloudinary_resource_type(:audio), do: "video"
  defp get_cloudinary_resource_type(_), do: "auto"

  # Helper function to extract the path from different upload formats
  defp get_upload_path(%{path: path}) when is_binary(path), do: path
  defp get_upload_path(%Plug.Upload{path: path}) when is_binary(path), do: path
  defp get_upload_path(_), do: nil
end
