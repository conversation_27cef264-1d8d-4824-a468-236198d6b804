defmodule AthenaWeb.Resolvers.Interactions.PublishMediaUpdate do
  @moduledoc """
  Resolvers for PublishMediaUpdate

  After publishing media update, automatically distribute if enabled
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate

  def resolve(_parent, %{id: id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id, user_id: current_user_id}
        }
      }) do
    with %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = media_update <-
           Interactions.get_media_update(id),
         true <- company_profile_id == current_company_profile_id,
         {:ok, updated_mu} <-
           Interactions.publish_media_update(media_update, current_user_id) do
      {:ok, updated_mu}
    else
      nil ->
        {:error, "Update does not exist."}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Unable to publish update."}
    end
  end
end
