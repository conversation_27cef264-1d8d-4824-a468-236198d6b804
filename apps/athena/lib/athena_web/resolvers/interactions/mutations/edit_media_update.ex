defmodule AthenaWeb.Resolvers.Interactions.EditMediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def resolve(_parent, %{media_id: media_id, media_update: media_update_attrs}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    whitelisted_attrs = Map.take(media_update_attrs, [:content_draft, :title, :thumbnail_url])

    with {:ok, media} <- media_permitted(media_id, current_company_profile_id),
         {:ok, media_update} <-
           create_or_update_media_update(media, whitelisted_attrs, current_company_user_id) do
      {:ok, media_update}
    else
      nil ->
        {:error, "Media update not found."}

      {:error, :unauthorised} ->
        {:error, "You are not authorised to edit this media update."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp create_or_update_media_update(media, media_update_attrs, current_company_user_id) do
    case Interactions.get_media_update_by(media_id: media.id) do
      nil ->
        media_update_attrs =
          Map.merge(media_update_attrs, %{
            company_profile_id: media.company_profile_id,
            media_id: media.id,
            last_updated_by_id: current_company_user_id,
            title: media.title,
            slug: Slug.slugify(media.title)
          })

        Interactions.create_newsflow_media_update(media_update_attrs)

      media_update ->
        Interactions.update_media_update(media_update, media_update_attrs)
    end
  end

  defp media_permitted(media_id, current_company_profile_id) do
    case Interactions.get_media(media_id) do
      %{company_profile_id: ^current_company_profile_id} = media ->
        {:ok, Repo.preload(media, [:media_update])}

      _ ->
        {:error, :unauthorised}
    end
  end
end
