defmodule AthenaWeb.Resolvers.Interactions.NonDraftNotLinkedPreparedAnnouncements do
  @moduledoc """
    PreparedAnnouncements resolver
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(_, %{search: search}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Interactions.list_non_draft_not_linked_prepared_announcements_for_company(
           company_profile_id,
           search
         ) do
      announcements when is_list(announcements) ->
        {:ok, announcements}

      _error ->
        {:error, "Unable to get non draft prepared announcements"}
    end
  end
end
