defmodule AthenaWeb.Resolvers.Interactions.AnnouncementsList do
  @moduledoc """
    AnnouncementsList includes both media_announcements and prepared_announcements
  """
  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def resolve(_parents, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    case options
         |> Interactions.announcements_list_query()
         |> select([ma, t, pa], %{prepared_announcement: pa, media_announcement: ma})
         |> Connection.from_query(&get_announcements_list/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  # Issue about full_join:
  # The left joined table is populated even if it is null.
  # So, if media_announcement id is nil, media_announcement will return nil as a whole
  defp get_announcements_list(query) do
    query
    |> Repo.all()
    |> Enum.map(fn
      # Handle case when media_announcement is nil
      %{prepared_announcement: pa, media_announcement: nil} ->
        %{prepared_announcement: pa, media_announcement: nil}

      # Handle case when media_announcement is present but id is nil
      %{prepared_announcement: pa, media_announcement: %{id: nil}} ->
        %{prepared_announcement: pa, media_announcement: nil}

      # Handle normal case when media_announcement is present with an id
      %{media_announcement: %{id: _id}} = announcement ->
        announcement
    end)
  end
end
