defmodule AthenaWeb.Resolvers.Interactions.MediaInteractedInvestors do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.InvestorMediaInteractions
  alias Gaia.Repo

  def cursor(_parents, %{media_id: media_id, media_type: media_type} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    connection_result =
      args
      |> InvestorMediaInteractions.media_interacted_investors_query(media_id, media_type, company_profile_id)
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: Map.get(args, :options, %{filters: [], orders: []})
         }}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  def total(_parents, %{media_id: media_id, media_type: media_type} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    length =
      args
      |> InvestorMediaInteractions.media_interacted_investors_count_query(media_id, media_type, company_profile_id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end
end
