defmodule AthenaWeb.Resolvers.Interactions.InteractiveMediaStats do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    {:ok,
     %{
       total_active_questions: Interactions.count_total_active_media_comments_for_company(current_company_profile_id),
       total_likes_last_week:
         Interactions.count_total_media_likes_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.beginning_of_week(),
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.end_of_week()
         ),
       total_likes_this_week:
         Interactions.count_total_media_likes_for_company(
           current_company_profile_id,
           Timex.beginning_of_week(DateTime.utc_now()),
           Timex.end_of_week(DateTime.utc_now())
         ),
       total_likes: Interactions.count_total_media_likes_for_company(current_company_profile_id),
       total_questions_last_week:
         Interactions.count_total_media_comments_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.beginning_of_week(),
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.end_of_week()
         ),
       total_questions_this_week:
         Interactions.count_total_media_comments_for_company(
           current_company_profile_id,
           Timex.beginning_of_week(DateTime.utc_now()),
           Timex.end_of_week(DateTime.utc_now())
         ),
       total_questions: Interactions.count_total_media_comments_for_company(current_company_profile_id),
       total_survey_responses_last_week:
         Interactions.count_total_distinct_survey_responses_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.beginning_of_week(),
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.end_of_week()
         ),
       total_survey_responses_this_week:
         Interactions.count_total_distinct_survey_responses_for_company(
           current_company_profile_id,
           Timex.beginning_of_week(DateTime.utc_now()),
           Timex.end_of_week(DateTime.utc_now())
         ),
       total_survey_responses: Interactions.count_total_distinct_survey_responses_for_company(current_company_profile_id),
       total_announcements_last_week:
         Interactions.count_total_announcements_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.beginning_of_week(),
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.end_of_week()
         ),
       total_announcements_this_week:
         Interactions.count_total_announcements_for_company(
           current_company_profile_id,
           Timex.beginning_of_week(DateTime.utc_now()),
           Timex.end_of_week(DateTime.utc_now())
         ),
       total_announcements: Interactions.count_total_announcements_for_company(current_company_profile_id),
       total_announcements_last_month:
         Interactions.count_total_announcements_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(months: -1) |> Timex.beginning_of_month(),
           DateTime.utc_now() |> Timex.shift(months: -1) |> Timex.end_of_month()
         ),
       total_announcements_this_month:
         Interactions.count_total_announcements_for_company(
           current_company_profile_id,
           Timex.beginning_of_month(DateTime.utc_now()),
           Timex.end_of_month(DateTime.utc_now())
         ),
       total_announcements_this_year:
         Interactions.count_total_announcements_for_company(
           current_company_profile_id,
           Timex.beginning_of_year(DateTime.utc_now()),
           Timex.end_of_year(DateTime.utc_now())
         ),
       total_updates_last_week:
         Interactions.count_total_updates_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.beginning_of_week(),
           DateTime.utc_now() |> Timex.shift(weeks: -1) |> Timex.end_of_week()
         ),
       total_updates_this_week:
         Interactions.count_total_updates_for_company(
           current_company_profile_id,
           Timex.beginning_of_week(DateTime.utc_now()),
           Timex.end_of_week(DateTime.utc_now())
         ),
       total_updates: Interactions.count_total_updates_for_company(current_company_profile_id),
       total_updates_last_month:
         Interactions.count_total_updates_for_company(
           current_company_profile_id,
           DateTime.utc_now() |> Timex.shift(months: -1) |> Timex.beginning_of_month(),
           DateTime.utc_now() |> Timex.shift(months: -1) |> Timex.end_of_month()
         ),
       total_updates_this_month:
         Interactions.count_total_updates_for_company(
           current_company_profile_id,
           Timex.beginning_of_month(DateTime.utc_now()),
           Timex.end_of_month(DateTime.utc_now())
         ),
       total_updates_this_year:
         Interactions.count_total_updates_for_company(
           current_company_profile_id,
           Timex.beginning_of_year(DateTime.utc_now()),
           Timex.end_of_year(DateTime.utc_now())
         )
     }}
  end
end
