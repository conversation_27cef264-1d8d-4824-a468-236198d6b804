defmodule AthenaWeb.Resolvers.Interactions.LinkedinMediaStatistics do
  @moduledoc """
  Resolvers for GraphQL field linkedin_media_statistics
  """
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection
  alias Gaia.Interactions
  alias Gaia.Socials
  alias Socials.LinkedIn

  def resolve(_parent, %{post_id: post_urn}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{
              social_connection:
                %SocialConnection{linkedin_access_token: access_token, linkedin_organisation_id: organisation_urn} =
                  social_connection
            }
          }
        }
      }) do
    with {:check_linkedin_media, %Interactions.SocialPost{}} <-
           {:check_linkedin_media,
            Interactions.get_social_post_by(%{
              social_post_id: post_urn,
              platform: :linkedin
            })},
         {:check_linkedin_connected, true} <-
           {:check_linkedin_connected, Companies.get_is_linkedin_connected(social_connection)},
         {:ok, post_statistics} <-
           LinkedIn.post_statistics(access_token, organisation_urn, post_urn) do
      # Cache the statistics in the social_post.analytics_data field
      social_post =
        Interactions.get_social_post_by(%{
          social_post_id: post_urn,
          platform: :linkedin
        })

      # Update the analytics_data field with the latest statistics
      if social_post do
        Interactions.update_social_post(social_post, %{analytics_data: post_statistics})
      end

      {:ok, post_statistics}
    else
      {:check_linkedin_media, _} ->
        {:error, "LinkedIn post not found"}

      {:check_linkedin_connected, _} ->
        {:error, "LinkedIn not connected"}

      {:error, reason} ->
        {:error, "Unable to retrieve LinkedIn post statistics: #{inspect(reason)}"}
    end
  end

  def resolve(_, _, _) do
    {:error, "Unable to retrieve LinkedIn post statistics"}
  end
end
