defmodule AthenaWeb.Resolvers.Interactions.TotalStarredMediaQuestions do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def resolve(_parents, args, %{
        context: %{
          current_company_profile_user: %ProfileUser{user: %{id: company_user_id}, profile_id: current_company_profile_id}
        }
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id" and !String.starts_with?(&1.key, "tab-")))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id},
        %{key: "starred", value: "true"}
      ])

    {:ok,
     args
     |> Map.get(:options, %{})
     |> Map.put(:filters, filters)
     |> Interactions.media_questions_query(current_company_profile_id, company_user_id)
     |> Repo.aggregate(:count)}
  end
end
