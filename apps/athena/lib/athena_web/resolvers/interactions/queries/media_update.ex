defmodule AthenaWeb.Resolvers.Interactions.MediaUpdate do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = media_update <-
           Interactions.get_media_update(id),
         true <- company_profile_id == current_company_profile_id do
      {:ok, media_update}
    else
      nil ->
        {:ok, nil}

      false ->
        {:error, "You are unauthorized or don't have required permission!"}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
