defmodule AthenaWeb.Resolvers.Interactions.ExistingTags do
  @moduledoc """
  ExistingTags Query Resolvers
  """

  alias Gaia.Interactions

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{} = current_company_profile
          }
        }
      }) do
    {:ok, Interactions.existing_tags(current_company_profile)}
  end

  def resolve(_, _, _) do
    {:ok, []}
  end
end
