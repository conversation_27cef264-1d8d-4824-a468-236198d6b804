defmodule AthenaWeb.Resolvers.Interactions.MediaSurveyAnswers do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaSurveyAnswer

  def resolve(_parents, %{media_id: media_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    case Interactions.get_media_survey_answers_for_media(media_id) do
      [%MediaSurveyAnswer{media: %Media{company_profile_id: company_profile_id}} | _tail] = results
      when company_profile_id == current_company_profile_id ->
        {:ok, results}

      _ ->
        {:ok, []}
    end
  end
end
