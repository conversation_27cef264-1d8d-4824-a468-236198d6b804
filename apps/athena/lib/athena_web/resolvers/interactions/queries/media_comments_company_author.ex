defmodule AthenaWeb.Resolvers.Interactions.MediaCommentsCompanyAuthor do
  @moduledoc false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(_parent, %{media_id: media_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with true <- Interactions.media_belongs_to_company?(media_id, company_profile_id),
         media_comments = Interactions.get_company_authored_media_comments(media_id),
         true <- is_list(media_comments) do
      {:ok, media_comments}
    else
      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
