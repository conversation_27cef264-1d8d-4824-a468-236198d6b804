defmodule AthenaWeb.Resolvers.Interactions.AIAnswerComments do
  @moduledoc false

  alias Gaia.Interactions

  def resolve(_, %{media_comment_id: media_comment_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    with %Gaia.Interactions.MediaComment{
           media: %Gaia.Interactions.Media{
             company_profile: %Gaia.Companies.Profile{
               id: company_profile_id
             }
           }
         } = media_comment <-
           media_comment_id |> Interactions.get_media_comment() |> Gaia.Repo.preload(media: :company_profile),
         true <- company_profile_id == current_company_profile_id,
         {:ok, answer} <- Gaia.AI.AnnouncementAnswerQuestions.answer_media_comment(media_comment) do
      {:ok, answer}
    else
      _error ->
        {:error, "Unable to generate AI answer at the moment, please try again."}
    end
  end
end
