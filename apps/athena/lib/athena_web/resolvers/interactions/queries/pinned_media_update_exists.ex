defmodule AthenaWeb.Resolvers.Interactions.PinnedMediaUpdateExists do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions

  def resolve(_parents, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    case Interactions.get_company_current_pinned_media_update(current_company_profile_id) do
      nil -> {:ok, false}
      _ -> {:ok, true}
    end
  end
end
