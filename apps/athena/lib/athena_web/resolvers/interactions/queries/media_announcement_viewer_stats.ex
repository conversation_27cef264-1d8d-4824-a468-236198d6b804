defmodule AthenaWeb.Resolvers.Interactions.MediaAnnouncementViewerStats do
  @moduledoc """
  Resolves the viewer stats for a media announcement
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement

  def resolve(_, %{media_id: media_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    %{
      id: media_id,
      company_profile_id: current_company_profile_id
    }
    |> Interactions.get_media_by()
    |> Gaia.Repo.preload([:media_announcement])
    |> case do
      %Media{media_announcement: %MediaAnnouncement{}} = media ->
        {:ok, get_media_announcement_views(media)}

      _error ->
        {:error, "Unable to get media announcement viewer stats"}
    end
  end

  def resolve(_, _, _), do: {:error, "Unable to get media announcement viewer stats"}

  defp get_media_announcement_views(
         %Media{media_announcement: media_announcement, company_profile_id: company_profile_id} = media
       ) do
    utm_source_clicks = Gaia.Tracking.get_utm_source_clicks_by_announcement_id(company_profile_id, media_announcement.id)

    campaign_views = Interactions.count_total_distributed_campaign_views_for_media(media.id)

    prepared_link_views = Interactions.count_prepared_link_views_for_media_announcement(media_announcement)

    twitter_views =
      Enum.reduce(utm_source_clicks, 0, fn
        %{utm_source: "twitter", count: count}, acc -> acc + count
        %{utm_source: "x", count: count}, acc -> acc + count
        _, acc -> acc
      end)

    linkedin_views =
      utm_source_clicks |> Enum.find(%{count: 0}, fn ul -> ul.utm_source == "linkedin" end) |> Map.get(:count)

    other =
      Gaia.Tracking.count_interactive_announcement_views(media_announcement.id, media.company_profile_id) -
        (campaign_views + prepared_link_views + twitter_views + linkedin_views)

    %{
      campaign_views: campaign_views,
      prepared_link_views: prepared_link_views,
      twitter_views: twitter_views,
      linkedin_views: linkedin_views,
      other: other
    }
  end
end
