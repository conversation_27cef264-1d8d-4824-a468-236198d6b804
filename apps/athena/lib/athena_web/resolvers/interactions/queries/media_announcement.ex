defmodule AthenaWeb.Resolvers.Interactions.MediaAnnouncement do
  @moduledoc false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %MediaAnnouncement{media: %Media{company_profile_id: company_profile_id}} = media_announcement <-
           Interactions.get_media_announcement(id),
         true <- company_profile_id == current_company_profile_id do
      {:ok, media_announcement}
    else
      nil ->
        {:ok, nil}

      false ->
        {:error, "You are unauthorised."}
    end
  end
end
