defmodule AthenaWeb.Resolvers.Interactions.MediaQuestionsList do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def cursor(_parents, args, %{
        context: %{
          current_company_profile_user: %ProfileUser{user: %{id: company_user_id}, profile_id: current_company_profile_id}
        }
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    case options
         |> Interactions.media_questions_query(current_company_profile_id, company_user_id)
         |> Connection.from_query(&Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
