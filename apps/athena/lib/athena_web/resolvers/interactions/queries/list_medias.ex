defmodule AthenaWeb.Resolvers.Interactions.ListMedias do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    options = build_options(args, current_company_profile_id)

    # Create a custom function to preload associations when fetching media items
    fetch_with_preloads = fn query ->
      query
      |> Repo.all()
      |> Repo.preload([
        :media_announcement,
        :media_update,
        :email,
        :linkedin_social_post,
        :twitter_social_post,
        :prepared_announcement
      ])
    end

    case options
         |> Interactions.medias_query()
         |> Connection.from_query(fetch_with_preloads, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info,
           # Store the parent args so the total resolver can access them
           __parent_args__: args
         }}

      {:error, _reason} ->
        {:error, "Failed to fetch medias"}
    end
  end

  def resolve_total(parent, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    # Use parent args if available, otherwise fall back to field args
    effective_args = Map.get(parent, :__parent_args__, args)

    options = build_options(effective_args, current_company_profile_id)

    # Build the same query but select only distinct media IDs for counting
    base_query = Interactions.medias_query(options)

    # Remove any order_by clauses for the count query
    count_query = from(m in base_query, select: count(m.id, :distinct))
    count_query = %{count_query | order_bys: []}

    case Repo.one(count_query) do
      count when is_integer(count) ->
        {:ok, count}

      _error ->
        {:error, "Failed to get the total number of medias"}
    end
  end

  defp build_options(args, current_company_profile_id) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    args
    |> Map.get(:options, %{})
    |> Map.put(:filters, filters)
  end
end
