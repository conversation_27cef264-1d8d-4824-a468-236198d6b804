defmodule AthenaWeb.Resolvers.Interactions.OldestMediaAnnouncementDate do
  @moduledoc false

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Markets.Ticker

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{ticker: %Ticker{listing_key: listing_key}}}
        }
      }) do
    {:ok, Interactions.get_oldest_media_announcement_date(listing_key)}
  end
end
