defmodule AthenaWeb.Resolvers.Interactions.TotalMediaUpdates do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Repo

  def resolve(_parents, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    {:ok,
     args
     |> Map.get(:options, %{})
     |> Map.put(:filters, filters)
     |> Interactions.media_updates_query()
     |> Repo.aggregate(:count)}
  end
end
