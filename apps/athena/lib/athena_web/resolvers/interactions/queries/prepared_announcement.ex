defmodule AthenaWeb.Resolvers.Interactions.PreparedAnnouncement do
  @moduledoc """
  PreparedAnnouncement Query
  """

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.PreparedAnnouncement

  def resolve(_parent, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %PreparedAnnouncement{company_profile_id: company_profile_id} = prepared_announcement <-
           Interactions.get_prepared_announcement_by(%{id: id}),
         true <- company_profile_id == current_company_profile_id do
      {:ok, prepared_announcement}
    else
      nil ->
        {:ok, nil}

      false ->
        {:error, "You are unauthorised."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end
end
