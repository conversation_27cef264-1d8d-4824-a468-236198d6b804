defmodule AthenaWeb.Resolvers.Dashboard.ShareholderTradingActivity do
  @moduledoc """
  Cursor for shareholder trading activity
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Dashboard
  alias Gaia.Repo

  def cursor(
        _parents,
        %{
          shareholder_trading_activity_input: %{
            end_date: end_date,
            start_date: start_date,
            trading_activity_type: trading_activity_type
          }
        } = args,
        %{context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}}
      ) do
    with {:query, query} when not is_nil(query) <-
           {:query,
            Dashboard.shareholder_trading_activity_query(
              id,
              start_date,
              end_date,
              trading_activity_type,
              Map.get(args, :order)
            )},
         total = query |> subquery() |> select([q], count(q.id)) |> Repo.one(),
         {:ok, %{edges: edges, page_info: page_info}} <-
           Connection.from_query(query, &Repo.all/1, args) do
      {:ok,
       %{
         total: total,
         edges: edges,
         page_info: page_info
       }}
    else
      {:query, nil} ->
        {:ok, nil}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
