defmodule AthenaWeb.Resolvers.Dashboard.CurrentHoldingInsights do
  @moduledoc """
    CurrentHoldingInsights Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.Markets
  alias Gaia.Registers

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with %Date{} = date <-
           Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id),
         %{close: close} <-
           Markets.get_latest_timeseries_non_adjusted_by_company_profile_id(
             company_profile_id,
             max_date: date
           ) do
      average_holding_size = Dashboard.get_average_holding_size(company_profile_id, date)

      holding_timeseries = Dashboard.get_holding_timeseries(company_profile_id, Timex.shift(date, months: -3), date)

      {:ok,
       %{
         id: to_string(date),
         average_holding_size: average_holding_size,
         date: date,
         average_holding_value: average_holding_size * close,
         holding_timeseries: holding_timeseries
       }}
    else
      _ ->
        {:ok, nil}
    end
  end
end
