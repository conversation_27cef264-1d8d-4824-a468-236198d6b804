defmodule AthenaWeb.Resolvers.Dashboard.TradingActivity do
  @moduledoc """
  Cursor for trading activity
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.Repo

  def cursor(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: id}}}) do
    {options, start_date, end_date} = extract_filters(args)
    query = Dashboard.trading_activity_query(options, id, start_date, end_date)

    case query do
      nil ->
        {:ok, nil}

      query ->
        connection(query, args, options)
    end
  end

  def total(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: id}}}) do
    {options, start_date, end_date} = extract_filters(args)
    query = Dashboard.trading_activity_query(options, id, start_date, end_date)

    case query do
      nil ->
        {:ok, nil}

      query ->
        total_query = from(q in subquery(query), select: count("*"))
        {:ok, Repo.one(total_query)}
    end
  end

  def stats(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: id}}}) do
    {_options, start_date, end_date} = extract_filters(args)
    query = Dashboard.trading_activity_stats_query(id, start_date, end_date)

    case query do
      nil ->
        {:ok, nil}

      query ->
        {:ok, Repo.one(query)}
    end
  end

  defp connection(query, args, options) do
    case Connection.from_query(query, &Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  defp extract_filters(args) do
    options = Map.get(args, :options, %{filters: [], orders: []})
    filters = Map.get(options, :filters)
    start_date = filters |> Enum.find(fn filter -> filter.key == "start_date" end) |> Map.get(:value) |> to_date()
    end_date = filters |> Enum.find(fn filter -> filter.key == "end_date" end) |> Map.get(:value) |> to_date()
    {options, start_date, end_date}
  end

  defp to_date(nil), do: nil
  defp to_date("Invalid Date"), do: nil
  defp to_date(%Date{} = date), do: date
  defp to_date(date) when is_binary(date), do: Date.from_iso8601!(date)
end
