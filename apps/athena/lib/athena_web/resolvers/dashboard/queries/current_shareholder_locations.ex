defmodule AthenaWeb.Resolvers.Dashboard.CurrentShareholderLocations do
  @moduledoc """
    CurrentShareholderLocations Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.Registers

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    case Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id) do
      %Date{} = date ->
        {:ok,
         %{
           id: to_string(date),
           date: date,
           shareholder_locations: Dashboard.get_shareholder_locations(company_profile_id, date)
         }}

      _ ->
        {:ok, nil}
    end
  end
end
