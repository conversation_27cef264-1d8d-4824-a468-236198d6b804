defmodule AthenaWeb.Resolvers.Dashboard.MetabaseStaticEmbedUrl do
  @moduledoc """
  MetabaseStaticEmbedUrl Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Markets.Ticker
  alias Joken.Signer

  def resolve(_parent, %{custom_report: custom_report}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = company_profile} = profile_user}
      }) do
    with true <- Gaia.FeatureFlags.enabled?(:insights_page, for: company_profile),
         {:ok, metabase_static_embed_secret} <-
           Application.fetch_env(:gaia, :metabase_static_embed_secret),
         dashboard_id when is_integer(dashboard_id) <-
           get_dashboard_id(custom_report),
         %{} = params <-
           get_params(custom_report, profile_user),
         unsigned_params = get_unsigned_params(custom_report, profile_user),
         payload = %{
           "resource" => %{"dashboard" => dashboard_id},
           "params" => params,
           # 30 mins expiration
           "exp" => DateTime.utc_now() |> DateTime.to_unix() |> Kernel.+(30 * 60)
         },
         signer = Signer.create("HS256", metabase_static_embed_secret),
         {:ok, token, _claims} <-
           Athena.MetabaseStaticEmbedToken.generate_and_sign(payload, signer) do
      {:ok,
       "https://fresh.metabaseapp.com/embed/dashboard/" <>
         token <> unsigned_params <> "#bordered=false&titled=false&downloads=true"}
    else
      _ ->
        {:ok, nil}
    end
  end

  def resolve(_parent, _args, _resolution) do
    {:ok, nil}
  end

  def get_dashboard_id("channel-performance"), do: 2344
  def get_dashboard_id("day-traders"), do: 2608
  def get_dashboard_id("email-campaign-performance"), do: 2047
  def get_dashboard_id("hnw-overview"), do: 2476
  def get_dashboard_id("profitability-report"), do: 2378
  def get_dashboard_id("registry-tear-sheet"), do: 2311
  def get_dashboard_id("retail-vs-non-retail"), do: 2575
  def get_dashboard_id("survey-responses"), do: 2443
  def get_dashboard_id("uk-shareholders"), do: 1321
  def get_dashboard_id("vwap-report"), do: 2641
  def get_dashboard_id("weekly-reporting"), do: 2544
  def get_dashboard_id(_), do: nil

  def get_params(custom_report, %ProfileUser{profile: %Profile{id: company_profile_id}})
      when custom_report in [
             "channel-performance",
             "day-traders",
             "email-campaign-performance",
             "hnw-overview",
             "retail-vs-non-retail",
             "vwap-report"
           ] do
    %{"id" => [company_profile_id]}
  end

  def get_params(custom_report, %ProfileUser{profile: %Profile{id: company_profile_id}})
      when custom_report in ["survey-responses", "weekly-reporting"] do
    %{"company_id" => "#{company_profile_id}"}
  end

  def get_params(custom_report, %ProfileUser{
        profile: %Profile{ticker: %Ticker{listing_key: listing_key, market_key: market_key}}
      })
      when custom_report in ["profitability-report"] do
    %{"ticker" => [String.upcase("#{market_key}:#{listing_key}")]}
  end

  def get_params(custom_report, %ProfileUser{
        profile: %Profile{id: company_profile_id, ticker: %Ticker{listing_key: listing_key, market_key: market_key}}
      })
      when custom_report in ["registry-tear-sheet"] do
    %{"company_id" => "#{company_profile_id}", "ticker" => [String.upcase("#{market_key}:#{listing_key}")]}
  end

  def get_params("uk-shareholders", %ProfileUser{profile: %Profile{id: company_profile_id}}) do
    %{"company" => [company_profile_id]}
  end

  def get_params(_, _) do
    nil
  end

  # Unsigned parameters can be edited by clients after loaded
  def get_unsigned_params("profitability-report", %ProfileUser{profile: %Profile{id: company_profile_id}}) do
    company_profile_id
    |> Gaia.Dashboard.get_break_even_price()
    |> case do
      nil ->
        # If somehow nil, use default filters
        ""

      price ->
        min_price = 0.0
        max_price = Helper.Number.round_to_significant_number(price, 2)
        # Create 10 increments for the range
        price_increment = Helper.Number.round_to_significant_number((max_price - min_price) / 10, 2)

        unsigned_params_string =
          URI.encode_query(
            %{"min_price_(aud)" => min_price, "max_price_(aud)" => max_price, "price_increment_(aud)" => price_increment},
            :rfc3986
          )

        "?#{unsigned_params_string}"
    end
  end

  def get_unsigned_params(_, _) do
    ""
  end
end
