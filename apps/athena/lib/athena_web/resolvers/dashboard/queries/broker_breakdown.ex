defmodule AthenaWeb.Resolvers.Dashboard.BrokerBreakdown do
  @moduledoc """
    BrokerBreakdown Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Brokers
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Registers

  def resolve(_parent, %{start_date: %Date{} = start_date, end_date: %Date{} = end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with %Date{} = latest_date <-
           Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id),
         calculated_end_date = Enum.min([end_date, latest_date], Date),
         [_ | _] = breakdown <-
           Registers.get_broker_breakdown(company_profile_id, calculated_end_date),
         [_ | _] = movements <-
           Registers.get_broker_movements(company_profile_id, start_date, calculated_end_date) do
      count_mapping = Enum.reduce(breakdown, %{}, &Map.put(&2, &1.broker_pid, &1.shareholders_count))

      shares_mapping = Enum.reduce(breakdown, %{}, &Map.put(&2, &1.broker_pid, &1.total_shares))

      movements_mapping = Enum.reduce(movements, %{}, &Map.put(&2, &1.broker_pid, &1.net_movements))

      # Gets the list of brokers that we have
      # Loop through each broker based on the pids
      # We should only returns the brokers that have data
      Brokers.list_brokers()
      |> Enum.reduce({[], count_mapping}, fn
        %{pids: pids} = broker, {brokers_acc, count_acc} ->
          pids
          |> Enum.reduce({0, 0, 0}, fn pid, {count, shares, net} ->
            {count + Map.get(count_acc, pid, 0), shares + Map.get(shares_mapping, pid, 0),
             net + Map.get(movements_mapping, pid, 0)}
          end)
          |> case do
            {0, 0, 0} ->
              # This broker is not returned because they do not have shareholdings
              {brokers_acc, count_acc}

            {total_count, total_shares, total_net} ->
              # This broker has shareholdings, add to the list
              # Remove the pids from the brokers_breakdown mapping
              broker_pids_joined = Enum.join(pids, "-")

              {
                [
                  %{
                    id: "#{start_date} #{end_date} #{broker_pids_joined}",
                    name: broker.name,
                    name_short: broker.name_short,
                    net_movements: total_net,
                    pids: pids,
                    shareholders_count: total_count,
                    total_shares: total_shares
                  }
                  | brokers_acc
                ],
                Map.drop(count_acc, pids)
              }
          end
      end)
      |> case do
        {brokers, leftover_count_mapping} when leftover_count_mapping == %{} ->
          # Empty map means that we have all of the broker information
          # Nothing more to do, just returns the brokers
          brokers

        {brokers, leftover_count_mapping} ->
          # We don't have the broker information for some of the pids
          # Put this leftover information into one unknown_broker and add it to list
          {pids, total_count, total_shares, total_net} =
            Enum.reduce(leftover_count_mapping, {[], 0, 0, 0}, fn {broker_pid, count},
                                                                  {pids_acc, count_acc, shares_acc, net_acc} ->
              {[broker_pid | pids_acc], count + count_acc, shares_acc + Map.get(movements_mapping, broker_pid, 0),
               net_acc + Map.get(movements_mapping, broker_pid, 0)}
            end)

          broker_pids_joined = Enum.join(pids, "-")

          # Sort the pids for consistency
          unknown_broker = %{
            id: "#{start_date} #{end_date} #{broker_pids_joined}",
            name: Brokers.unknown_broker().name,
            name_short: Brokers.unknown_broker().name_short,
            net_movements: total_net,
            pids: pids,
            shareholders_count: total_count,
            total_shares: total_shares
          }

          [unknown_broker | brokers]
      end
      |> Enum.sort_by(& &1.name)
      |> {:ok, __}
    else
      _ ->
        {:ok, []}
    end
  end

  def resolve(_parent, _args, _resolution) do
    {:ok, []}
  end
end
