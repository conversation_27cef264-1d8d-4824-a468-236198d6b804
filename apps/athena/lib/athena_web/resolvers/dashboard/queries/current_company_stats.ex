defmodule AthenaWeb.Resolvers.Dashboard.CurrentCompanyStats do
  @moduledoc """
    CurrentCompanyStats Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.RegistryDataStatus

  def get_current_company_stats(_parent, _args, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id} = company_profile}
        }
      }) do
    as_of_date =
      company_profile
      |> RegistryDataStatus.get_status()
      |> Map.get(:latest_report_date)

    result = %{
      id: "#{to_string(as_of_date)}#{Enum.random(0..1_000_000)}",
      as_of_date: as_of_date,
      company_profile_id: company_profile_id,
      geographical_breakdown_chart: get_geographical_breakdown_chart(as_of_date, company_profile_id),
      growth_ratio_past_ninety_days: get_growth_ratio_past_ninety_days(as_of_date, company_profile_id)
    }

    {:ok, result}
  end

  def get_current_company_stats(_parent, _args, _resolution) do
    {:ok, nil}
  end

  defp get_geographical_breakdown_chart(%Date{} = as_of_date, company_profile_id) do
    geographical_breakdown_data =
      company_profile_id
      |> Dashboard.get_shareholder_locations(as_of_date)
      |> Enum.map(
        &%{
          id: &1.id,
          eligible: &1.location != "Rest of the world",
          label: &1.location,
          value: &1.shareholders_count
        }
      )

    %{data: geographical_breakdown_data}
  end

  defp get_geographical_breakdown_chart(_, _) do
    %{data: []}
  end

  defp get_growth_ratio_past_ninety_days(%Date{}, company_profile_id) do
    with %Date{} = end_date <- Helper.ExDay.now_date(),
         %Date{} = start_date <- Timex.shift(end_date, days: -90),
         %{new: new, churned: churned, returning: returning} <-
           Dashboard.get_key_insights_data(company_profile_id, start_date, end_date),
         true <- new + returning > 0,
         true <- churned > 0 do
      (new + returning) / churned
    else
      _ ->
        0.0
    end
  end

  defp get_growth_ratio_past_ninety_days(_, _) do
    0.0
  end
end
