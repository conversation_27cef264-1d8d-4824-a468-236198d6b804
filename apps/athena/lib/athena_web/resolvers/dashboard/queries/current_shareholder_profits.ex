defmodule AthenaWeb.Resolvers.Dashboard.CurrentShareholderProfits do
  @moduledoc """
    CurrentShareholderProfits Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.Registers

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with %Date{} = date <-
           Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id),
         %{shareholders_count: shareholders_in_loss, total_losses: total_unrealised_loss} <-
           Dashboard.get_shareholders_loss_breakdown(company_profile_id),
         %{shareholders_count: shareholders_in_profit, total_profits: total_unrealised_gain} <-
           Dashboard.get_shareholders_profit_breakdown(company_profile_id) do
      {:ok,
       %{
         id: to_string(date),
         average_shareholders_profit: Dashboard.get_average_profit(company_profile_id),
         date: date,
         shareholder_profits: Dashboard.get_shareholder_profits(company_profile_id, date),
         shareholders_in_loss: shareholders_in_loss,
         shareholders_in_profit: shareholders_in_profit,
         total_unrealised_gain: total_unrealised_gain,
         total_unrealised_loss: total_unrealised_loss
       }}
    else
      _ ->
        {:ok, nil}
    end
  end
end
