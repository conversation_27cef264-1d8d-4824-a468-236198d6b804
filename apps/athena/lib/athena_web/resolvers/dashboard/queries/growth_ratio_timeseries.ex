defmodule AthenaWeb.Resolvers.Dashboard.GrowthRatioTimeseries do
  @moduledoc """
    GrowthRatioTimeseries Query Resolvers
  """

  use Helper.Pipe

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard

  def resolve(_parent, %{start_date: %Date{} = start_date, end_date: %Date{} = end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    company_profile_id
    |> Dashboard.get_growth_ratio_timeseries(start_date, end_date)
    |> {:ok, __}
  end

  def resolve(_parent, _args, _resolution) do
    {:ok, []}
  end
end
