defmodule AthenaWeb.Resolvers.Dashboard.SendStartPlanningSppEmail do
  @moduledoc false
  use Gettext, backend: AthenaWeb.Gettext

  def send_start_planning_spp_email(_, %{send_start_planning_spp_email_input: enquiry}, %{
        context: %{
          current_company_profile_user:
            %Gaia.Companies.ProfileUser{
              profile: %Gaia.Companies.Profile{id: company_profile_id},
              user: %Gaia.Companies.User{} = user
            } = profile_user
        }
      }) do
    with {:ok, _} <-
           Gaia.Notifications.Email.deliver(
             EmailTransactional.Operations,
             :spp_contact,
             [profile_user, enquiry],
             company_profile_id
           ),
         {:ok, _} <-
           Gaia.Notifications.Email.deliver(EmailTransactional.Company, :spp_contact_receipt, [user], company_profile_id) do
      {:ok, true}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             gettext(
               "Oops, looks like something went wrong. Please fill in the form again and press submit. If the issue persists, contact InvestorHub."
             )
         }}
    end
  end
end
