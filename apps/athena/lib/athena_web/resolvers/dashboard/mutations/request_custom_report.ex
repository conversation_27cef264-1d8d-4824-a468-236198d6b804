defmodule AthenaWeb.Resolvers.Dashboard.RequestCustomReport do
  @moduledoc """
  Clients requesting a custom report
  """

  def resolve(_, %{message: message}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{
              ticker: %Gaia.Markets.Ticker{listing_key: listing_key, market_key: market_key}
            },
            user: %Gaia.Companies.User{email: email}
          }
        }
      }) do
    runtime_env = :helper |> Application.get_env(:runtime_env) |> String.upcase()
    ticker = String.upcase("#{market_key}:#{listing_key}")

    %{message: "[#{runtime_env}] #{ticker} - #{email} has requested a custom report:\n#{message}"}
    |> Slack.WorkflowWebHook.send(:request_custom_report_url)
    |> case do
      :ok ->
        {:ok, true}

      other_response ->
        {:error, %Helper.AbsintheError{error: other_response, message: "Oops, something went wrong"}}
    end
  end
end
