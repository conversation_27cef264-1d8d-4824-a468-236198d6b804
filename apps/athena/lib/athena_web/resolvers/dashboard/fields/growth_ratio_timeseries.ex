defmodule AthenaWeb.Resolvers.Dashboard.GrowthRatioTimeseriesFields do
  @moduledoc """
  Resolvers for object growth_ratio_timeseries
  """

  # Return the id if exists
  # Generate id from date if id not exists
  def id(%{id: id}, _, _) when not is_nil(id) do
    {:ok, id}
  end

  def id(%{date: %Date{} = date}, _, _) do
    {:ok, Timex.format!(date, "{YYYY}{0M}{0D}")}
  end

  def growth_ratio(%{new: new, returning: returning, churned: churned}, _, _)
      when is_number(new) and is_number(returning) and is_number(churned) and new + returning > 0 and churned > 0 do
    {:ok, (new + returning) / churned}
  end

  def growth_ratio(_, _, _) do
    {:ok, nil}
  end
end
