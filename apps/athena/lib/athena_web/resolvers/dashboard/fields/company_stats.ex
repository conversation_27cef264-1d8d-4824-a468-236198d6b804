defmodule AthenaWeb.Resolvers.Dashboard.CompanyStatsFields do
  @moduledoc """
  Resolvers for object company_stats
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Dashboard
  alias Gaia.ScenarioModelling

  def average_holding_time(%{as_of_date: %Date{} = as_of_date, company_profile_id: company_profile_id}, _, _) do
    case Dashboard.get_holding_timeseries(company_profile_id, as_of_date, as_of_date) do
      [%{average_holding_time: average_holding_time}] ->
        {:ok, average_holding_time}

      _ ->
        {:ok, 0.0}
    end
  end

  def average_holding_time(_, _, _) do
    {:ok, 0.0}
  end

  def profit_loss(%{as_of_date: %Date{} = as_of_date, company_profile_id: company_profile_id}, _, _) do
    result = %{
      average_shareholder_profit_loss: Dashboard.get_average_profit(company_profile_id),
      shareholders_in_profit: Dashboard.get_shareholders_in_profit(company_profile_id, as_of_date)
    }

    {:ok, result}
  end

  def profit_loss(_, _, _) do
    {:ok, %{average_shareholder_profit_loss: 0.0, shareholders_in_profit: 0.0}}
  end

  def raising_potential(%{as_of_date: %Date{}}, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}
      }) do
    buckets_breakdown = Dashboard.get_spp_buckets_breakdown(profile)

    # Base prediction
    base_high = Dashboard.predict_spp_raise_amount(buckets_breakdown, :upper)
    base_low = Dashboard.predict_spp_raise_amount(buckets_breakdown, :lower)

    %{
      predicted_spp_average_uptake: base_average_uptake,
      predicted_spp_participation_rate: base_shareholder_participation
    } = Dashboard.predict_spp_shareholders_participation_and_uptake(buckets_breakdown)

    result = %{
      average_uptake: base_average_uptake,
      high: base_high,
      low: base_low,
      mean: (base_high + base_low) / 2,
      shareholder_participation: base_shareholder_participation,
      scenario_range: %{
        all_bad: raising_potential_scenario(buckets_breakdown, :all_bad),
        all_good: raising_potential_scenario(buckets_breakdown, :all_good),
        rocky_finish: raising_potential_scenario(buckets_breakdown, :rocky_finish)
      }
    }

    {:ok, result}
  end

  def raising_potential(_, _, _) do
    default_value = %{
      average_uptake: 0.0,
      high: 0.0,
      low: 0.0,
      mean: 0.0,
      shareholder_participation: 0.0
    }

    result =
      Enum.into(
        %{scenario_range: %{all_bad: default_value, all_good: default_value, rocky_finish: default_value}},
        default_value
      )

    {:ok, result}
  end

  def raising_potential_scenario(buckets_breakdown, scenario) do
    high = ScenarioModelling.predict_spp_amount(buckets_breakdown, scenario, :upper)
    low = ScenarioModelling.predict_spp_amount(buckets_breakdown, scenario, :lower)

    %{
      predicted_spp_average_uptake: average_uptake,
      predicted_spp_participation_rate: shareholder_participation
    } = ScenarioModelling.predict_spp_shareholders_participation_and_uptake(buckets_breakdown, scenario)

    %{
      average_uptake: average_uptake,
      high: high,
      low: low,
      mean: (high + low) / 2,
      shareholder_participation: shareholder_participation
    }
  end

  def shareholder_insights(
        %{
          as_of_date: %Date{} = as_of_date,
          company_profile_id: company_profile_id,
          geographical_breakdown_chart: %{data: [_ | _] = geographical_breakdown_chart_data}
        },
        _,
        _
      ) do
    %{
      emails_count: emails_count,
      phones_count: phones_count,
      addresses_count: addresses_count,
      po_boxes_count: po_boxes_count
    } = Dashboard.get_reachability(company_profile_id, as_of_date)

    total_shareholders = addresses_count + po_boxes_count

    eligible_count =
      Enum.reduce(geographical_breakdown_chart_data, 0, fn
        %{eligible: true, value: value}, acc -> acc + value
        _, acc -> acc
      end)

    reachability =
      (emails_count * 0.5 + phones_count * 0.3 + addresses_count * 0.2 + po_boxes_count * 0.1) /
        total_shareholders

    result = %{
      addresses: addresses_count,
      eligibility: eligible_count / total_shareholders,
      email: emails_count,
      mobile: phones_count,
      reachability: reachability,
      total: total_shareholders
    }

    {:ok, result}
  end

  def shareholder_insights(_, _, _) do
    result = %{
      addresses: 0,
      eligibility: 0.0,
      email: 0,
      mobile: 0,
      reachability: 0.0,
      total: 0
    }

    {:ok, result}
  end

  # The SPP estimation could result a negative value, we should return it as 0
  def low(%{low: low}, _, _) when is_number(low) and low > 0.0, do: {:ok, low}
  def low(_, _, _), do: {:ok, 0.0}
end
