defmodule AthenaWeb.Resolvers.Dashboard.TradingActivityFields do
  @moduledoc """
  Resolvers for trading activity fields
  """

  alias Gaia.Registers

  def shareholding(%{shareholding_id: shareholding_id}, _, _) do
    Absinthe.Resolution.Helpers.batch(
      {Registers, :batch_get_shareholdings},
      shareholding_id,
      fn batch_results ->
        {:ok, Enum.find(batch_results, &(&1.id == shareholding_id))}
      end
    )
  end

  def shareholding(_, _, _), do: {:ok, nil}
end
