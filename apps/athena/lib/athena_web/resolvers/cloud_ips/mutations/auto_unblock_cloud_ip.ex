defmodule AthenaWeb.Resolvers.CloudIps.AutoUnblockCloudIp do
  @moduledoc """
  AutoUnblockCloudIp Mutation Resolvers
  """

  use Helper.Pipe

  alias <PERSON>aia.CloudIps
  alias Gaia.Companies
  alias Gaia.Companies.Profile

  def resolve(_, %{email: email, ticker: ticker, market_key: market_key}, %{context: %{cloud_ip: cloud_ip}}) do
    ticker
    |> Companies.get_profile_by_listing_and_market_key(
      market_key
      |> String.downcase()
      |> String.to_atom()
    )
    |> case do
      %Profile{id: company_profile_id} ->
        ip_address = cloud_ip |> Tuple.to_list() |> Enum.join(".")

        CloudIps.company_auto_unblock_cloud_ip(ip_address, email, company_profile_id)

        {:ok, true}

      nil ->
        {:ok, false}
    end
  end
end
