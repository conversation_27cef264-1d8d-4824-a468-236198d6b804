defmodule AthenaWeb.Resolvers.Tracking.DeleteUtmLink do
  @moduledoc """
  DeleteUtmLink mutation resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking
  alias Tracking.UtmLink

  @gettext_context "DeleteUtmLink mutation resolver"

  def resolve(_, %{id: id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %UtmLink{company_profile_id: company_profile_id} = existing_utm_link <-
           Tracking.get_utm_link_by(%{id: id}),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %UtmLink{} = _deleted_utm_link} <- Tracking.delete_utm_link(existing_utm_link) do
      {:ok, true}
    else
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Utm link does not exist."
         )}

      false ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are unauthorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Oops! Something went wrong."
             )
         }}
    end
  end
end
