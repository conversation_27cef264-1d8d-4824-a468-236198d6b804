defmodule AthenaWeb.Resolvers.Tracking.CreateUtmLink do
  @moduledoc """
  CreateUtmLink mutation resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking
  alias Tracking.UtmLink

  @gettext_context "CreateUtmLink mutation resolver"

  def resolve(_, %{utm_link: attrs}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    with %{} = attrs <- Map.put(attrs, :company_profile_id, company_profile_id),
         {:ok, %UtmLink{}} = created <- Tracking.create_utm_link(attrs) do
      created
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not create Utm link"
             )
         }}
    end
  end
end
