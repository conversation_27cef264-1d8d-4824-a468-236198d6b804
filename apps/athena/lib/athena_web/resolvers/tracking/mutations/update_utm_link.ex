defmodule AthenaWeb.Resolvers.Tracking.UpdateUtmLink do
  @moduledoc """
  UpdateUtmLink mutation resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking
  alias Tracking.UtmLink

  @gettext_context "UpdateUtmLink mutation resolver"

  def resolve(_, %{id: utm_link_id, updated_utm_attrs: utm_link_input}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %UtmLink{company_profile_id: company_profile_id} = utm_link <-
           Tracking.get_utm_link_by(%{id: utm_link_id}),
         true <- company_profile_id == current_company_profile_id,
         {:ok, %UtmLink{} = updated_utm_link} <-
           Tracking.update_utm_link(utm_link, utm_link_input) do
      {:ok, updated_utm_link}
    else
      nil ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Utm link does not exist."
         )}

      false ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "You are not authorised."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Oops! Something went wrong."
             )
         }}
    end
  end
end
