defmodule AthenaWeb.Resolvers.Tracking.SendHubspotCustomEvent do
  @moduledoc """
  Sending custom event to Hubspot.

  The custom event will be used by CSM to check if clients are using the platform
  So it's important to only send custom event that are completed by clients themselves
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User

  require Helper.Error.Custom.ErrorHandler

  # Ignore if simulating
  def resolve(_, _, %{context: %{simulating_admin_user_id: simulating_admin_user_id}})
      when not is_nil(simulating_admin_user_id) do
    {:ok, false}
  end

  def resolve(_, %{custom_event: custom_event}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{hubspot_company_id: hubspot_company_id},
            user: %User{email: email}
          }
        }
      })
      when not is_nil(hubspot_company_id) do
    case Gaia.Hubspot.send_custom_event(hubspot_company_id, custom_event, email) do
      {:ok, _} ->
        {:ok, true}

      {:skip, nil} ->
        {:ok, false}

      {:error, error} ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error sending custom event to Hubspot",
          %{
            company_profile_user_id: company_profile_user_id,
            custom_event: custom_event
          },
          error
        )

        {:ok, false}
    end
  end

  def resolve(_, _, _), do: {:ok, false}
end
