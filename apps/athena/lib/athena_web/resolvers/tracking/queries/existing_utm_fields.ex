defmodule AthenaWeb.Resolvers.Tracking.ExistingUtmFields do
  @moduledoc """
  Fetch the existing utm_campaigns, utm_mediums and utm_sources for a company
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}}) do
    utm_mediums_and_sources = Tracking.get_existing_utm_medium_and_utm_sources_by_company_profile_id(company_profile_id)
    utm_campaigns = Tracking.get_existing_utm_campaign_name_by_company_profile_id(company_profile_id)

    {:ok, %{utm_campaigns: utm_campaigns, utm_mediums_and_sources: utm_mediums_and_sources}}
  end
end
