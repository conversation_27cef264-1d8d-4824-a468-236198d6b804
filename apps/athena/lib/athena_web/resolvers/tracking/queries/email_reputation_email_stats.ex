defmodule AthenaWeb.Resolvers.Tracking.EmailReputationEmailStats do
  @moduledoc """
  EmailStats query resolver
  """
  use Gettext, backend: AthenaWeb.Gettext

  import Ecto.Query, warn: false

  alias Gaia.Comms
  alias Gaia.Comms.Helper.EmailMergeTags
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo

  def resolve(_, %{type: "latest"}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    start_date = get_start_date(current_company_profile_id)

    {:ok, get_latest_email_reputation_stats(current_company_profile_id, start_date)}
  end

  def resolve(_, %{type: "worst"}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok, get_worst_email_reputation_stats(current_company_profile_id)}
  end

  # Fallback clause for when the context doesn't contain the expected structure
  def resolve(_, _, _) do
    {:error, "Unauthorized or missing company profile context"}
  end

  def get_latest_email_reputation_stats(company_profile_id, start_date) do
    Gaia.Comms.Email
    |> where(
      [e],
      not is_nil(e.sent_at) and e.company_profile_id == ^company_profile_id
    )
    |> where([e], e.sent_at >= ^start_date)
    |> order_by(desc: :sent_at)
    |> limit(25)
    |> Repo.all()
    |> Repo.preload(
      [
        :email_unsubscribed_contacts,
        :recipients_tracking_events,
        :email_globally_unsubscribed_contacts,
        email_recipients: [:tracking_email],
        media: [:media_announcement, :media_update]
      ],
      with_invalidated: true
    )
    |> Enum.map(&format_email_reputation_stats(&1))
    |> Enum.filter(&(&1.sends > 0))
  end

  def get_worst_email_reputation_stats(company_profile_id) do
    Gaia.Comms.Email
    |> where(
      [e],
      not is_nil(e.sent_at) and e.company_profile_id == ^company_profile_id
    )
    |> order_by(desc: :sent_at)
    |> Repo.all()
    |> Repo.preload(
      [
        :email_unsubscribed_contacts,
        :recipients_tracking_events,
        :email_globally_unsubscribed_contacts,
        email_recipients: [:tracking_email],
        media: [:media_announcement, :media_update]
      ],
      with_invalidated: true
    )
    |> Enum.map(&format_email_reputation_stats(&1))
    |> Enum.filter(&(&1.sends > 0))
    # A complaint is 50 times worse thatn a bounce according to SES
    |> Enum.sort_by(&(&1.complaint_rate * 50 + &1.bounce_rate), :desc)
    |> Enum.take(25)
  end

  defp count_unique_events(events, event_type) do
    events
    |> Enum.filter(&(&1.event_type == String.to_existing_atom(event_type)))
    |> Enum.map(& &1.email_id)
    |> Enum.uniq()
    |> length()
  end

  defp calculate_rate(events, event_type, total_sends) do
    count = count_unique_events(events, event_type)
    if total_sends > 0, do: count / total_sends, else: 0
  end

  defp get_start_date(company_profile_id) do
    email_count =
      Gaia.Tracking.Email
      |> where([e], e.company_profile_id == ^company_profile_id)
      |> select([e], count(e.id))
      |> Repo.one()

    if email_count >= 10_000 do
      Gaia.Tracking.Email
      |> where([e], e.company_profile_id == ^company_profile_id)
      |> order_by(desc: :inserted_at)
      |> offset(9999)
      |> limit(1)
      |> select([e], e.inserted_at)
      |> Repo.one()
    else
      Gaia.Tracking.Email
      |> where([e], e.company_profile_id == ^company_profile_id)
      |> order_by(asc: :inserted_at)
      |> limit(1)
      |> select([e], e.inserted_at)
      |> Repo.one()
      |> case do
        nil -> DateTime.utc_now()
        oldest_email_date -> oldest_email_date
      end
    end
  end

  defp format_email_reputation_stats(%Gaia.Comms.Email{} = email) do
    email_method = Comms.get_email_distribution_method(email)
    email_type = Comms.get_email_type(email)

    preloaded_email = Repo.preload(email, media: [:media_announcement, :media_update])

    merge_tags =
      preloaded_email
      |> Map.get(:media)
      |> Comms.build_subject_merge_tags_for_email_media()

    %{
      identifier: email.id,
      type: email_type,
      method: email_method,
      subject:
        EmailMergeTags.replace_merge_tag(
          struct(%EmailMergeTags{}, merge_tags),
          email.subject
          |> String.replace(
            ~r/{{[\n| ]*announcement_title[\n| ]*}}/,
            "{{ interactive_media_title }}"
          )
          |> String.replace(~r/{{[\n| ]*update_title[\n| ]*}}/, "{{ interactive_media_title }}")
        ),
      sends: count_unique_events(email.recipients_tracking_events, "Send"),
      opens: count_unique_events(email.recipients_tracking_events, "Open"),
      clicks: count_unique_events(email.recipients_tracking_events, "Click"),
      bounces: count_unique_events(email.recipients_tracking_events, "Bounce"),
      complaints: count_unique_events(email.recipients_tracking_events, "Complaint"),
      unsubscribes:
        length(Enum.uniq_by(email.email_unsubscribed_contacts, & &1.contact_id)) +
          length(email.email_globally_unsubscribed_contacts),
      inserted_at: email.inserted_at,
      sent_at: email.sent_at,
      complaint_rate:
        calculate_rate(
          email.recipients_tracking_events,
          "Complaint",
          length(email.email_recipients)
        ),
      bounce_rate: calculate_rate(email.recipients_tracking_events, "Bounce", length(email.email_recipients))
    }
  end
end
