defmodule AthenaWeb.Resolvers.Tracking.EmailReputationOverTime do
  @moduledoc """
  EmailReputation query resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking.EmailReputations

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    company_profile = Companies.get_profile!(current_company_profile_id)

    end_date = Date.utc_today()

    email_reputation_over_time =
      0..11
      |> Enum.map(fn weeks_ago ->
        date = Date.add(end_date, -weeks_ago * 7)

        company_profile
        |> EmailReputations.get_email_reputation_data_for_week(date)
        |> Map.put(:date, format_date(date))
      end)
      |> Enum.reverse()

    {:ok, email_reputation_over_time}
  end

  # Fallback clause for when the context doesn't contain the expected structure
  def resolve(_, _, _) do
    {:error, "Unauthorized or missing company profile context"}
  end

  defp format_date(date) do
    Calendar.strftime(date, "%b %d, %Y")
  end
end
