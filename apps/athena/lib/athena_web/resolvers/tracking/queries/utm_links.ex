defmodule AthenaWeb.Resolvers.Tracking.UtmLinks do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Tracking

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([
        %{key: "company_profile_id", value: current_company_profile_id}
      ])

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    case options
         |> Tracking.utm_links_query()
         |> Connection.from_query(&Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
