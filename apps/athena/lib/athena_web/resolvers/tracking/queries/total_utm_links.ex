defmodule AthenaWeb.Resolvers.Tracking.TotalUtmLinks do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Tracking

  def resolve(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))
      |> Kernel.++([%{key: "company_profile_id", value: current_company_profile_id}])

    {:ok,
     args
     |> Map.get(:options, %{})
     |> Map.put(:filters, filters)
     |> Tracking.utm_links_query()
     |> Repo.aggregate(:count)}
  end
end
