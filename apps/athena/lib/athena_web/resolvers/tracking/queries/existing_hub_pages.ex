defmodule AthenaWeb.Resolvers.Tracking.ExistingHubPages do
  @moduledoc false

  use Helper.Pipe

  alias Absinthe.Relay.Connection
  alias Gaia.Repo
  alias Gaia.Tracking

  def resolve(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    case options
         |> Tracking.existing_hub_pages_for_company_query(company_profile_id)
         |> Connection.from_query(&Repo.all/1, args) do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           options: options,
           page_info: connection.page_info
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end

  def total(_, args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters =
      args
      |> Map.get(:options, %{})
      |> Map.get(:filters, [])
      |> Enum.filter(&(&1.key != "company_profile_id"))

    options =
      args
      |> Map.get(:options, %{})
      |> Map.put(:filters, filters)

    {:ok,
     options
     |> Tracking.existing_hub_pages_for_company_query(company_profile_id)
     |> Repo.aggregate(:count)}
  end
end
