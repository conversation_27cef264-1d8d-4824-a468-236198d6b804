defmodule AthenaWeb.Resolvers.Tracking.UtmLinkAnalyticsSignups do
  @moduledoc false

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Tracking

  def resolve(_, %{utm_link_id: utm_link_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    %{value: start_date_filter, key: _} = get_start_date(args)

    %{value: end_date_filter, key: _} = get_end_date(args)

    filters = update_filters_company_profile_id(args)

    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)

    with %Tracking.UtmLink{company_profile_id: company_profile_id} <-
           Tracking.get_utm_link_by(%{id: utm_link_id}),
         {:check_owner, true} <-
           {:check_owner, current_company_profile_id == company_profile_id},
         {:ok, connection} <-
           utm_link_id
           |> Tracking.utm_link_sign_ups(company_profile_id, start_date_filter, end_date_filter, options)
           |> Connection.from_query(&Repo.all/1, args) do
      {:ok,
       %{
         utm_link_id: utm_link_id,
         edges: connection.edges,
         page_info: connection.page_info,
         options: options
       }}
    else
      error -> {:error, %Helper.AbsintheError{error: error, message: "Oops! Something went wrong."}}
    end
  end

  def total(_, %{utm_link_id: utm_link_id} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    %{value: start_date_filter, key: _} = get_start_date(args)

    %{value: end_date_filter, key: _} = get_end_date(args)

    filters = update_filters_company_profile_id(args)

    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)

    with %Tracking.UtmLink{company_profile_id: company_profile_id} <-
           Tracking.get_utm_link_by(%{id: utm_link_id}),
         {:check_owner, true} <-
           {:check_owner, current_company_profile_id == company_profile_id} do
      {:ok,
       utm_link_id
       |> Tracking.utm_link_sign_ups(company_profile_id, start_date_filter, end_date_filter, options)
       |> Repo.aggregate(:count)}
    else
      error -> {:error, %Helper.AbsintheError{error: error, message: "Oops! Something went wrong."}}
    end
  end

  defp get_start_date(args) do
    args
    |> Map.get(:options, %{})
    |> Map.get(:filters, [])
    |> Enum.find(%{value: Timex.to_datetime(~N[1900-01-01 00:00:00]), key: "start_date"}, &(&1.key == "start_date"))
  end

  defp get_end_date(args) do
    args
    |> Map.get(:options, %{})
    |> Map.get(:filters, [])
    |> Enum.find(%{value: DateTime.utc_now(), key: "end_date"}, &(&1.key == "end_date"))
  end

  defp update_filters_company_profile_id(args) do
    args
    |> Map.get(:options, %{})
    |> Map.get(:filters, [])
    |> Enum.filter(&(&1.key != "company_profile_id"))
  end
end
