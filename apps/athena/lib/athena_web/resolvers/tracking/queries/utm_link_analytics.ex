defmodule AthenaWeb.Resolvers.Tracking.UtmLinkAnalytics do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking

  def resolve(_, %{id: id, start_date: start_date, end_date: end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Tracking.UtmLink{company_profile_id: company_profile_id} <- Tracking.get_utm_link_by(%{id: id}),
         {:check_owner, true} <- {:check_owner, current_company_profile_id == company_profile_id},
         duration when is_integer(duration) <- Timex.diff(start_date, end_date, :days),
         last_period_start_date = Timex.shift(start_date, days: duration),
         last_period_end_date = start_date,
         [%{}] = [curr_period_analytics] <-
           Tracking.utm_link_total_hits_and_total_sign_ups(%{
             id: id,
             company_profile_id: current_company_profile_id,
             start_date: start_date,
             end_date: end_date
           }),
         [%{}] = [prev_period_analytics] <-
           Tracking.utm_link_total_hits_and_total_sign_ups(%{
             id: id,
             company_profile_id: current_company_profile_id,
             start_date: last_period_start_date,
             end_date: last_period_end_date
           }) do
      {:ok,
       %{
         curr_period_analytics: curr_period_analytics,
         prev_period_analytics: prev_period_analytics
       }}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
