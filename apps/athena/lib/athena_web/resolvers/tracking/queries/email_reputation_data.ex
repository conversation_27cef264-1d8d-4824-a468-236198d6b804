defmodule AthenaWeb.Resolvers.Tracking.EmailReputationData do
  @moduledoc """
  EmailReputation query resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.ProfileUser
  alias Gaia.Tracking.EmailReputations

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}}) do
    company_profile = Companies.get_profile(current_company_profile_id)
    email_reputation_data = EmailReputations.get_email_reputation_data(company_profile)
    {:ok, email_reputation_data}
  end

  # Fallback clause for when the context doesn't contain the expected structure
  def resolve(_, _, _) do
    {:error, "Unauthorized or missing company profile context"}
  end
end
