defmodule AthenaWeb.Resolvers.Websites.CreateBlock do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites
  alias Gaia.Websites.Block
  alias Gaia.Websites.Page

  def resolve(_, %{page_id: page_id, type: type, position: position, content: content}, %{
        context: %{current_company_profile_user: %ProfileUser{user: current_user} = profile_user}
      }) do
    with %Page{} = page <- Websites.get_page(page_id),
         :ok <- has_permission?(page, profile_user),
         {:ok, %Block{} = block} <-
           Websites.create_block(page, current_user, %{type: type, position: position, content: content}) do
      {:ok, block}
    else
      nil ->
        {:error, "Page not found"}

      :unauthorized ->
        {:error, "User is not allowed to edit this page"}

      {:error, _changeset} ->
        {
          :error,
          "Failed to create block"
        }
    end
  end

  defp has_permission?(%Page{} = page, %ProfileUser{} = profile_user) do
    page = Repo.preload(page, :website)

    if page.website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
