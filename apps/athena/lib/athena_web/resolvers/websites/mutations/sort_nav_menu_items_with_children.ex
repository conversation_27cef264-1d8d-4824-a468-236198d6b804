defmodule AthenaWeb.Resolvers.Websites.SortNavMenuItemsWithChildren do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_, %{nav_menu_items: nav_menu_items}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)

    if has_permission?(nav_menu_items, website) do
      sort_nav_menu_items(nav_menu_items)
    else
      {:error, "You do not have permission to sort navigation menu items"}
    end
  end

  defp sort_nav_menu_items(nav_menu_items) do
    case Websites.sort_nav_menu_items_with_children(nav_menu_items) do
      {:ok, _} ->
        {:ok, true}

      {:error, _reason} ->
        {:error, "Failed to sort navigation menu items"}
    end
  end

  defp has_permission?(nav_menu_items, website) do
    nav_menu_items
    |> Enum.map(fn nav_menu_item -> nav_menu_item.id end)
    |> Websites.get_nav_menu_items_by_ids()
    |> Enum.all?(fn nav_menu_item -> nav_menu_item.website_id == website.id end)
  end
end
