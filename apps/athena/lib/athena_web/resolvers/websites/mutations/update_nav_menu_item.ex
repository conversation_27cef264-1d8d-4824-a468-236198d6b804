defmodule AthenaWeb.Resolvers.Websites.UpdateNavMenuItem do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites
  alias Gaia.Websites.NavMenuItem

  def resolve(_, %{id: id} = args, %{context: %{current_company_profile_user: %ProfileUser{} = profile_user}}) do
    with %NavMenuItem{} = nav_menu_item <- Websites.get_nav_menu_item(id),
         :ok <- check_permission(nav_menu_item, profile_user) do
      Websites.update_nav_menu_item(nav_menu_item, Map.delete(args, :id))
    else
      nil ->
        {:error, "Navigation menu item not found"}

      :unauthorized ->
        {:error, "User is not authorized to update this navigation menu item"}
    end
  end

  defp check_permission(nav_menu_item, profile_user) do
    website = Websites.get_website!(nav_menu_item.website_id)

    if website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
