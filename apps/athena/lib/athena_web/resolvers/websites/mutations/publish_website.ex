defmodule AthenaWeb.Resolvers.Websites.PublishWebsite do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites

  require Helper.Error.Custom.ErrorHandler

  def resolve(_, _, %{
        context: %{
          current_company_profile_user: %ProfileUser{user: current_user, profile: %Profile{id: company_profile_id}}
        }
      }) do
    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)
    website = Repo.preload(website, pages: :blocks, nav_menu_items: [])

    case Websites.publish_website(website, current_user) do
      {:ok, _website} ->
        {:ok, true}

      {:error, failed_operation} ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Failed to publish website",
          %{
            company_profile_id: company_profile_id,
            current_user_id: current_user.id
          },
          failed_operation
        )

        {:error, "Oops! Something went wrong"}
    end
  end
end
