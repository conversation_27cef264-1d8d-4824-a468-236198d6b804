defmodule AthenaWeb.Resolvers.Websites.UpdateWebsite do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites
  alias Gaia.Websites.Website

  require Helper.Error.Custom.ErrorHandler

  def resolve(_, params, %{context: %{current_company_profile_user: %ProfileUser{profile_id: profile_id} = profile_user}}) do
    website = Websites.get_or_create_website_by_company_profile_id(profile_id)

    with :ok <- has_permission?(website, profile_user),
         {:ok, website} <- Websites.update_website_as_company_user(website, params) do
      {:ok, website}
    else
      :unauthorized ->
        {:error, "User is not allowed to update this website"}

      {:error, failed_operation} ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Failed to update website",
          params,
          failed_operation
        )

        {:error, "Oops! Something went wrong"}
    end
  end

  defp has_permission?(%Website{company_profile_id: company_profile_id}, %ProfileUser{profile_id: profile_id}) do
    if company_profile_id == profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
