defmodule AthenaWeb.Resolvers.Websites.SortNavMenuItems do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_, %{nav_menu_item_ids: nav_menu_item_ids} = args, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: company_profile_id}}
      }) do
    parent_id = args[:parent_id]
    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)
    nav_menu_items = Websites.get_nav_menu_items_by_ids(nav_menu_item_ids)

    if has_permission?(nav_menu_items, website, parent_id) do
      sort_nav_menu_items(nav_menu_item_ids)
    else
      {:error, "You do not have permission to sort navigation menu items"}
    end
  end

  defp sort_nav_menu_items(nav_menu_item_ids) do
    case Websites.sort_nav_menu_items(nav_menu_item_ids) do
      {:ok, _} ->
        {:ok, true}

      {:error, _reason} ->
        {:error, "Failed to sort navigation menu items"}
    end
  end

  defp has_permission?(nav_menu_items, website, nil) do
    Enum.all?(nav_menu_items, fn nav_menu_item ->
      nav_menu_item.website_id == website.id
    end)
  end

  defp has_permission?(nav_menu_items, website, parent_id) do
    Enum.all?(nav_menu_items, fn nav_menu_item ->
      nav_menu_item.website_id == website.id && nav_menu_item.parent_id == parent_id
    end)
  end
end
