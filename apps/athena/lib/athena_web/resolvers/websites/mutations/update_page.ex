defmodule AthenaWeb.Resolvers.Websites.UpdatePage do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites
  alias Gaia.Websites.Page

  def resolve(_, %{id: page_id, title: title, slug: slug} = args, %{
        context: %{current_company_profile_user: %ProfileUser{user: current_user} = profile_user}
      }) do
    attrs = %{
      title: title,
      slug: slug,
      last_edited_by_company_user_id: current_user.id,
      meta_description: Map.get(args, :meta_description),
      social_image_cloudinary_public_id: Map.get(args, :social_image_cloudinary_public_id)
    }

    with %Page{} = page <- Websites.get_page(page_id),
         :ok <- has_permission?(page, profile_user),
         {:ok, page} <- Websites.update_page(page, attrs) do
      {:ok, page}
    else
      nil ->
        {:error, "Page not found"}

      :unauthorized ->
        {:error, "User is not allowed to edit this page"}

      {:error,
       %Ecto.Changeset{
         errors: [website_id: {_, [constraint: :unique, constraint_name: "websites_pages_website_id_slug_index"]}]
       }} ->
        {:error, "This slug is already in use for this website"}
    end
  end

  defp has_permission?(%Page{} = page, %ProfileUser{} = profile_user) do
    page = Repo.preload(page, :website)

    if page.website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
