defmodule AthenaWeb.Resolvers.Websites.SortBlocks do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites

  def resolve(_, %{page_id: page_id, block_ids: block_ids}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: user_profile_id}}
      }) do
    page = page_id |> Websites.get_page() |> Repo.preload(:website)

    if page && page.website.company_profile_id == user_profile_id do
      case Websites.sort_blocks(page_id, block_ids) do
        {:ok, _} -> {:ok, true}
        {:error, _reason} -> {:error, "Failed to sort blocks"}
      end
    else
      {:error, "User is not allowed to edit this page"}
    end
  end
end
