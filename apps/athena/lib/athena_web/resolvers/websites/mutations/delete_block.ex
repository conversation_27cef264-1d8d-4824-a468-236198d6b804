defmodule AthenaWeb.Resolvers.Websites.DeleteBlock do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites
  alias Gaia.Websites.Block

  def resolve(_, %{id: block_id}, %{context: %{current_company_profile_user: %ProfileUser{} = profile_user}}) do
    with %Block{} = block <- Websites.get_block(block_id),
         :ok <- has_permission?(block, profile_user),
         {:ok, block} <- Websites.delete_block(block) do
      {:ok, block}
    else
      nil ->
        {:error, "Block not found"}

      :unauthorized ->
        {:error, "User is not allowed to edit this block"}

      {:error, _changeset} ->
        {
          :error,
          "Failed to delete block"
        }
    end
  end

  defp has_permission?(%Block{} = block, %ProfileUser{} = profile_user) do
    block = Repo.preload(block, page: :website)

    if block.page.website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
