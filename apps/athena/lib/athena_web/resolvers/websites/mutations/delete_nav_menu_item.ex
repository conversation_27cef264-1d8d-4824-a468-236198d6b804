defmodule AthenaWeb.Resolvers.Websites.DeleteNavMenuItem do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites
  alias Gaia.Websites.NavMenuItem

  def resolve(_, %{id: id}, %{context: %{current_company_profile_user: %ProfileUser{profile_id: user_profile_id}}}) do
    case Websites.get_nav_menu_item!(id) do
      nil ->
        {:error, "Navigation menu item not found"}

      %NavMenuItem{} = nav_menu_item ->
        case check_permission(nav_menu_item, user_profile_id) do
          :ok ->
            Websites.delete_nav_menu_item(nav_menu_item)

          :unauthorized ->
            {:error, "User is not authorized to delete this navigation menu item"}
        end
    end
  end

  defp check_permission(nav_menu_item, user_profile_id) do
    website = Websites.get_website!(nav_menu_item.website_id)

    if website.company_profile_id == user_profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
