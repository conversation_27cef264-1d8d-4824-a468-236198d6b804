defmodule AthenaWeb.Resolvers.Websites.DeletePage do
  @moduledoc false
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_parent, %{id: page_id}, %{context: %{current_company_profile_user: %ProfileUser{} = profile_user}}) do
    with page when not is_nil(page) <- Websites.get_page(page_id),
         page = Gaia.Repo.preload(page, :website),
         true <- page.website.company_profile_id == profile_user.profile_id,
         {:ok, page} <- Websites.delete_page(page) do
      {:ok, page}
    else
      nil -> {:error, "Page not found"}
      false -> {:error, "You are not authorized to delete this page"}
      {:error, _changeset} -> {:error, "Failed to delete page"}
    end
  end
end
