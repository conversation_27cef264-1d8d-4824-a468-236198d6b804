defmodule AthenaWeb.Resolvers.Websites.CreatePage do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_, %{title: title, slug: slug, source_page_slug: source_page_slug} = params, %{
        context: %{
          current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}, user: current_user}
        }
      }) do
    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)

    case Websites.create_page_from_preset(website, params[:preset], current_user, %{
           title: title,
           slug: slug,
           source_page_slug: source_page_slug
         }) do
      {:ok, page} ->
        {:ok, page}

      {:error,
       %Ecto.Changeset{
         errors: [website_id: {_, [constraint: :unique, constraint_name: "websites_pages_website_id_slug_index"]}]
       }} ->
        {:error, "This slug is already in use for this website"}

      {:error, _changeset} ->
        {:error, "Failed to create page"}
    end
  end
end
