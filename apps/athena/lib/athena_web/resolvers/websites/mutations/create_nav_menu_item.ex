defmodule AthenaWeb.Resolvers.Websites.CreateNavMenuItem do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  require Helper.Error.Custom.ErrorHandler

  def resolve(_, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)
    args = Map.put(args, :website_id, website.id)

    case Websites.create_nav_menu_item(args) do
      {:ok, nav_menu_item} ->
        {:ok, nav_menu_item}

      {:error, changeset} ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Failed to create navigation menu item",
          %{
            company_profile_id: company_profile_id,
            website_id: website.id
          },
          changeset
        )

        {:error, "Oops! Something went wrong"}
    end
  end
end
