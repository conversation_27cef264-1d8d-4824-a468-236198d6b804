defmodule AthenaWeb.Resolvers.Websites.Block do
  @moduledoc false
  alias <PERSON><PERSON>.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites
  alias Gaia.Websites.Block

  def resolve(_, %{id: block_id}, %{context: %{current_company_profile_user: %ProfileUser{} = profile_user}}) do
    with %Block{} = block <- Websites.get_block(block_id),
         :ok <- has_permission?(block, profile_user) do
      {:ok, Gaia.Repo.preload(block, [:last_edited_by_company_user])}
    else
      nil ->
        {:error, "Page not found"}

      :unauthorized ->
        {:error, "Unauthorized access"}
    end
  end

  defp has_permission?(%Block{} = block, profile_user) do
    block = Repo.preload(block, page: :website)

    if block.page.website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
