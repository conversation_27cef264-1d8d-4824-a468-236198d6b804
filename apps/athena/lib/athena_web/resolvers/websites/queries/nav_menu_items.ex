defmodule AthenaWeb.Resolvers.Websites.NavMenuItems do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_parent, _args, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{} = profile}}}) do
    case Websites.get_unpublished_website(profile) do
      nil ->
        {:ok, []}

      website ->
        nav_menu_items = Websites.list_top_level_nav_menu_items_by_website(website)
        {:ok, nav_menu_items}
    end
  end
end
