defmodule AthenaWeb.Resolvers.Websites.CurrentWebsite do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_, _, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}}) do
    website =
      company_profile_id
      |> Websites.get_or_create_website_by_company_profile_id()
      |> Websites.maybe_set_published_at_from_live_site()
      |> Gaia.Repo.preload([:published_by_company_user, :pages])

    {:ok, website}
  end
end
