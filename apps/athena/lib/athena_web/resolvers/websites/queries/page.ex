defmodule AthenaWeb.Resolvers.Websites.Page do
  @moduledoc false
  alias <PERSON><PERSON>.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.Websites
  alias Gaia.Websites.Page

  def resolve(_, %{slug: slug}, %{context: %{current_company_profile_user: %ProfileUser{profile: profile} = profile_user}}) do
    with %Websites.Website{} = website <- Websites.get_unpublished_website(profile),
         %Page{} = page <- Repo.get_by(Page, website_id: website.id, slug: slug),
         :ok <- has_permission?(page, profile_user) do
      {:ok, Repo.preload(page, blocks: [:last_edited_by_company_user])}
    else
      nil ->
        {:error, "Page not found"}

      :unauthorized ->
        {:error, "Unauthorized access"}
    end
  end

  defp has_permission?(page, profile_user) do
    website = Websites.get_website!(page.website_id)

    if website.company_profile_id == profile_user.profile_id do
      :ok
    else
      :unauthorized
    end
  end
end
