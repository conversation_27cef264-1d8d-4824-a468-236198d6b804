defmodule AthenaWeb.Resolvers.Websites.HubStats do
  @moduledoc false
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Websites

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    index_page_sign_ups_unique_visitors_and_pending_qualified_investors_count_last_30_days =
      Websites.index_page_sign_ups_unique_visitors_and_pending_qualified_investors_count_last_30_days(company_profile_id)

    {:ok, index_page_sign_ups_unique_visitors_and_pending_qualified_investors_count_last_30_days}
  end
end
