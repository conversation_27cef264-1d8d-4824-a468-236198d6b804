defmodule AthenaWeb.Resolvers.Investors.InvestorUserFields do
  @moduledoc false

  def is_holding_verified(%Gaia.Investors.User{} = investor_user, _args, _resolution),
    do: {:ok, Gaia.ShareholderMatching.is_holding_verified?(investor_user)}

  def is_holding_verified(_parent, _args, _resolution), do: {:ok, false}

  def updates_viewed(%{id: investor_user_id}, _args, _resolution),
    do: {:ok, Gaia.Investors.updates_viewed(investor_user_id)}

  def updates_viewed(_parent, _args, _resolution), do: {:ok, 0}

  def announcements_viewed(%{id: investor_user_id}, _args, _resolution),
    do: {:ok, Gaia.Investors.announcements_viewed(investor_user_id)}

  def announcements_viewed(_parent, _args, _resolution), do: {:ok, 0}
end
