defmodule AthenaWeb.Resolvers.Investors.Total do
  @moduledoc """
    Investor Users Field Resolvers
  """

  import Ecto.Query, warn: false

  alias <PERSON>aia.Investors
  alias Gaia.Repo

  def total(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    options = Map.get(args, :options, %{filters: [], orders: []})

    length =
      options
      |> Investors.users_query_by_company_profile_id(id)
      |> Repo.aggregate(:count)

    {:ok, length}
  end
end
