defmodule AthenaWeb.Resolvers.Investors.CertificateById do
  @moduledoc false
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Investors
  alias Investors.Certificate
  alias Investors.User

  @gettext_context "Certificate by ID query resolver"

  def resolve(_parents, %{id: certificate_id}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    with %Certificate{investor_user: %User{company_profile_id: company_profile_id}} = certificate <-
           certificate_id
           |> Investors.get_certificate()
           |> Gaia.Repo.preload(:investor_user),
         {:check_certificate_ownership, true} <-
           {:check_certificate_ownership, company_profile_id == current_company_profile_id} do
      pending_certificates = Investors.list_pending_certificates_by_company_profile_id(current_company_profile_id)

      total_pending = length(pending_certificates)
      next_pending_id = if total_pending == 0, do: nil, else: pending_certificates |> Enum.at(0) |> Map.get(:id)

      {:ok,
       %{
         certificate: certificate,
         total_pending: total_pending,
         next_pending_id: next_pending_id
       }}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Could not find certificate by ID"
             )
         }}
    end
  end
end
