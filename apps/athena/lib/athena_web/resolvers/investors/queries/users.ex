defmodule AthenaWeb.Resolvers.Investors.Users do
  @moduledoc """
    Investor Users Query Resolvers
  """

  import Ecto.Query, warn: false

  alias Absinthe.Relay.Connection
  alias Gaia.Investors
  alias Gaia.Repo

  def cursor(_parents, args, %{
        context: %{current_company_profile_user: %Gaia.Companies.ProfileUser{profile: %Gaia.Companies.Profile{id: id}}}
      }) do
    options = Map.get(args, :options, %{filters: [], orders: []})

    connection_result =
      options
      |> Investors.users_query_by_company_profile_id(id)
      |> Connection.from_query(&Repo.all/1, args)

    case connection_result do
      {:ok, connection} ->
        {:ok,
         %{
           edges: connection.edges,
           page_info: connection.page_info,
           options: options
         }}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Oops! Something went wrong."
         }}
    end
  end
end
