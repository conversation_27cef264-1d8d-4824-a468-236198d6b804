defmodule AthenaWeb.Resolvers.Investors.PendingReviewCertificates do
  @moduledoc false
  def resolve(_parents, _arg, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }),
      do: {:ok, Gaia.Investors.list_pending_certificates_by_company_profile_id(current_company_profile_id)}
end
