defmodule AthenaWeb.Resolvers.Investors.InvestorActivityStats do
  @moduledoc false
  def resolve(_parents, %{id: investor_user_id}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    case Gaia.Investors.get_user_by(%{id: investor_user_id, company_profile_id: current_company_profile_id}) do
      %Gaia.Investors.User{} -> {:ok, Gaia.Investors.activity_stats(investor_user_id)}
      _ -> {:ok, nil}
    end
  end

  def resolve(_parents, _arg, _context) do
    {:ok, nil}
  end
end
