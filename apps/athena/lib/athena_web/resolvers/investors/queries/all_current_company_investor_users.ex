defmodule AthenaWeb.Resolvers.Investors.AllCurrentCompanyInvestorUsers do
  @moduledoc false

  def resolve(_parent, _args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            profile: %Gaia.Companies.Profile{id: current_company_profile_id}
          }
        }
      }) do
    {:ok, Gaia.Investors.list_latest_investors_users_by(company_profile_id: current_company_profile_id)}
  end
end
