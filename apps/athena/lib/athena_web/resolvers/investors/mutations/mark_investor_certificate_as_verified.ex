defmodule AthenaWeb.Resolvers.Investors.MarkInvestorCertificateAsVerified do
  @moduledoc """
  Resolver for MarkInvestorCertificateAsVerified mutation
  """
  use Helper.Pipe
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Investors
  alias Gaia.Repo
  alias Investors.Certificate

  @gettext_context "Mark investor certificate as verified mutation resolver"

  def resolve(_parent, %{certificate_id: certificate_id, expires_at: expires_at, type: type}, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: profile_user_id,
            profile_id: current_company_profile_id
          }
        }
      }) do
    with {:get_certificate,
          %Certificate{
            investor_user: %Investors.User{company_profile_id: company_profile_id} = investor_user,
            status: :pending_review
          } = certificate} <-
           {:get_certificate, certificate_id |> Investors.get_certificate() |> Repo.preload([:investor_user])},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:update_certificate, {:ok, %Certificate{} = updated_certificate}} <-
           {:update_certificate,
            Investors.update_certificate(certificate, %{
              last_company_reviewer_id: profile_user_id,
              expires_at: expires_at,
              status: :verified,
              type: type
            })} do
      Task.start(fn -> notify_investor_of_outcome_via_email(investor_user) end)
      {:ok, updated_certificate}
    else
      {:get_certificate, %Certificate{status: :verified}} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Certificate has already been verified."
         )}

      {:get_certificate, %Certificate{status: :rejected}} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Certificate has already been rejected."
         )}

      {:get_certificate, nil} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Could not find certificate"
         )}

      {:check_ownership, false} ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      {:update_certificate, _} ->
        {:error, gettext("Could not mark certificate as verified")}
    end
  end

  def notify_investor_of_outcome_via_email(investor_user) do
    try do
      user = Investors.investor_user_for_email(investor_user)

      Gaia.Notifications.Email.deliver(
        EmailTransactional.Investor,
        :qualified_investor_verified,
        [user],
        user.company_profile.id
      )
    rescue
      error ->
        Sentry.capture_exception(error)
    end

    :ok
  end
end
