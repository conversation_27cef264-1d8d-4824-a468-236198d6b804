defmodule AthenaWeb.Resolvers.Investors.MarkInvestorCertificateAsRejected do
  @moduledoc """
  MarkInvestorCertificateAsRejected Mutation Resolver
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Investors
  alias Gaia.Repo

  @gettext_context "Reject Certificate Mutation"
  def resolve(_parenst, %{certificate_id: certificate_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            id: profile_user_id,
            profile_id: current_company_profile_id
          }
        }
      }) do
    rejection_comment = Map.get(args, :rejection_comment)

    with %Investors.Certificate{
           investor_user: %Investors.User{company_profile_id: company_profile_id} = investor_user,
           status: :pending_review
         } = certificate <-
           certificate_id
           |> Investors.get_certificate()
           |> Repo.preload([:investor_user]),
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:ok, %Investors.Certificate{} = rejected_certificate} <-
           Investors.update_certificate(certificate, %{
             last_company_reviewer_id: profile_user_id,
             status: :rejected
           }) do
      Task.start(fn -> send_email(investor_user, rejection_comment) end)
      {:ok, rejected_certificate}
    else
      %Investors.Certificate{status: :approved} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Can not reject certificate which already approved"
         )}

      %Investors.Certificate{status: :rejected} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "The Certificate has already been rejected."
         )}

      {:check_ownership, false} ->
        {:error, dpgettext("errors", @gettext_context, "You are unauthorised.")}

      {:error, _} ->
        {:error,
         dpgettext(
           "errors",
           @gettext_context,
           "Can not reject selected certificate."
         )}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: gettext("Oops! Something went wrong.")
         }}
    end
  end

  def send_email(investor_user, rejection_comment) do
    with %Investors.User{} = user <-
           Investors.investor_user_for_email(investor_user),
         {:ok, _} <-
           Gaia.Notifications.Email.deliver(
             EmailTransactional.Investor,
             :qualified_investor_rejected,
             [user, rejection_comment],
             user.company_profile.id
           ) do
      :ok
    else
      _ -> :ok
    end
  end
end
