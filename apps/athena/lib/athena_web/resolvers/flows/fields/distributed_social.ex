defmodule AthenaWeb.Resolvers.Flows.Fields.DistributedSocial do
  @moduledoc """
  Resolvers for GraphQL field distributed_social
  """

  def linkedin_post_url(%{linkedin_post_id: linkedin_post_id}, _, _) when is_binary(linkedin_post_id) do
    # Weird behaviour when using URI.merge with "urn:li:share:share_id"
    {:ok, "https://www.linkedin.com/feed/update/#{linkedin_post_id}"}
  end

  def linkedin_post_url(_, _, _), do: {:ok, nil}

  def twitter_post_url(%{twitter_post_id: twitter_post_id}, _, _) when is_binary(twitter_post_id) do
    # To keep it simple, we just use a default screen name in the URL
    # https://developer.twitter.com/en/blog/community/2020/getting-to-the-canonical-url-for-a-tweet

    post_url =
      "https://twitter.com/twitter/status/"
      |> URI.parse()
      |> URI.merge(twitter_post_id)
      |> URI.to_string()

    {:ok, post_url}
  end

  def twitter_post_url(_, _, _), do: {:ok, nil}
end
