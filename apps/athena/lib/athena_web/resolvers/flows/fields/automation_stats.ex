defmodule AthenaWeb.Resolvers.Flows.Fields.AutomationStats do
  @moduledoc false

  alias Gaia.Comms
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def shareholder_welcome_email_sent_count(_, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok, Comms.count_shareholder_welcome_emails_by_company_profile_id(current_company_profile_id)}
  end
end
