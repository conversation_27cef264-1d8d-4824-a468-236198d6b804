defmodule AthenaWeb.Resolvers.Flows.AutomationStats do
  @moduledoc """
  Resolver to get the high level stats for automation flows for the current company
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, _, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok, %{id: "automation-stats-#{current_company_profile_id}"}}
  end
end
