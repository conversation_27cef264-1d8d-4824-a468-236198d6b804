defmodule AthenaWeb.Resolvers.Flows.DistributionSettingsForChannelForFlowType do
  @moduledoc """
  Module to resolve if company has configured distribution flows
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings

  @gettext_context "Distribution settings for channel for flow type query"

  def resolve(_parent, %{channel: channel, flow_type: flow_type}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    channel
    |> Flows.get_channel_distribution_settings_for_flow_type_for_company(
      flow_type,
      current_company_profile_id
    )
    |> case do
      %DistributionSettings{} = distribution_settings ->
        {:ok, distribution_settings}

      nil ->
        {:ok, nil}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get distribution settings for channel and type"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{message: dpgettext("errors", @gettext_context, "Unable to get distribution settings")}}
end
