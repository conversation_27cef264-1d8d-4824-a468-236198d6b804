defmodule AthenaWeb.Resolvers.Flows.CurrentCompanyDistributionSettings do
  @moduledoc """
  Resolver to get the distribution settings for the current company
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows

  def resolve(_, %{flow_type: flow_type}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    {:ok, Flows.get_settings_by_flow_for_company(flow_type, current_company_profile_id)}
  end
end
