defmodule AthenaWeb.Resolvers.Flows.ActiveDistributionFlows do
  @moduledoc """
  Resolve to check if announcements and updates are configured.
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows

  @gettext_context "Active distribution flows query"

  def resolve(_parent, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}
      }) do
    current_company_profile_id
    |> Flows.get_active_distribution_settings_for_company()
    |> case do
      active_distribution_settings when is_list(active_distribution_settings) ->
        {
          :ok,
          active_distribution_settings
          |> Flows.map_to_active_flows()
          |> Map.put(:id, "active-distribution-flows-#{current_company_profile_id}")
        }

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to get active distribution flows"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{message: dpgettext("errors", @gettext_context, "Unable to get active distribution flows")}}
end
