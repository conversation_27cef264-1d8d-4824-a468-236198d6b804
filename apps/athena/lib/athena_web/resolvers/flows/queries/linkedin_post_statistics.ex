defmodule AthenaWeb.Resolvers.Flows.LinkedinPostStatistics do
  @moduledoc """
  Resolvers for GraphQL field linkedin_post_statistics
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.SocialConnection
  alias Gaia.Flows
  alias Gaia.Socials
  alias Socials.LinkedIn

  @gettext_context "LinkedinPostStatistics query"

  def resolve(_parent, %{post_id: post_urn}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile: %Profile{
              social_connection:
                %SocialConnection{
                  linkedin_access_token: access_token,
                  linkedin_organisation_id: organisation_urn,
                  company_profile_id: company_profile_id
                } = social_connection
            }
          }
        }
      }) do
    with {:check_linkedin_post, %Flows.DistributedSocial{}} <-
           {:check_linkedin_post,
            Flows.get_distributed_social_by(%{
              linkedin_post_id: post_urn,
              company_profile_id: company_profile_id
            })},
         {:check_linkedin_connected, true} <-
           {:check_linkedin_connected, Companies.get_is_linkedin_connected(social_connection)},
         {:ok, post_statistics} <-
           {:ok, LinkedIn.post_statistics(access_token, organisation_urn, post_urn)} do
      post_statistics
    else
      {:check_linkedin_post, _} ->
        {:error,
         %Helper.AbsintheError{
           message: dpgettext("errors", @gettext_context, "LinkedIn post not found")
         }}

      {:check_linkedin_connected, _} ->
        {:error,
         %Helper.AbsintheError{
           message: dpgettext("errors", @gettext_context, "LinkedIn not connected")
         }}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to retrieve LinkedIn post statistics")
     }}
  end
end
