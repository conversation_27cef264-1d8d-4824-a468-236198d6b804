defmodule AthenaWeb.Resolvers.Flows.ActivateDistributionSettingsForEmail do
  @moduledoc """
  Resolver for updating distribution settings
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Flows.DistributionSettingsEmail

  @gettext_context "Activate distribution settings mutation"

  def resolve(
        _parent,
        %{
          distribution_settings_id: distribution_settings_id,
          distribution_settings: final_settings,
          distribution_settings_email: final_email_settings
        },
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}}
      ) do
    with {:get_settings,
          %DistributionSettings{
            email_settings: %DistributionSettingsEmail{} = existing_email_settings
          } = existing_distribution_settings} <-
           {:get_settings,
            distribution_settings_id
            |> Flows.get_distribution_settings_for_company!(company_profile_id)
            |> Gaia.Repo.preload([:email_settings])},
         {:update_settings, {:ok, %{distribution_settings: %DistributionSettings{} = updated_settings}}} <-
           {:update_settings,
            update_settings_and_email_settings_and_activate(
              existing_distribution_settings,
              final_settings,
              existing_email_settings,
              final_email_settings
            )} do
      {:ok, updated_settings}
    else
      {:get_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to activate distribution settings, as settings do not exist"
             )
         }}

      {:update_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to activate distribution settings"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{message: dpgettext("errors", @gettext_context, "Unable to activate distribution settings")}}

  defp update_settings_and_email_settings_and_activate(
         distribution_settings,
         update_settings,
         distribution_settings_email,
         update_email_settings
       ) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :email_settings,
      Ecto.Changeset.change(distribution_settings_email, update_email_settings)
    )
    |> Ecto.Multi.update(
      :distribution_settings,
      Ecto.Changeset.change(distribution_settings, Map.put(update_settings, :is_active, true))
    )
    |> Gaia.Repo.transaction()
  end
end
