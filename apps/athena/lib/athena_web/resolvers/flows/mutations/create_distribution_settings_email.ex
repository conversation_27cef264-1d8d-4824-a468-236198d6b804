defmodule AthenaWeb.Resolvers.Flows.CreateDistributionSettingsEmail do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Flows.DistributionSettingsEmail

  @gettext_context "Create distribution settings email mutation"

  def resolve(_parent, %{distribution_settings_id: distribution_settings_id, distribution_settings_email: args}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile:
              %Profile{
                id: current_company_profile_id,
                name: company_name,
                ticker: %{market_key: market_key, listing_key: listing_key}
              } = profile
          }
        }
      }) do
    with {:settings_for_company, {true, %DistributionSettings{} = distribution_settings}} <-
           {:settings_for_company, ensure_settings_for_company(distribution_settings_id, current_company_profile_id)},
         {:get_templates, %{email_json: email_json, email_html: email_html}} <-
           {:get_templates, build_template(profile, distribution_settings)},
         {:custom_emails, %{marketing_email: marketing_email}} <-
           {:custom_emails, Gaia.Comms.get_custom_emails_by_company_profile_id(current_company_profile_id)},
         {:create_email_settings, {:ok, %DistributionSettingsEmail{} = new_email_settings}} <-
           {:create_email_settings,
            Flows.create_distribution_settings_email(
              args
              |> Map.put(
                :from_name,
                get_send_from_name_for_marketing(marketing_email, company_name)
              )
              |> Map.put(
                :subject,
                get_subject(market_key, listing_key, distribution_settings)
              )
              |> Map.put(:email_json, email_json)
              |> Map.put(:email_html, email_html)
              |> Map.put(:distribution_settings_id, distribution_settings_id)
              |> Map.put(:company_profile_id, current_company_profile_id)
            )} do
      {:ok, new_email_settings}
    else
      {:create_email_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to create email settings for distribution"
             )
         }}

      {:get_templates, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to generate default templates"
             )
         }}

      {:settings_for_company, _} ->
        {:error,
         %Helper.AbsintheError{
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unauthorized: Unable to create email settings for distribution"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{
         message: dpgettext("errors", @gettext_context, "Unable to create email settings for distribution")
       }}

  defp ensure_settings_for_company(distribution_settings_id, current_company_profile_id) do
    distribution_settings_id
    |> Flows.get_distribution_settings!()
    |> case do
      %DistributionSettings{company_profile_id: company_profile_id} = distribution_settings ->
        {company_profile_id == current_company_profile_id, distribution_settings}

      _ ->
        {false, nil}
    end
  end

  defp get_send_from_name_for_marketing(%Gaia.Comms.CustomEmail{send_from_name: from_name}, _company_name) do
    from_name
  end

  defp get_send_from_name_for_marketing(_marketing_email, company_name), do: company_name

  defp get_subject(market_key, listing_key, %DistributionSettings{
         included_announcement_types: included_announcement_types,
         included_update_types: nil,
         shareholder_welcome_enabled: nil
       })
       when is_list(included_announcement_types),
       do: "#{String.upcase(Atom.to_string(market_key))}:#{String.upcase(listing_key)} - {{ announcement_title }}"

  defp get_subject(market_key, listing_key, %DistributionSettings{
         included_announcement_types: nil,
         included_update_types: included_update_types,
         shareholder_welcome_enabled: nil
       })
       when is_list(included_update_types),
       do: "#{String.upcase(Atom.to_string(market_key))}:#{String.upcase(listing_key)} - {{ update_title }}"

  defp get_subject(market_key, listing_key, %DistributionSettings{
         included_announcement_types: nil,
         included_update_types: nil,
         shareholder_welcome_enabled: shareholder_welcome_enabled
       })
       when is_boolean(shareholder_welcome_enabled),
       do: "#{String.upcase(Atom.to_string(market_key))}:#{String.upcase(listing_key)} - Welcome new shareholder!"

  defp build_template(profile, %DistributionSettings{shareholder_welcome_enabled: nil}),
    do: Flows.build_distribution_email_template(profile, :automated_distribution)

  defp build_template(profile, %DistributionSettings{shareholder_welcome_enabled: shareholder_welcome_enabled})
       when is_boolean(shareholder_welcome_enabled),
       do: Flows.build_distribution_email_template(profile, :new_shareholder_welcome)
end
