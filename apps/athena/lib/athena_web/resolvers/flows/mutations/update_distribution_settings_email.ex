defmodule AthenaWeb.Resolvers.Flows.UpdateDistributionSettingsEmail do
  @moduledoc false

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettingsEmail

  @gettext_context "Update distribution settings email mutation"

  def resolve(
        _parent,
        %{distribution_settings_email_id: distribution_settings_email_id, distribution_settings_email: args},
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}}
      ) do
    with {:settings_for_company, %DistributionSettingsEmail{} = existing_settings} <-
           {:settings_for_company,
            ensure_and_get_settings_for_company(distribution_settings_email_id, current_company_profile_id)},
         {:update_email_settings, {:ok, %DistributionSettingsEmail{} = new_email_settings}} <-
           {:update_email_settings, Flows.update_distribution_settings_email(existing_settings, args)} do
      {:ok, new_email_settings}
    else
      {:update_email_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to update email settings for distribution"
             )
         }}

      {:settings_for_company, _} ->
        {:error,
         %Helper.AbsintheError{
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unauthorized: Unable to update email settings for distribution"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{
         message: dpgettext("errors", @gettext_context, "Unable to update email settings for distribution")
       }}

  defp ensure_and_get_settings_for_company(distribution_settings_email_id, current_company_profile_id) do
    distribution_settings_email_id
    |> Flows.get_distribution_settings_email!()
    |> case do
      %DistributionSettingsEmail{company_profile_id: company_profile_id} = settings ->
        if company_profile_id == current_company_profile_id, do: settings, else: false

      _ ->
        false
    end
  end
end
