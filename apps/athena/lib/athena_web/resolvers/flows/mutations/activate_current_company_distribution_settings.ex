defmodule AthenaWeb.Resolvers.Flows.ActivateCurrentCompanyDistributionSettings do
  @moduledoc """
  Resolver to activate current company distribution settings
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows

  @gettext_context "Athena - ActivateCurrentCompanyDistributionSettings"

  def resolve(_parent, %{flow_type: flow_type}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    Flows.activate_distribution_settings_for_company(flow_type, company_profile_id)
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to activate distribution settings")
     }}
  end
end
