defmodule AthenaWeb.Resolvers.Flows.GenerateSocialThumbnailSignedUrl do
  @moduledoc false

  use Gettext, backend: Athena<PERSON>eb.Gettext

  @gettext_context "Generate social thumbnail signed url mutation"

  def resolve(_parent, %{mime_type: mime_type}, _resolution) do
    with %GcsSignedUrl.Client{} = client <-
           :helper
           |> Application.fetch_env!(:service_account)
           |> Jason.decode!()
           |> GcsSignedUrl.Client.load(),
         [ext | _tail] <- MIME.extensions(mime_type),
         url when is_binary(url) <-
           GcsSignedUrl.generate_v4(
             client,
             Application.fetch_env!(:arc, :bucket),
             "uploads/social_thumbnails/#{Ecto.UUID.generate()}.#{ext}",
             expires: 1800,
             headers: ["Content-Type": mime_type, "X-Goog-Acl": "public-read"],
             verb: "PUT"
           ) do
      {:ok, url}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to generate signed url."
             )
         }}
    end
  end
end
