defmodule AthenaWeb.Resolvers.Flows.SendManualSocialPost do
  @moduledoc """
  Resolvers to manually send a social post about a media
  """

  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows.DistributedSocial
  alias Gaia.Flows.Distribution
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaUpdate

  @gettext_context "SendManualSocialPost mutation"

  def resolve(
        _parent,
        %{media_id: media_id, social_platform: social_platform, text: text, linkedin_thumbail_url: linkedin_thumbail_url},
        %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: current_company_profile_id}}}}
      ) do
    with {:get_media,
          %Media{
            company_profile_id: company_profile_id,
            media_announcement: announcement,
            media_update: update
          } = media}
         when is_struct(announcement, MediaAnnouncement) or is_struct(update, MediaUpdate) <-
           {
             :get_media,
             media_id
             |> Interactions.get_media()
             |> Gaia.Repo.preload([
               :distributed_social,
               :media_announcement,
               :media_update,
               company_profile: [:custom_domain, :social_connection, :ticker]
             ])
           },
         {:check_ownership, true} <-
           {:check_ownership, current_company_profile_id == company_profile_id},
         {:ok, %DistributedSocial{} = distributed_social} <-
           Distribution.distribute_to_social(media, social_platform, text, linkedin_thumbail_url) do
      {:ok, distributed_social}
    else
      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: dpgettext("errors", @gettext_context, "Unable to send social post manually")
         }}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to send social post manually")
     }}
  end
end
