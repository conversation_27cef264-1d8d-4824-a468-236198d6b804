defmodule AthenaWeb.Resolvers.Flows.UpdateDistributionSettings do
  @moduledoc """
  Resolver for updating distribution settings
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings

  @gettext_context "Update distribution settings mutation"

  def resolve(_parent, %{distribution_settings_id: distribution_settings_id, distribution_settings: args}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    with {:get_settings, %DistributionSettings{} = existing_distribution_settings} <-
           {:get_settings,
            Flows.get_distribution_settings_for_company!(
              distribution_settings_id,
              company_profile_id
            )},
         {:ensure_flow_validity, true} <-
           {:ensure_flow_validity, ensure_flow_validity(args, existing_distribution_settings)},
         {:update_settings, {:ok, %DistributionSettings{} = updated_settings}} <-
           {:update_settings, Flows.update_distribution_settings(existing_distribution_settings, args)} do
      {:ok, updated_settings}
    else
      {:get_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to update distribution settings, as settings do not exist"
             )
         }}

      {:ensure_flow_validity, _} ->
        {:error,
         %Helper.AbsintheError{
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to update distribution settings, as new settings do not match the same flow as existing settings"
             )
         }}

      {:update_settings, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message:
             dpgettext(
               "errors",
               @gettext_context,
               "Unable to update distribution settings"
             )
         }}
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{message: dpgettext("errors", @gettext_context, "Unable to update distribution settings")}}

  # If not updating flow values then valid
  defp ensure_flow_validity(%{included_announcement_types: nil, included_update_types: nil}, _existing_settings), do: true

  # If not updating announcements, and existing announcements is nil then valid
  defp ensure_flow_validity(%{included_announcement_types: nil}, %DistributionSettings{included_announcement_types: nil}),
    do: true

  # If not updating updates, and existing updates is nil then valid
  defp ensure_flow_validity(%{included_update_types: nil}, %DistributionSettings{included_update_types: nil}), do: true

  # If trying to update announcements and updates then invalid
  defp ensure_flow_validity(%{included_announcement_types: announcements, included_update_types: updates}, _)
       when is_list(announcements) and is_list(updates),
       do: false

  # If trying to update announcements and existing updates is list the invalid
  defp ensure_flow_validity(%{included_announcement_types: announcements}, %DistributionSettings{
         included_update_types: updates
       })
       when is_list(announcements) and is_list(updates),
       do: false

  # If trying to update updates and existing announcements is list the invalid
  defp ensure_flow_validity(%{included_update_types: updates}, %DistributionSettings{
         included_announcement_types: announcements
       })
       when is_list(announcements) and is_list(updates),
       do: false

  defp ensure_flow_validity(_, _), do: true
end
