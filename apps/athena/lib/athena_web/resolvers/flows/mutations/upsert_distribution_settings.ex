defmodule AthenaWeb.Resolvers.Flows.UpsertDistributionSettings do
  @moduledoc """
  Resolver to upsert current company distribution settings
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Comms
  alias Gaia.Comms.BaseEmailTemplate
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Flows.DistributionSettingsEmail
  alias Gaia.Repo

  @gettext_context "Athena - UpsertDistributionSettings"

  def resolve(_parent, %{flow_type: flow_type} = args, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: company_profile_id}
          }
        }
      }) do
    # Get inputs
    email = Map.get(args, :email)
    linkedin = Map.get(args, :linkedin)
    twitter = Map.get(args, :twitter)

    # Get existing settings
    settings = Flows.get_settings_by_flow_for_company(flow_type, company_profile_id)

    Ecto.Multi.new()
    |> prepare_upsert(flow_type, company_profile_id, :email, settings.email, email)
    |> prepare_upsert(flow_type, company_profile_id, :linkedin, settings.linkedin, linkedin)
    |> prepare_upsert(flow_type, company_profile_id, :twitter, settings.twitter, twitter)
    |> update_base_email_template(
      company_profile_id,
      settings.email,
      email,
      company_profile_user_id
    )
    |> Repo.transaction()
    |> case do
      {:ok, _} ->
        # Returns the updated settings
        {:ok, Flows.get_settings_by_flow_for_company(flow_type, company_profile_id)}

      error ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: dpgettext("errors", @gettext_context, "Unable to upsert distribution settings")
         }}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       message: dpgettext("errors", @gettext_context, "Unable to upsert distribution settings")
     }}
  end

  # Create setting when existing_setting is not found
  defp prepare_upsert(multi, flow_type, company_profile_id, channel, nil, %{} = input) do
    formatted_input = format_input(flow_type, company_profile_id, channel, nil, input)

    Ecto.Multi.insert(
      multi,
      String.to_atom("insert_#{flow_type}_#{company_profile_id}_#{channel}"),
      Flows.change_distribution_settings(%DistributionSettings{}, formatted_input)
    )
  end

  # Update setting when both existing_setting and input are provided
  defp prepare_upsert(
         multi,
         flow_type,
         company_profile_id,
         channel,
         %DistributionSettings{} = existing_setting,
         %{} = input
       ) do
    formatted_input = format_input(flow_type, company_profile_id, channel, existing_setting, input)

    Ecto.Multi.update(
      multi,
      String.to_atom("update_#{flow_type}_#{company_profile_id}_#{channel}"),
      Flows.change_distribution_settings(existing_setting, formatted_input)
    )
  end

  # Do nothing if input is not provided
  defp prepare_upsert(multi, _flow_type, _company_profile_id, _channel, _existing_setting, _input) do
    multi
  end

  # Update base_email_template when both existing_setting and input are provided
  defp update_base_email_template(
         multi,
         company_profile_id,
         %DistributionSettings{shareholder_welcome_enabled: shareholder_welcome_enabled},
         %{email_settings: %{email_html: email_html, email_json: email_json} = _email_settings_input} = _input,
         company_profile_user_id
       ) do
    template_type =
      if is_nil(shareholder_welcome_enabled),
        do: :automated_distribution,
        else: :new_shareholder_welcome

    %{company_profile_id: company_profile_id, template_type: template_type}
    |> Comms.get_base_email_template_by()
    |> case do
      %BaseEmailTemplate{} = exisitng_base_email_template ->
        Ecto.Multi.update(
          multi,
          String.to_atom("update_automated_distribution_base_email_template_#{company_profile_id}"),
          Comms.change_base_email_template(exisitng_base_email_template, %{
            email_html: email_html,
            email_json: email_json,
            last_edited_by_company_profile_user_id: company_profile_user_id
          })
        )

      _ ->
        Ecto.Multi.insert(
          multi,
          String.to_atom("insert_automated_distribution_base_email_template_#{company_profile_id}"),
          Comms.change_base_email_template(%BaseEmailTemplate{}, %{
            company_profile_id: company_profile_id,
            email_html: email_html,
            email_json: email_json,
            last_edited_by_company_profile_user_id: company_profile_user_id,
            template_type: template_type
          })
        )
    end
  end

  defp update_base_email_template(multi, _company_profile_id, _existing_email_setting, _input, _company_profile_user_id) do
    multi
  end

  def format_input(flow_type, company_profile_id, channel, existing_setting, input) do
    %{channel: channel, company_profile_id: company_profile_id}
    |> Enum.into(input)
    |> format_input_by_flow_type(flow_type)
    |> format_email_settings_input(existing_setting, company_profile_id)
  end

  defp format_input_by_flow_type(%{} = input, :announcement) do
    Map.put(input, :included_update_types, nil)
  end

  defp format_input_by_flow_type(%{} = input, :update) do
    Map.put(input, :included_announcement_types, nil)
  end

  defp format_input_by_flow_type(%{} = input, :new_shareholder_welcome) do
    input
    |> Map.put(:included_update_types, nil)
    |> Map.put(:included_announcement_types, nil)
  end

  # When channel is email, try to get the existing_email_settings_id whenever possible
  # Prevent flows_distributions_settings_email to increment its id unecessarily
  defp format_email_settings_input(
         %{email_settings: %{} = email_settings_input} = input,
         %DistributionSettings{
           channel: :email,
           email_settings: %DistributionSettingsEmail{id: existing_email_settings_id}
         },
         company_profile_id
       ) do
    updated_email_settings_input =
      email_settings_input
      |> Map.put(:id, existing_email_settings_id)
      |> Map.put(:company_profile_id, company_profile_id)

    Map.put(input, :email_settings, updated_email_settings_input)
  end

  defp format_email_settings_input(
         %{email_settings: %{} = email_settings_input} = input,
         _existing_setting,
         company_profile_id
       ) do
    updated_email_settings_input = Map.put(email_settings_input, :company_profile_id, company_profile_id)

    Map.put(input, :email_settings, updated_email_settings_input)
  end

  defp format_email_settings_input(input, _existing_setting, _company_profile_id) do
    input
  end
end
