defmodule AthenaWeb.Resolvers.Flows.CreateDistributionSettingsForFlowType do
  @moduledoc """
  Resolver for create distribution settings
  """
  use Gettext, backend: AthenaWeb.Gettext

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettings

  @gettext_context "Create distribution settings mutation"

  def resolve(_parents, %{distribution_settings: settings, flow_type: :announcement}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    settings
    |> Map.put(:company_profile_id, company_profile_id)
    |> Map.put(:included_announcement_types, [])
    |> Map.put(:recipient_list_type, [:all])
    |> Flows.create_distribution_settings()
    |> case do
      {:ok, %DistributionSettings{} = distribution_settings} ->
        {:ok, distribution_settings}

      {:error, error} ->
        return_error(error)
    end
  end

  def resolve(_parents, %{distribution_settings: settings, flow_type: :update}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    settings
    |> Map.put(:company_profile_id, company_profile_id)
    |> Map.put(:included_update_types, [])
    |> Map.put(:recipient_list_type, [:all])
    |> Flows.create_distribution_settings()
    |> case do
      {:ok, %DistributionSettings{} = distribution_settings} ->
        {:ok, distribution_settings}

      {:error, error} ->
        return_error(error)
    end
  end

  def resolve(_parents, %{distribution_settings: settings, flow_type: :new_shareholder_welcome}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    settings
    |> Map.put(:company_profile_id, company_profile_id)
    |> Map.put(:shareholder_welcome_enabled, true)
    |> Flows.create_distribution_settings()
    |> case do
      {:ok, %DistributionSettings{} = distribution_settings} ->
        {:ok, distribution_settings}

      {:error, error} ->
        return_error(error)
    end
  end

  def resolve(_, _, _),
    do:
      {:error,
       %Helper.AbsintheError{message: dpgettext("errors", @gettext_context, "Unable to get create distribution settings")}}

  defp return_error(error) do
    {:error,
     %Helper.AbsintheError{
       error: error,
       message:
         dpgettext(
           "errors",
           @gettext_context,
           "Unable to get create distribution settings"
         )
     }}
  end
end
