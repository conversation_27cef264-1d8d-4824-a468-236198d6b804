defmodule AthenaWeb.Resolvers.ShareholderInsights.HNWInvestorFields do
  @moduledoc """
  Resolvers for object hnw_investor
  """

  use Helper.Pipe

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.RegistryDataStatus
  alias Gaia.Repo
  alias Gaia.ShareholderInsights

  def total(_, args, %{context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id}}}}) do
    id
    |> ShareholderInsights.hnw_investors_query(args)
    |> Repo.aggregate(:count)
    |> {:ok, __}
  end

  def totals(_, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: id} = company_profile}}
      }) do
    args = Map.drop(args, [:hnw_type])

    Repo.transaction(fn ->
      latest_report_date = get_latest_report_date_as_string(company_profile)
      all = get_total(id, args, nil)
      nominated_without_cert = get_total(id, args, :nominated_without_cert)
      nominated_cert_pending = get_total(id, args, :nominated_cert_pending)
      nominated_cert_verified = get_total(id, args, :nominated_cert_verified)
      identified_via_behaviour = get_total(id, args, :identified_via_behaviour)

      %{
        id: "#{latest_report_date}#{Enum.random(0..1_000_000)}",
        all: all,
        nominated_without_cert: nominated_without_cert,
        nominated_cert_pending: nominated_cert_pending,
        nominated_cert_verified: nominated_cert_verified,
        identified_via_behaviour: identified_via_behaviour
      }
    end)
  end

  defp get_total(company_profile_id, args, type) do
    args = Map.put(args, :hnw_type, type)

    company_profile_id
    |> ShareholderInsights.hnw_investors_query(args)
    |> Repo.aggregate(:count)
  end

  defp get_latest_report_date_as_string(company_profile) do
    company_profile
    |> RegistryDataStatus.get_status()
    |> Map.get(:latest_report_date)
    |> to_string()
  end

  def hnw_status(%{hnw_status: hnw_status}, _args, _resolution) when is_binary(hnw_status),
    do: {:ok, String.to_existing_atom(hnw_status)}

  def hnw_status(%{hnw_status: hnw_status}, _args, _resolution) when is_atom(hnw_status), do: {:ok, hnw_status}

  def hnw_status(_, _args, _resolution), do: {:ok, nil}
end
