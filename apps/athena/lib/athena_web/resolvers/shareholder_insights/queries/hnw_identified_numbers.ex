defmodule AthenaWeb.Resolvers.ShareholderInsights.HNWIdentifiedNumbers do
  @moduledoc """
  HNWIdentifiedNumbers Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.ShareholderInsights

  def resolve(_, _args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    company_profile_id
    |> ShareholderInsights.hnw_identified_numbers()
    |> case do
      {:error, error} ->
        {:error,
         %Helper.AbsintheError{
           error: error,
           message: "Error retrieving hnw identified numbers"
         }}

      {last_seven_days, seven_to_fourteen_days_ago} ->
        {:ok,
         %{
           id: 1,
           last_seven_days: last_seven_days,
           seven_to_fourteen_days_ago: seven_to_fourteen_days_ago
         }}
    end
  end
end
