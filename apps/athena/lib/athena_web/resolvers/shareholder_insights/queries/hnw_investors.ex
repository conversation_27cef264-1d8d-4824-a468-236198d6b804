defmodule AthenaWeb.Resolvers.ShareholderInsights.HNWInvestors do
  @moduledoc """
  HNWInvestors Query Resolvers
  """

  alias Absinthe.Relay.Connection
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Repo
  alias Gaia.ShareholderInsights

  def resolve(_, args, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    company_profile_id
    |> ShareholderInsights.hnw_investors_query(args)
    |> Connection.from_query(&Repo.all/1, args)
    |> case do
      {:ok, connection} ->
        {:ok, %{edges: connection.edges, page_info: connection.page_info}}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving hnw investors"}}
    end
  end
end
