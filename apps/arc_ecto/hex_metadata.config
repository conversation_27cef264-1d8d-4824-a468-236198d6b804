{<<"app">>,<<"arc_ecto">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"An integration with Arc and Ecto.">>}.
{<<"elixir">>,<<"~> 1.4">>}.
{<<"files">>,
 [<<"mix.exs">>,<<"README.md">>,<<"lib">>,<<"lib/arc_ecto.ex">>,
  <<"lib/arc_ecto">>,<<"lib/arc_ecto/type.ex">>,<<"lib/arc_ecto/schema.ex">>,
  <<"lib/arc_ecto/definition.ex">>]}.
{<<"licenses">>,[<<"Apache 2.0">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/stavro/arc_ecto">>}]}.
{<<"name">>,<<"arc_ecto">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"arc">>},
   {<<"name">>,<<"arc">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.11.0">>}],
  [{<<"app">>,<<"ecto">>},
   {<<"name">>,<<"ecto">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<">= 2.1.0">>}]]}.
{<<"version">>,<<"0.11.3">>}.
