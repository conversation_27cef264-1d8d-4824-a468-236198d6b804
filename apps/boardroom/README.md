# Boardroom

Client for interacting with Boardroom API

All the docs we have are [these Swagger ones](https://boardroom-pty-ltd-nexus.apigee.io/apis/boardroom-crm-api/index).

## Development setup

- Must use a static IP address to connect to boardroom (that is in their whitelist - we have ************** as our dev one)
- Staging and prod already use one via app engine
- for dev env:
  `gcloud config set project leaf-343323`
  `gcloud compute ssh --zone australia-southeast2-a tunnel -- -N -p 22 -D localhost:5000 -f`
- this opens up a tunnel via an app engine instance with a static IP ("tunnel") so you can use a SOCKS5 proxy for outgoing requests
- test with:
  `curl --proxy SOCKS5://localhost:5000 https://ifconfig.me/`
- should return `**************`

## Installation

If [available in Hex](https://hex.pm/docs/publish), the package can be installed
by adding `boardroom` to your list of dependencies in `mix.exs`:

```elixir
def deps do
  [
    {:boardroom, "~> 0.1.0"}
  ]
end
```

Documentation can be generated with [ExDoc](https://github.com/elixir-lang/ex_doc)
and published on [HexDocs](https://hexdocs.pm). Once published, the docs can
be found at <https://hexdocs.pm/boardroom>.

