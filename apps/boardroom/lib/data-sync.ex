defmodule Boardroom.DataSync do
  @moduledoc """
  Documentation for `Boardroom Data Sync Routes`.
  Docs: https://boardroom-pty-ltd-nexus.apigee.io/#/DataSync
  """

  import Boardroom

  @route "DataSync"

  @doc """
  Request snapshot of holders job to run
  Security class "005" is Fully Paid Ordinary Shares

  Example:

  week_ago = :second |> NaiveDateTime.utc_now() |> DateTime.add(-432000, :second) |> DateTime.to_string
  Boardroom.DataSync.request_snapshot("DMJC", "005", week_ago)

  -> {:ok, %{"jobId" => "2efb582a76094eeeaec278462d41a041"}}

  """
  def request_snapshot(issuer_id, security_class, after_date_time) do
    body = %{
      "issuerId" => issuer_id,
      "securityClass" => security_class,
      "afterDateTime" => after_date_time,
      "threshold" => 1
    }

    post("#{@route}/RequestSnapshot", body)
  end

  @doc """
  Download the job for the snapshot
  Security class "005" is Fully Paid Ordinary Shares

  Example:

  Boardroom.DataSync.download_snapshot("2efb582a76094eeeaec278462d41a041")

  -> {:ok,
      %{
        "investors" => [],
        "message" => nil,
        "onlineApplicationIndividuals" => [],
        "onlineApplicationTrustees" => [],
        "onlineApplications" => [],
        "status" => "Ready",
        "transactions" => []
      }}

  """
  def download_snapshot(job_id) do
    body = %{
      "jobId" => job_id
    }

    post("#{@route}/DownloadSnapshot", body)
  end

  @doc """
  Download the job for the investors snapshot

  Example:

  Boardroom.DataSync.download_investors("796146a787eb429fbfc55796f0066ad6")

  -> {:ok,
      %{
        "investors" => [],
        "message" => nil,
        "onlineApplicationIndividuals" => [],
        "onlineApplicationTrustees" => [],
        "onlineApplications" => [],
        "status" => "Ready",
        "transactions" => []
      }}

  """
  def download_investors(job_id) do
    body = %{
      "jobId" => job_id
    }

    post("#{@route}/DownloadInvestors", body)
  end

  @doc """
  Download the job for the transactions snapshot

  Example:

  Boardroom.DataSync.download_transactions("796146a787eb429fbfc55796f0066ad6")

  -> {:ok,
      %{
        "investors" => [],
        "message" => nil,
        "onlineApplicationIndividuals" => [],
        "onlineApplicationTrustees" => [],
        "onlineApplications" => [],
        "status" => "Ready",
        "transactions" => []
      }}

  """
  def download_transactions(job_id) do
    body = %{
      "jobId" => job_id
    }

    post("#{@route}/DownloadTransactions", body)
  end

  # Transaction object
  # %{
  #   "adviserId" => nil,
  #   "brokerId" => nil,
  #   "commissionEntryRate" => nil,
  #   "commissionTrailRate" => nil,
  #   "date" => "20210104",
  #   "direction" => "On",
  #   "entryFee" => nil,
  #   "entryFeeRebate" => 0.0,
  #   "id" => *********,
  #   "investorId" => "S00000688967",
  #   "issuerId" => "DMJC",
  #   "manageFeeIncTax" => nil,
  #   "pricePerUnit" => nil,
  #   "securityClass" => "005",
  #   "subregisterType" => "CHESS",
  #   "tranType" => "HoldingMovement",
  #   "units" => 41664.0,
  #   "value" => nil
  # }
end
