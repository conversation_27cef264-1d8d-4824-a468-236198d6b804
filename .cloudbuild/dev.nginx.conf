events {

}
http {
    map $http_upgrade $connection_upgrade {
    default Upgrade;
    ''      close;
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^athena-staging?\.(.*?)(.*?)$";
    location / {
      proxy_pass http://127.0.0.1:4001;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^hermes-staging?\.(.*?)(.*?)$";
    location / {
      proxy_pass http://127.0.0.1:4003;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^public-staging?\.(.*?)(.*?)$";
    location / {
      proxy_pass http://127.0.0.1:4006;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^admin-staging?\.(.*?)(.*?)$";
    location / {
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_set_header Host $host;
      proxy_pass http://127.0.0.1:4002;
    }
  }

  server {
    listen 8080;
    server_name _;
    location / {
      proxy_pass http://127.0.0.1:4000;
    }
  }
}
