#!/bin/bash
#
# Usage
# > ./prune.sh leaf-staging 10
#
# Arguments:
# 1. Environment
# 2. Number of versions

set -euo pipefail

ENVIRONMENT=$1
NUM_VERSIONS=$2

SERVICE=$(
    grep 'service' .cloudbuild/app.${ENVIRONMENT}.yaml \
    | awk -F':\ ' '{print $2}'
)

echo "Environment: $ENVIRONMENT"
echo "Service: $SERVICE"
echo "Number of versions: $NUM_VERSIONS"

VERSIONS=$(
    gcloud app versions list \
      --service $SERVICE \
    | sed 's/  */:/g' \
    | cut -f 2 -d : \
    | tail -n +2 \
    | head -n $((NUM_VERSIONS * -1))
)

if test -z "$VERSIONS"
then
    echo "No versions to prune"
else
    echo "Some versions need to be pruned"
    gcloud app versions delete --quiet ${VERSIONS[@]}
fi
