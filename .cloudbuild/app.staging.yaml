service: leaf-staging
runtime: custom
env: flex

env_variables:
  ATHENA_URL: https://app-staging.investorhub.com
  ATHENA_WEB_URL: https://athena-staging.investorhub.com
  GOOGLE_STORAGE_BUCKET: leaf-staging
  HADES_URL: https://admin-staging.investorhub.com
  HERMES_WEB_URL: https://hermes-staging.investorhub.com
  LEAF_ENVIRONMENT: staging
  MAILCHIMP_MARKETING_LIST_ID: 4c1e466774
  POSTGRES_POOL_SIZE: 20
  POSTGRES_SOCKET_DIR: /cloudsql/leaf-343323:australia-southeast1:leaf-staging
  LINK_SSH_DIR: ~/.ssh
  LINK_SSH_PUBLIC_KEY_NAME: link_public_key.pem
  LINK_SSH_PRIVATE_KEY_NAME: link_private_key.pem
  LINK_GPG_DIR: ~/.gnupg
  LINK_GPG_PUBLIC_KEY_NAME: link_gpg_public_key.asc
  LINK_GPG_PRIVATE_KEY_NAME: link_gpg_private_key.asc
  RELEASE_NAME: "leaf_be"
  REL_NAME: "leaf_be"
  REPLACE_OS_VARS: true

manual_scaling:
  instances: 1

resources:
  cpu: 2
  memory_gb: 4
  disk_size_gb: 20

beta_settings:
  cloud_sql_instances: leaf-343323:australia-southeast1:leaf-staging

readiness_check:
  path: "/readiness_check"
  check_interval_sec: 30
  timeout_sec: 20
  app_start_timeout_sec: 600

liveness_check:
  path: "/liveness_check"
  check_interval_sec: 30
  timeout_sec: 20

network:
  forwarded_ports:
    # epmd
    - 4369
    # erlang distribution
    - 9999
