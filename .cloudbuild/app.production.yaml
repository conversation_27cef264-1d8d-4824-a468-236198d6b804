service: default
runtime: custom
env: flex

env_variables:
  ATHENA_URL: https://app.investorhub.com
  ATHENA_WEB_URL: https://athena.investorhub.com
  GOOGLE_STORAGE_BUCKET: leaf-prod
  HADES_URL: https://admin.investorhub.com
  tracking: https://tracking.investorhub.com
  HEIMDALLR_WEB_URL: https://heimdallr.investorhub.com
  HERMES_WEB_URL: https://hermes.investorhub.com
  LEAF_ENVIRONMENT: production
  MAILCHIMP_MARKETING_LIST_ID: dd99f4fbaa
  POSTGRES_POOL_SIZE: 50
  POSTGRES_SOCKET_DIR: /cloudsql/leaf-343323:australia-southeast1:investorhub-production
  LINK_SSH_DIR: ~/.ssh
  LINK_SSH_PUBLIC_KEY_NAME: link_public_key.pem
  LINK_SSH_PRIVATE_KEY_NAME: link_private_key.pem
  LINK_GPG_DIR: ~/.gnupg
  LINK_GPG_PUBLIC_KEY_NAME: link_gpg_public_key.asc
  LINK_GPG_PRIVATE_KEY_NAME: link_gpg_private_key.asc
  RELEASE_NAME: "leaf_be"
  REL_NAME: "leaf_be"
  REPLACE_OS_VARS: true

beta_settings:
  cloud_sql_instances: leaf-343323:australia-southeast1:investorhub-production

resources:
  cpu: 2
  memory_gb: 6
  disk_size_gb: 20

automatic_scaling:
  min_num_instances: 2
  max_num_instances: 6
  cool_down_period_sec: 300
  cpu_utilization:
    target_utilization: 0.7

readiness_check:
  path: "/readiness_check"
  check_interval_sec: 30
  timeout_sec: 20
  app_start_timeout_sec: 600

liveness_check:
  path: "/liveness_check"
  check_interval_sec: 30
  timeout_sec: 20

network:
  forwarded_ports:
    # epmd
    - 4369
    # erlang distribution
    - 9999
