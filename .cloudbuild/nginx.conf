events {

}
http {
  keepalive_timeout 650;
  keepalive_requests 10000;

  map $http_upgrade $connection_upgrade {
    default Upgrade;
    '' close;
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^athena(-staging)?\.investorhub\.com(.*?)$";
    proxy_read_timeout 600;
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    location / {
      client_max_body_size 200m;

      proxy_http_version 1.1;

      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;

      proxy_redirect off;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Cluster-Client-Ip $remote_addr;
      
      proxy_pass http://127.0.0.1:4001;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^admin(-staging)?\.investorhub\.com(.*?)$";
    location / {
      proxy_http_version 1.1;

      proxy_set_header Connection $connection_upgrade;
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;

      proxy_pass http://127.0.0.1:4002;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^hermes(-staging)?\.investorhub\.com(.*?)$";
    location / {
      client_max_body_size 50m;

      proxy_http_version 1.1;

      proxy_set_header Connection $connection_upgrade;
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;
      
      
      proxy_redirect off;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Cluster-Client-Ip $remote_addr;

      proxy_pass http://127.0.0.1:4003;
    }
  }

   server {
    listen 8080;
    listen 443;
    server_name "~^public(-staging)?\.investorhub\.com(.*?)$";
    location / {
      client_max_body_size 50m;

      proxy_http_version 1.1;

      proxy_set_header Connection $connection_upgrade;
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;
      
      
      proxy_redirect off;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Cluster-Client-Ip $remote_addr;

      proxy_pass http://127.0.0.1:4006;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^tracking(-staging)?\.investorhub\.com(.*?)$";
    location / {
      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-for $remote_addr;

      proxy_pass http://127.0.0.1:4004;
    }
  }

  server {
    listen 8080;
    listen 443;
    server_name "~^heimdallr(-staging)?\.investorhub\.com(.*?)$";
    location / {
      client_max_body_size 50m;

      proxy_http_version 1.1;

      proxy_set_header Connection $connection_upgrade;
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;
      
      
      proxy_redirect off;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Cluster-Client-Ip $remote_addr;

      proxy_pass http://127.0.0.1:4007;
    }
  }

  server {
    listen 8080;
    server_name _;
    location / {
      proxy_pass http://127.0.0.1:4000;
    }
  }

  proxy_read_timeout 600;
  proxy_connect_timeout 600;
  proxy_send_timeout 600;
}
