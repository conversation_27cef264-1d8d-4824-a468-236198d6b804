---
description: 
globs: 
alwaysApply: false
---
---
description: >
  When you say "merge", automate the process of reviewing changes, branching (if on master), staging, committing, and pushing with sensible names and messages.
alwaysApply: false
manual: true
---

## Git Merge Workflow

When you say **merge**, follow these steps:

1. **Format the code**
   - Run `mix format` to ensure all code is properly formatted.

2. **Check the diff**
   - Show the current git diff to review changes.

3. **Create a new branch**
   - Create a new branch with a descriptive name based on the changes or context.

4. **Stage all changes**
   - Run `git add .` to stage all modified files.

5. **Commit with a sensible message**
   - Commit the changes with a clear, descriptive commit message summarizing the changes.

6. **Push the branch**
   - Push the branch to the remote repository.

---

### Example Shell Commands

```sh
# 1. Format code
mix format

# 2. Show diff
git diff

# 3. If on master, create a new branch
git branch  # check current branch
# if on master:
git checkout -b <sensible-branch-name>

# 4. Stage all changes
git add .

# 5. Commit with a sensible message
git commit -m "<sensible commit message>"

# 6. Push
git push -u origin <branch-name>
```

> Replace `<sensible-branch-name>` and `<sensible commit message>` with context-appropriate values. 