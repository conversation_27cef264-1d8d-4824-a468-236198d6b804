---
description: 
globs: 
alwaysApply: false
---
## Tech Stack Details

- **<PERSON><PERSON><PERSON>**: The codebase is structured as an umbrella project, with each sub-app in `apps/` having its own `lib/` and `test/` directories.
- **GraphQL**:  
  - `athena` and `hermes` expose GraphQL APIs.
- **Phoenix LiveView**:  
  - `hades` is a Phoenix LiveView app for internal admin, using Tailwind CSS and Alpine.js.
- **Frontend**:  
  - Tailwind CSS and Alpine.js are used in `hades` for styling and interactivity.
- **Other Apps**:  
  - Additional apps in `apps/` handle integrations (e.g., AWS, Google, Slack), business logic, and supporting services.

> Use this rule to inform code navigation, search, and context when working with this codebase.
