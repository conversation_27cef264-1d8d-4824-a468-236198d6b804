{"files.associations": {"*.heex": "phoenix-heex"}, "[elixir]": {"editor.defaultFormatter": "JakeBecker.elixir-ls", "editor.formatOnSave": true, "editor.tabSize": 2}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2}, "tailwindCSS.includeLanguages": {"elixir": "html", "phoenix-heex": "html"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dockerfile]": {"editor.defaultFormatter": "ms-azuretools.vscode-containers"}, "[dockercompose]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "cSpell.words": ["amazonses", "appsignal", "asic", "Automic", "boreport", "cloudex", "color", "computershare", "dmarc", "dpgettext", "esbuild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hotcopper_most_discussed_stocks", "investorhub", "priv", "rectype", "sface", "<PERSON><PERSON><PERSON><PERSON>", "unsub", "upserted", "Vercel"]}